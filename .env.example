# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_NAME=maxcoupon
DB_USER=postgres
DB_PASSWORD=password
DB_SSL_MODE=disable
DB_MAX_OPEN_CONNS=25
DB_MAX_IDLE_CONNS=5
DB_CONN_MAX_LIFETIME=300s

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0
REDIS_MAX_RETRIES=3
REDIS_POOL_SIZE=10

# Server Configuration
SERVER_HOST=localhost
SERVER_PORT=8080
SERVER_MODE=development
SERVER_READ_TIMEOUT=30s
SERVER_WRITE_TIMEOUT=30s
SERVER_IDLE_TIMEOUT=60s

# CORS Configuration
CORS_ALLOWED_ORIGINS=http://localhost:3000,http://localhost:4321
CORS_ALLOWED_METHODS=GET,POST,PUT,DELETE,OPTIONS
CORS_ALLOWED_HEADERS=Content-Type,Authorization,X-Requested-With
CORS_ALLOW_CREDENTIALS=true

# Rate Limiting
RATE_LIMIT_REQUESTS_PER_MINUTE=100
RATE_LIMIT_BURST=20

# Logging Configuration
LOG_LEVEL=info
LOG_FORMAT=json
LOG_OUTPUT=stdout

# Security
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
API_KEY_HEADER=X-API-Key
ADMIN_API_KEY=admin-api-key-change-this

# External APIs (for future integrations)
AFFILIATE_API_KEY=
BRAND_API_KEY=
PRICE_TRACKING_API_KEY=

# Email Configuration (for notifications)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=
SMTP_PASSWORD=
SMTP_FROM_EMAIL=<EMAIL>
SMTP_FROM_NAME=MaxCoupon

# File Upload Configuration
UPLOAD_MAX_SIZE=5MB
UPLOAD_ALLOWED_TYPES=jpg,jpeg,png,webp
UPLOAD_PATH=./uploads

# Cache Configuration
CACHE_DEFAULT_TTL=3600
CACHE_SEARCH_TTL=1800
CACHE_POPULAR_TTL=7200

# Background Jobs
JOBS_ENABLED=true
JOBS_INTERVAL_EXPIRED_COUPONS=1h
JOBS_INTERVAL_DEAL_REFRESH=30m
JOBS_INTERVAL_BRAND_UPDATE=6h
JOBS_INTERVAL_CACHE_WARM=15m

# Monitoring and Analytics
HEALTH_CHECK_ENABLED=true
METRICS_ENABLED=true
PROMETHEUS_PORT=9090

# Development
DEBUG=false
PROFILING_ENABLED=false
MOCK_DATA_ENABLED=false

# Production
ENVIRONMENT=development
VERSION=1.0.0
BUILD_TIME=
GIT_COMMIT=
