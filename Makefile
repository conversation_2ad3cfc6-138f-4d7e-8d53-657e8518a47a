.PHONY: help setup build start stop clean test lint format migrate seed docker-build docker-up docker-down

# Default target
help:
	@echo "MaxCoupon Development Commands"
	@echo "=============================="
	@echo "setup          - Initial project setup"
	@echo "build          - Build all components"
	@echo "start          - Start development servers"
	@echo "stop           - Stop all services"
	@echo "clean          - Clean build artifacts"
	@echo "test           - Run all tests"
	@echo "lint           - Run linters"
	@echo "format         - Format code"
	@echo "migrate-up     - Run database migrations"
	@echo "migrate-down   - Rollback database migrations"
	@echo "seed           - Seed database with sample data"
	@echo "docker-build   - Build Docker images"
	@echo "docker-up      - Start Docker services"
	@echo "docker-down    - Stop Docker services"

# Initial project setup
setup:
	@echo "Setting up MaxCoupon project..."
	@cp .env.example .env
	@echo "✓ Environment file created"
	@cd backend && go mod tidy
	@echo "✓ Go dependencies installed"
	@cd frontend && npm install
	@echo "✓ Node.js dependencies installed"
	@echo "✓ Setup complete! Edit .env file with your configuration"

# Build all components
build:
	@echo "Building backend..."
	@cd backend && go build -o bin/server cmd/server/main.go
	@echo "Building frontend..."
	@cd frontend && npm run build
	@echo "✓ Build complete"

# Start development servers
start:
	@echo "Starting development servers..."
	@docker-compose up -d postgres redis
	@echo "Waiting for database to be ready..."
	@sleep 5
	@cd backend && go run cmd/server/main.go &
	@cd frontend && npm run dev &
	@echo "✓ Development servers started"
	@echo "Frontend: http://localhost:4321"
	@echo "Backend: http://localhost:8080"

# Stop all services
stop:
	@echo "Stopping services..."
	@pkill -f "go run cmd/server/main.go" || true
	@pkill -f "npm run dev" || true
	@docker-compose down
	@echo "✓ Services stopped"

# Clean build artifacts
clean:
	@echo "Cleaning build artifacts..."
	@cd backend && rm -rf bin/
	@cd frontend && rm -rf dist/ .astro/
	@docker-compose down -v
	@docker system prune -f
	@echo "✓ Clean complete"

# Run all tests
test:
	@echo "Running backend tests..."
	@cd backend && go test ./...
	@echo "Running frontend tests..."
	@cd frontend && npm run test
	@echo "✓ Tests complete"

# Run linters
lint:
	@echo "Running backend linter..."
	@cd backend && golangci-lint run
	@echo "Running frontend linter..."
	@cd frontend && npm run lint
	@echo "✓ Linting complete"

# Format code
format:
	@echo "Formatting backend code..."
	@cd backend && go fmt ./...
	@echo "Formatting frontend code..."
	@cd frontend && npm run format
	@echo "✓ Formatting complete"

# Database migrations
migrate-up:
	@echo "Running database migrations..."
	@cd backend && go run cmd/migrate/main.go up
	@echo "✓ Migrations complete"

migrate-down:
	@echo "Rolling back database migrations..."
	@cd backend && go run cmd/migrate/main.go down
	@echo "✓ Rollback complete"

# Seed database
seed:
	@echo "Seeding database with sample data..."
	@cd backend && go run cmd/seed/main.go
	@echo "✓ Database seeded"

# Docker commands
docker-build:
	@echo "Building Docker images..."
	@docker-compose build
	@echo "✓ Docker images built"

docker-up:
	@echo "Starting Docker services..."
	@docker-compose up -d
	@echo "✓ Docker services started"

docker-down:
	@echo "Stopping Docker services..."
	@docker-compose down
	@echo "✓ Docker services stopped"

# Production deployment
deploy-prod:
	@echo "Deploying to production..."
	@docker-compose -f docker-compose.prod.yml up -d
	@echo "✓ Production deployment complete"

# Database backup
backup-db:
	@echo "Creating database backup..."
	@docker exec maxcoupon_postgres pg_dump -U postgres maxcoupon > backup_$(shell date +%Y%m%d_%H%M%S).sql
	@echo "✓ Database backup created"

# Restore database
restore-db:
	@echo "Restoring database from backup..."
	@docker exec -i maxcoupon_postgres psql -U postgres maxcoupon < $(BACKUP_FILE)
	@echo "✓ Database restored"

# Monitor logs
logs:
	@docker-compose logs -f

# Health check
health:
	@echo "Checking service health..."
	@curl -f http://localhost:8080/health || echo "Backend: DOWN"
	@curl -f http://localhost:4321 || echo "Frontend: DOWN"
	@docker exec maxcoupon_postgres pg_isready -U postgres || echo "Database: DOWN"
	@docker exec maxcoupon_redis redis-cli ping || echo "Redis: DOWN"
