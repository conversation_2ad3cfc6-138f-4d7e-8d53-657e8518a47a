-- MaxCoupon Database Initialization Script
-- This script sets up the database with proper extensions and configurations

-- Enable required PostgreSQL extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm"; -- For fuzzy text search
CREATE EXTENSION IF NOT EXISTS "unaccent"; -- For accent-insensitive search

-- Create custom text search configuration for better search
CREATE TEXT SEARCH CONFIGURATION IF NOT EXISTS maxcoupon_search (COPY = english);

-- Create function for better search ranking
CREATE OR REPLACE FUNCTION search_rank(
    search_vector tsvector,
    query tsquery,
    title_weight FLOAT DEFAULT 1.0,
    description_weight FLOAT DEFAULT 0.5
) RETURNS FLOAT AS $$
BEGIN
    RETURN ts_rank_cd(search_vector, query) * title_weight + 
           ts_rank(search_vector, query) * description_weight;
END;
$$ LANGUAGE plpgsql IMMUTABLE;

-- Create function for fuzzy search
CREATE OR REPLACE FUNCTION fuzzy_search(
    search_text TEXT,
    target_text TEXT,
    threshold FLOAT DEFAULT 0.3
) R<PERSON>URNS BOOLEAN AS $$
BEGIN
    RETURN similarity(search_text, target_text) > threshold;
END;
$$ LANGUAGE plpgsql IMMUTABLE;

-- Create function to clean and normalize search queries
CREATE OR REPLACE FUNCTION normalize_search_query(query TEXT)
RETURNS TEXT AS $$
BEGIN
    -- Remove special characters, normalize spaces, convert to lowercase
    RETURN TRIM(REGEXP_REPLACE(LOWER(unaccent(query)), '[^a-z0-9\s]', ' ', 'g'));
END;
$$ LANGUAGE plpgsql IMMUTABLE;

-- Create materialized view for search optimization
CREATE MATERIALIZED VIEW IF NOT EXISTS search_index AS
SELECT 
    'coupon' as entity_type,
    c.id as entity_id,
    c.title,
    c.description,
    b.name as brand_name,
    cat.name as category_name,
    co.name as country_name,
    c.status,
    c.expiry_date,
    c.popularity_score,
    c.click_count,
    c.created_at,
    to_tsvector('maxcoupon_search', 
        COALESCE(c.title, '') || ' ' || 
        COALESCE(c.description, '') || ' ' || 
        COALESCE(b.name, '') || ' ' || 
        COALESCE(cat.name, '')
    ) as search_vector
FROM coupons c
LEFT JOIN brands b ON c.brand_id = b.id
LEFT JOIN categories cat ON c.category_id = cat.id
LEFT JOIN countries co ON c.country_id = co.id
WHERE c.status = 'active' AND c.expiry_date > CURRENT_TIMESTAMP

UNION ALL

SELECT 
    'deal' as entity_type,
    d.id as entity_id,
    d.title,
    d.description,
    b.name as brand_name,
    cat.name as category_name,
    co.name as country_name,
    d.status,
    d.end_date as expiry_date,
    d.popularity_score,
    d.click_count,
    d.created_at,
    to_tsvector('maxcoupon_search', 
        COALESCE(d.title, '') || ' ' || 
        COALESCE(d.description, '') || ' ' || 
        COALESCE(b.name, '') || ' ' || 
        COALESCE(cat.name, '')
    ) as search_vector
FROM deals d
LEFT JOIN brands b ON d.brand_id = b.id
LEFT JOIN categories cat ON d.category_id = cat.id
LEFT JOIN countries co ON d.country_id = co.id
WHERE d.status = 'active' AND (d.end_date IS NULL OR d.end_date > CURRENT_TIMESTAMP)

UNION ALL

SELECT 
    'brand' as entity_type,
    b.id as entity_id,
    b.name as title,
    b.description,
    b.name as brand_name,
    NULL as category_name,
    co.name as country_name,
    b.status,
    NULL as expiry_date,
    b.popularity_score,
    0 as click_count,
    b.created_at,
    to_tsvector('maxcoupon_search', 
        COALESCE(b.name, '') || ' ' || 
        COALESCE(b.description, '') || ' ' || 
        COALESCE(array_to_string(b.tags, ' '), '')
    ) as search_vector
FROM brands b
LEFT JOIN countries co ON b.headquarters_country_id = co.id
WHERE b.status = 'active'

UNION ALL

SELECT 
    'category' as entity_type,
    cat.id as entity_id,
    cat.name as title,
    cat.description,
    NULL as brand_name,
    cat.name as category_name,
    NULL as country_name,
    cat.status,
    NULL as expiry_date,
    0 as popularity_score,
    0 as click_count,
    cat.created_at,
    to_tsvector('maxcoupon_search', 
        COALESCE(cat.name, '') || ' ' || 
        COALESCE(cat.description, '')
    ) as search_vector
FROM categories cat
WHERE cat.status = 'active';

-- Create indexes on the materialized view
CREATE INDEX IF NOT EXISTS idx_search_index_vector ON search_index USING gin(search_vector);
CREATE INDEX IF NOT EXISTS idx_search_index_entity ON search_index(entity_type, entity_id);
CREATE INDEX IF NOT EXISTS idx_search_index_popularity ON search_index(popularity_score DESC);

-- Function to refresh search index
CREATE OR REPLACE FUNCTION refresh_search_index()
RETURNS VOID AS $$
BEGIN
    REFRESH MATERIALIZED VIEW CONCURRENTLY search_index;
END;
$$ LANGUAGE plpgsql;

-- Create function for comprehensive search
CREATE OR REPLACE FUNCTION search_all(
    search_query TEXT,
    entity_types TEXT[] DEFAULT ARRAY['coupon', 'deal', 'brand', 'category'],
    country_filter VARCHAR(3) DEFAULT NULL,
    limit_results INTEGER DEFAULT 50
)
RETURNS TABLE(
    entity_type TEXT,
    entity_id INTEGER,
    title TEXT,
    description TEXT,
    brand_name TEXT,
    category_name TEXT,
    country_name TEXT,
    relevance_score FLOAT
) AS $$
DECLARE
    query_tsquery tsquery;
    normalized_query TEXT;
BEGIN
    -- Normalize and prepare the search query
    normalized_query := normalize_search_query(search_query);
    query_tsquery := plainto_tsquery('maxcoupon_search', normalized_query);
    
    RETURN QUERY
    SELECT 
        si.entity_type,
        si.entity_id,
        si.title,
        si.description,
        si.brand_name,
        si.category_name,
        si.country_name,
        search_rank(si.search_vector, query_tsquery) as relevance_score
    FROM search_index si
    WHERE 
        si.entity_type = ANY(entity_types)
        AND (country_filter IS NULL OR si.country_name = (SELECT name FROM countries WHERE code = country_filter))
        AND (
            si.search_vector @@ query_tsquery
            OR fuzzy_search(normalized_query, LOWER(si.title))
            OR fuzzy_search(normalized_query, LOWER(si.brand_name))
        )
    ORDER BY relevance_score DESC, si.popularity_score DESC, si.click_count DESC
    LIMIT limit_results;
END;
$$ LANGUAGE plpgsql;

-- Set up database configuration for better performance
ALTER SYSTEM SET shared_preload_libraries = 'pg_stat_statements';
ALTER SYSTEM SET track_activity_query_size = 2048;
ALTER SYSTEM SET log_min_duration_statement = 1000; -- Log slow queries
ALTER SYSTEM SET log_checkpoints = on;
ALTER SYSTEM SET log_connections = on;
ALTER SYSTEM SET log_disconnections = on;

-- Create database roles for different access levels
DO $$
BEGIN
    -- Read-only role for analytics and reporting
    IF NOT EXISTS (SELECT FROM pg_catalog.pg_roles WHERE rolname = 'maxcoupon_readonly') THEN
        CREATE ROLE maxcoupon_readonly;
        GRANT CONNECT ON DATABASE maxcoupon TO maxcoupon_readonly;
        GRANT USAGE ON SCHEMA public TO maxcoupon_readonly;
        GRANT SELECT ON ALL TABLES IN SCHEMA public TO maxcoupon_readonly;
        ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT SELECT ON TABLES TO maxcoupon_readonly;
    END IF;
    
    -- Application role with read/write access
    IF NOT EXISTS (SELECT FROM pg_catalog.pg_roles WHERE rolname = 'maxcoupon_app') THEN
        CREATE ROLE maxcoupon_app;
        GRANT CONNECT ON DATABASE maxcoupon TO maxcoupon_app;
        GRANT USAGE ON SCHEMA public TO maxcoupon_app;
        GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA public TO maxcoupon_app;
        GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA public TO maxcoupon_app;
        ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT SELECT, INSERT, UPDATE, DELETE ON TABLES TO maxcoupon_app;
        ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT USAGE, SELECT ON SEQUENCES TO maxcoupon_app;
    END IF;
END
$$;
