-- Sample data for MaxCoupon database
-- This file contains realistic sample data for development and testing

-- Sample coupons data
INSERT INTO coupons (
    title, description, code, discount_type, discount_value, minimum_order_amount,
    brand_id, category_id, country_id, expiry_date, is_featured, is_exclusive,
    affiliate_url, terms_and_conditions
) VALUES
-- Amazon coupons
('Amazon Prime Day Special - 20% Off Electronics', 'Get 20% off on all electronics during Prime Day sale', 'PRIME20', 'percentage', 20.00, 50.00,
 (SELECT id FROM brands WHERE name = 'Amazon'), 
 (SELECT id FROM categories WHERE slug = 'electronics-tech'),
 (SELECT id FROM countries WHERE code = 'US'),
 CURRENT_TIMESTAMP + INTERVAL '30 days', true, false,
 'https://amazon.com/deals/electronics?tag=maxcoupon',
 'Valid for Prime members only. Cannot be combined with other offers.'),

('Amazon Fashion Week - 15% Off Clothing', 'Save 15% on fashion and clothing items', 'FASHION15', 'percentage', 15.00, 25.00,
 (SELECT id FROM brands WHERE name = 'Amazon'),
 (SELECT id FROM categories WHERE slug = 'fashion-clothing'),
 (SELECT id FROM countries WHERE code = 'US'),
 CURRENT_TIMESTAMP + INTERVAL '14 days', false, true,
 'https://amazon.com/fashion?tag=maxcoupon',
 'Valid on selected fashion items. Minimum purchase required.'),

-- Nike coupons
('Nike Summer Sale - 25% Off Athletic Wear', 'Get 25% off on all athletic wear and footwear', 'SUMMER25', 'percentage', 25.00, 75.00,
 (SELECT id FROM brands WHERE name = 'Nike'),
 (SELECT id FROM categories WHERE slug = 'sports-outdoors'),
 (SELECT id FROM countries WHERE code = 'US'),
 CURRENT_TIMESTAMP + INTERVAL '21 days', true, false,
 'https://nike.com/sale?tag=maxcoupon',
 'Valid on regular priced items only. Excludes limited editions.'),

('Nike Free Shipping', 'Free shipping on all orders over $50', NULL, 'free_shipping', 0.00, 50.00,
 (SELECT id FROM brands WHERE name = 'Nike'),
 (SELECT id FROM categories WHERE slug = 'sports-outdoors'),
 (SELECT id FROM countries WHERE code = 'US'),
 CURRENT_TIMESTAMP + INTERVAL '60 days', false, false,
 'https://nike.com?tag=maxcoupon',
 'Free standard shipping. Express shipping not included.'),

-- H&M coupons
('H&M Student Discount - 10% Off', 'Students get 10% off on all purchases', 'STUDENT10', 'percentage', 10.00, 0.00,
 (SELECT id FROM brands WHERE name = 'H&M'),
 (SELECT id FROM categories WHERE slug = 'fashion-clothing'),
 (SELECT id FROM countries WHERE code = 'SE'),
 CURRENT_TIMESTAMP + INTERVAL '90 days', false, false,
 'https://hm.com/student?tag=maxcoupon',
 'Valid student ID required. Cannot be combined with sale items.'),

('H&M New Collection - $10 Off $50', 'Get $10 off when you spend $50 or more on new collection', 'NEW10', 'fixed_amount', 10.00, 50.00,
 (SELECT id FROM brands WHERE name = 'H&M'),
 (SELECT id FROM categories WHERE slug = 'fashion-clothing'),
 (SELECT id FROM countries WHERE code = 'SE'),
 CURRENT_TIMESTAMP + INTERVAL '15 days', true, true,
 'https://hm.com/new?tag=maxcoupon',
 'Valid on new collection items only. One use per customer.'),

-- IKEA coupons
('IKEA Kitchen Sale - 20% Off Kitchen Items', 'Save 20% on all kitchen furniture and accessories', 'KITCHEN20', 'percentage', 20.00, 100.00,
 (SELECT id FROM brands WHERE name = 'IKEA'),
 (SELECT id FROM categories WHERE slug = 'kitchen-dining'),
 (SELECT id FROM countries WHERE code = 'SE'),
 CURRENT_TIMESTAMP + INTERVAL '45 days', true, false,
 'https://ikea.com/kitchen?tag=maxcoupon',
 'Valid on kitchen items only. Assembly service not included.'),

-- Best Buy coupons
('Best Buy Gaming Week - 15% Off Gaming', 'Get 15% off on gaming consoles, games, and accessories', 'GAMING15', 'percentage', 15.00, 100.00,
 (SELECT id FROM brands WHERE name = 'Best Buy'),
 (SELECT id FROM categories WHERE slug = 'gaming'),
 (SELECT id FROM countries WHERE code = 'US'),
 CURRENT_TIMESTAMP + INTERVAL '7 days', true, false,
 'https://bestbuy.com/gaming?tag=maxcoupon',
 'Valid on gaming products only. Limited time offer.'),

-- Adidas coupons
('Adidas Originals - 30% Off', 'Get 30% off on Adidas Originals collection', 'ORIGINALS30', 'percentage', 30.00, 80.00,
 (SELECT id FROM brands WHERE name = 'Adidas'),
 (SELECT id FROM categories WHERE slug = 'sports-outdoors'),
 (SELECT id FROM countries WHERE code = 'DE'),
 CURRENT_TIMESTAMP + INTERVAL '20 days', true, true,
 'https://adidas.com/originals?tag=maxcoupon',
 'Valid on Originals collection only. Limited sizes available.'),

-- ASOS coupons
('ASOS First Order - 20% Off', 'New customers get 20% off their first order', 'WELCOME20', 'percentage', 20.00, 30.00,
 (SELECT id FROM brands WHERE name = 'ASOS'),
 (SELECT id FROM categories WHERE slug = 'fashion-clothing'),
 (SELECT id FROM countries WHERE code = 'GB'),
 CURRENT_TIMESTAMP + INTERVAL '365 days', false, true,
 'https://asos.com/welcome?tag=maxcoupon',
 'Valid for new customers only. One use per customer.');

-- Sample deals data
INSERT INTO deals (
    title, description, original_price, sale_price, currency,
    brand_id, category_id, country_id, end_date, is_featured, is_hot_deal,
    product_url, image_url, deal_type, availability
) VALUES
-- Amazon deals
('Apple iPhone 15 Pro - Limited Time Deal', 'Latest iPhone 15 Pro with advanced camera system and A17 Pro chip', 999.00, 849.00, 'USD',
 (SELECT id FROM brands WHERE name = 'Amazon'),
 (SELECT id FROM categories WHERE slug = 'smartphones-tablets'),
 (SELECT id FROM countries WHERE code = 'US'),
 CURRENT_TIMESTAMP + INTERVAL '3 days', true, true,
 'https://amazon.com/iphone-15-pro?tag=maxcoupon',
 'https://images.amazon.com/iphone-15-pro.jpg',
 'flash_sale', 'limited_stock'),

('Samsung 65" 4K Smart TV', 'Crystal UHD 4K Smart TV with HDR and built-in streaming apps', 799.00, 599.00, 'USD',
 (SELECT id FROM brands WHERE name = 'Amazon'),
 (SELECT id FROM categories WHERE slug = 'audio-video'),
 (SELECT id FROM countries WHERE code = 'US'),
 CURRENT_TIMESTAMP + INTERVAL '7 days', true, false,
 'https://amazon.com/samsung-tv?tag=maxcoupon',
 'https://images.amazon.com/samsung-tv.jpg',
 'sale', 'in_stock'),

-- Nike deals
('Nike Air Max 270 - Flash Sale', 'Popular Nike Air Max 270 sneakers in multiple colors', 150.00, 89.99, 'USD',
 (SELECT id FROM brands WHERE name = 'Nike'),
 (SELECT id FROM categories WHERE slug = 'shoes-footwear'),
 (SELECT id FROM countries WHERE code = 'US'),
 CURRENT_TIMESTAMP + INTERVAL '2 days', true, true,
 'https://nike.com/air-max-270?tag=maxcoupon',
 'https://images.nike.com/air-max-270.jpg',
 'flash_sale', 'limited_stock'),

-- H&M deals
('H&M Summer Dress Collection', 'Trendy summer dresses perfect for the season', 49.99, 24.99, 'EUR',
 (SELECT id FROM brands WHERE name = 'H&M'),
 (SELECT id FROM categories WHERE slug = 'womens-clothing'),
 (SELECT id FROM countries WHERE code = 'SE'),
 CURRENT_TIMESTAMP + INTERVAL '14 days', false, false,
 'https://hm.com/summer-dresses?tag=maxcoupon',
 'https://images.hm.com/summer-dress.jpg',
 'seasonal', 'in_stock'),

-- IKEA deals
('IKEA MALM Bed Frame - Clearance', 'Popular MALM bed frame in white, queen size', 199.00, 149.00, 'EUR',
 (SELECT id FROM brands WHERE name = 'IKEA'),
 (SELECT id FROM categories WHERE slug = 'furniture'),
 (SELECT id FROM countries WHERE code = 'SE'),
 CURRENT_TIMESTAMP + INTERVAL '30 days', false, false,
 'https://ikea.com/malm-bed?tag=maxcoupon',
 'https://images.ikea.com/malm-bed.jpg',
 'clearance', 'in_stock'),

-- Best Buy deals
('PlayStation 5 Console Bundle', 'PS5 console with extra controller and popular game', 599.99, 549.99, 'USD',
 (SELECT id FROM brands WHERE name = 'Best Buy'),
 (SELECT id FROM categories WHERE slug = 'gaming'),
 (SELECT id FROM countries WHERE code = 'US'),
 CURRENT_TIMESTAMP + INTERVAL '5 days', true, true,
 'https://bestbuy.com/ps5-bundle?tag=maxcoupon',
 'https://images.bestbuy.com/ps5-bundle.jpg',
 'bundle', 'limited_stock'),

-- Adidas deals
('Adidas Ultraboost 22 Running Shoes', 'Premium running shoes with Boost technology', 180.00, 126.00, 'EUR',
 (SELECT id FROM brands WHERE name = 'Adidas'),
 (SELECT id FROM categories WHERE slug = 'shoes-footwear'),
 (SELECT id FROM countries WHERE code = 'DE'),
 CURRENT_TIMESTAMP + INTERVAL '10 days', true, false,
 'https://adidas.com/ultraboost?tag=maxcoupon',
 'https://images.adidas.com/ultraboost.jpg',
 'sale', 'in_stock');

-- Update brand statistics
UPDATE brands SET 
    total_coupons = (SELECT COUNT(*) FROM coupons WHERE brand_id = brands.id AND status = 'active'),
    total_deals = (SELECT COUNT(*) FROM deals WHERE brand_id = brands.id AND status = 'active'),
    last_updated = CURRENT_TIMESTAMP;

-- Generate some sample analytics data
INSERT INTO click_analytics (entity_type, entity_id, user_ip, country_code, device_type, clicked_at)
SELECT 
    'coupon',
    c.id,
    ('192.168.1.' || (RANDOM() * 254 + 1)::INTEGER)::INET,
    co.code,
    CASE (RANDOM() * 3)::INTEGER
        WHEN 0 THEN 'mobile'
        WHEN 1 THEN 'tablet'
        ELSE 'desktop'
    END,
    CURRENT_TIMESTAMP - (RANDOM() * INTERVAL '30 days')
FROM coupons c
JOIN countries co ON c.country_id = co.id
CROSS JOIN generate_series(1, 5); -- Generate 5 clicks per coupon

-- Generate sample search analytics
INSERT INTO search_analytics (query, results_count, country_code, device_type, searched_at)
VALUES
('nike shoes', 15, 'US', 'mobile', CURRENT_TIMESTAMP - INTERVAL '1 hour'),
('amazon deals', 25, 'US', 'desktop', CURRENT_TIMESTAMP - INTERVAL '2 hours'),
('h&m clothing', 12, 'SE', 'mobile', CURRENT_TIMESTAMP - INTERVAL '3 hours'),
('ikea furniture', 8, 'SE', 'tablet', CURRENT_TIMESTAMP - INTERVAL '4 hours'),
('gaming console', 6, 'US', 'desktop', CURRENT_TIMESTAMP - INTERVAL '5 hours'),
('summer dress', 18, 'GB', 'mobile', CURRENT_TIMESTAMP - INTERVAL '6 hours'),
('adidas sneakers', 22, 'DE', 'desktop', CURRENT_TIMESTAMP - INTERVAL '7 hours'),
('electronics sale', 30, 'US', 'mobile', CURRENT_TIMESTAMP - INTERVAL '8 hours');
