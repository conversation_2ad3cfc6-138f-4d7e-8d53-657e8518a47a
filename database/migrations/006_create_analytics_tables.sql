-- Create analytics tables for tracking user interactions and site performance

-- Click tracking table
CREATE TABLE IF NOT EXISTS click_analytics (
    id SERIAL PRIMARY KEY,
    entity_type VARCHAR(20) NOT NULL CHECK (entity_type IN ('coupon', 'deal', 'brand')),
    entity_id INTEGER NOT NULL,
    user_ip INET,
    user_agent TEXT,
    referrer_url TEXT,
    country_code VARCHAR(3),
    device_type VARCHAR(20), -- mobile, tablet, desktop
    browser VARCHAR(50),
    os VARCHAR(50),
    clicked_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for analytics
CREATE INDEX idx_click_analytics_entity ON click_analytics(entity_type, entity_id);
CREATE INDEX idx_click_analytics_clicked_at ON click_analytics(clicked_at);
CREATE INDEX idx_click_analytics_country_code ON click_analytics(country_code);
CREATE INDEX idx_click_analytics_device_type ON click_analytics(device_type);

-- Search analytics table
CREATE TABLE IF NOT EXISTS search_analytics (
    id SERIAL PRIMARY KEY,
    query TEXT NOT NULL,
    results_count INTEGER DEFAULT 0,
    user_ip INET,
    user_agent TEXT,
    country_code VARCHAR(3),
    device_type VARCHAR(20),
    search_filters JSONB, -- Store applied filters
    clicked_result_id INTEGER, -- Which result was clicked (if any)
    clicked_result_type VARCHAR(20), -- coupon, deal, brand, category
    searched_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for search analytics
CREATE INDEX idx_search_analytics_query ON search_analytics(query);
CREATE INDEX idx_search_analytics_searched_at ON search_analytics(searched_at);
CREATE INDEX idx_search_analytics_country_code ON search_analytics(country_code);
CREATE INDEX idx_search_analytics_results_count ON search_analytics(results_count);

-- Popular searches view
CREATE VIEW popular_searches AS
SELECT 
    query,
    COUNT(*) as search_count,
    AVG(results_count) as avg_results,
    COUNT(clicked_result_id) as click_count,
    ROUND((COUNT(clicked_result_id)::DECIMAL / COUNT(*)) * 100, 2) as click_through_rate
FROM search_analytics 
WHERE searched_at >= CURRENT_DATE - INTERVAL '30 days'
GROUP BY query
HAVING COUNT(*) >= 5
ORDER BY search_count DESC;

-- Daily statistics table
CREATE TABLE IF NOT EXISTS daily_stats (
    id SERIAL PRIMARY KEY,
    stat_date DATE NOT NULL UNIQUE,
    total_coupons INTEGER DEFAULT 0,
    active_coupons INTEGER DEFAULT 0,
    expired_coupons INTEGER DEFAULT 0,
    total_deals INTEGER DEFAULT 0,
    active_deals INTEGER DEFAULT 0,
    expired_deals INTEGER DEFAULT 0,
    total_clicks INTEGER DEFAULT 0,
    total_searches INTEGER DEFAULT 0,
    unique_visitors INTEGER DEFAULT 0,
    top_brand_id INTEGER,
    top_category_id INTEGER,
    top_country_code VARCHAR(3),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create index for daily stats
CREATE INDEX idx_daily_stats_date ON daily_stats(stat_date);

-- Function to generate daily statistics
CREATE OR REPLACE FUNCTION generate_daily_stats(target_date DATE DEFAULT CURRENT_DATE)
RETURNS VOID AS $$
DECLARE
    stats_record daily_stats%ROWTYPE;
BEGIN
    -- Calculate statistics for the target date
    SELECT 
        target_date,
        (SELECT COUNT(*) FROM coupons WHERE DATE(created_at) <= target_date),
        (SELECT COUNT(*) FROM coupons WHERE status = 'active' AND DATE(created_at) <= target_date AND expiry_date > target_date),
        (SELECT COUNT(*) FROM coupons WHERE status = 'expired' AND DATE(created_at) <= target_date),
        (SELECT COUNT(*) FROM deals WHERE DATE(created_at) <= target_date),
        (SELECT COUNT(*) FROM deals WHERE status = 'active' AND DATE(created_at) <= target_date AND (end_date IS NULL OR end_date > target_date)),
        (SELECT COUNT(*) FROM deals WHERE status = 'expired' AND DATE(created_at) <= target_date),
        (SELECT COUNT(*) FROM click_analytics WHERE DATE(clicked_at) = target_date),
        (SELECT COUNT(*) FROM search_analytics WHERE DATE(searched_at) = target_date),
        (SELECT COUNT(DISTINCT user_ip) FROM click_analytics WHERE DATE(clicked_at) = target_date),
        (SELECT entity_id FROM click_analytics WHERE DATE(clicked_at) = target_date AND entity_type = 'brand' GROUP BY entity_id ORDER BY COUNT(*) DESC LIMIT 1),
        (SELECT c.category_id FROM click_analytics ca JOIN coupons c ON ca.entity_id = c.id WHERE DATE(ca.clicked_at) = target_date AND ca.entity_type = 'coupon' GROUP BY c.category_id ORDER BY COUNT(*) DESC LIMIT 1),
        (SELECT country_code FROM click_analytics WHERE DATE(clicked_at) = target_date GROUP BY country_code ORDER BY COUNT(*) DESC LIMIT 1)
    INTO stats_record;

    -- Insert or update the daily stats
    INSERT INTO daily_stats (
        stat_date, total_coupons, active_coupons, expired_coupons,
        total_deals, active_deals, expired_deals, total_clicks,
        total_searches, unique_visitors, top_brand_id, top_category_id, top_country_code
    ) VALUES (
        stats_record.stat_date, stats_record.total_coupons, stats_record.active_coupons, stats_record.expired_coupons,
        stats_record.total_deals, stats_record.active_deals, stats_record.expired_deals, stats_record.total_clicks,
        stats_record.total_searches, stats_record.unique_visitors, stats_record.top_brand_id, stats_record.top_category_id, stats_record.top_country_code
    )
    ON CONFLICT (stat_date) DO UPDATE SET
        total_coupons = EXCLUDED.total_coupons,
        active_coupons = EXCLUDED.active_coupons,
        expired_coupons = EXCLUDED.expired_coupons,
        total_deals = EXCLUDED.total_deals,
        active_deals = EXCLUDED.active_deals,
        expired_deals = EXCLUDED.expired_deals,
        total_clicks = EXCLUDED.total_clicks,
        total_searches = EXCLUDED.total_searches,
        unique_visitors = EXCLUDED.unique_visitors,
        top_brand_id = EXCLUDED.top_brand_id,
        top_category_id = EXCLUDED.top_category_id,
        top_country_code = EXCLUDED.top_country_code;
END;
$$ LANGUAGE plpgsql;

-- Function to track clicks
CREATE OR REPLACE FUNCTION track_click(
    p_entity_type VARCHAR(20),
    p_entity_id INTEGER,
    p_user_ip INET DEFAULT NULL,
    p_user_agent TEXT DEFAULT NULL,
    p_referrer_url TEXT DEFAULT NULL,
    p_country_code VARCHAR(3) DEFAULT NULL,
    p_device_type VARCHAR(20) DEFAULT NULL,
    p_browser VARCHAR(50) DEFAULT NULL,
    p_os VARCHAR(50) DEFAULT NULL
)
RETURNS VOID AS $$
BEGIN
    -- Insert click record
    INSERT INTO click_analytics (
        entity_type, entity_id, user_ip, user_agent, referrer_url,
        country_code, device_type, browser, os
    ) VALUES (
        p_entity_type, p_entity_id, p_user_ip, p_user_agent, p_referrer_url,
        p_country_code, p_device_type, p_browser, p_os
    );
    
    -- Update entity click count
    IF p_entity_type = 'coupon' THEN
        UPDATE coupons SET click_count = click_count + 1 WHERE id = p_entity_id;
    ELSIF p_entity_type = 'deal' THEN
        UPDATE deals SET click_count = click_count + 1 WHERE id = p_entity_id;
    END IF;
END;
$$ LANGUAGE plpgsql;

-- Function to track searches
CREATE OR REPLACE FUNCTION track_search(
    p_query TEXT,
    p_results_count INTEGER DEFAULT 0,
    p_user_ip INET DEFAULT NULL,
    p_user_agent TEXT DEFAULT NULL,
    p_country_code VARCHAR(3) DEFAULT NULL,
    p_device_type VARCHAR(20) DEFAULT NULL,
    p_search_filters JSONB DEFAULT NULL,
    p_clicked_result_id INTEGER DEFAULT NULL,
    p_clicked_result_type VARCHAR(20) DEFAULT NULL
)
RETURNS VOID AS $$
BEGIN
    INSERT INTO search_analytics (
        query, results_count, user_ip, user_agent, country_code,
        device_type, search_filters, clicked_result_id, clicked_result_type
    ) VALUES (
        p_query, p_results_count, p_user_ip, p_user_agent, p_country_code,
        p_device_type, p_search_filters, p_clicked_result_id, p_clicked_result_type
    );
END;
$$ LANGUAGE plpgsql;
