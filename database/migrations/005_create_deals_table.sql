-- Create deals table
CREATE TABLE IF NOT EXISTS deals (
    id SERIAL PRIMARY KEY,
    title VARCHAR(200) NOT NULL,
    description TEXT,
    original_price DECIMAL(10,2), -- Original price before discount
    sale_price DECIMAL(10,2), -- Current sale price
    discount_percentage DECIMAL(5,2), -- Calculated discount percentage
    currency VARCHAR(3) DEFAULT 'USD', -- Currency for prices
    
    -- Product details
    product_url TEXT, -- Direct link to product
    affiliate_url TEXT, -- Affiliate tracking link
    image_url VARCHAR(500),
    product_sku VARCHAR(100), -- Stock keeping unit
    availability VARCHAR(50) DEFAULT 'in_stock', -- in_stock, out_of_stock, limited_stock
    stock_quantity INTEGER, -- Available quantity (if known)
    
    -- Relationships
    brand_id INTEGER NOT NULL REFERENCES brands(id) ON DELETE CASCADE,
    category_id INTEGER REFERENCES categories(id) ON DELETE SET NULL,
    country_id INTEGER REFERENCES countries(id) ON DELETE SET NULL,
    
    -- Dates and timing
    start_date TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    end_date TIMESTAMP WITH TIME ZONE, -- NULL for ongoing deals
    
    -- Deal metadata
    is_featured BOOLEAN DEFAULT FALSE,
    is_hot_deal BOOLEAN DEFAULT FALSE, -- Trending/hot deals
    is_limited_time BOOLEAN DEFAULT FALSE,
    is_flash_sale BOOLEAN DEFAULT FALSE,
    deal_type VARCHAR(30) DEFAULT 'sale' CHECK (deal_type IN ('sale', 'clearance', 'flash_sale', 'daily_deal', 'bundle', 'seasonal')),
    
    -- Engagement metrics
    popularity_score INTEGER DEFAULT 0,
    click_count INTEGER DEFAULT 0,
    view_count INTEGER DEFAULT 0,
    like_count INTEGER DEFAULT 0,
    share_count INTEGER DEFAULT 0,
    
    -- Quality and verification
    is_verified BOOLEAN DEFAULT FALSE,
    last_price_check TIMESTAMP WITH TIME ZONE,
    price_history JSONB, -- Store price changes over time
    
    -- SEO and display
    meta_title VARCHAR(200),
    meta_description TEXT,
    tags TEXT[], -- Array of tags for better categorization
    
    -- Terms and conditions
    terms_and_conditions TEXT,
    shipping_info TEXT,
    return_policy TEXT,
    
    -- Status and timestamps
    status VARCHAR(20) NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'expired', 'pending', 'out_of_stock')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for better performance
CREATE INDEX idx_deals_brand_id ON deals(brand_id);
CREATE INDEX idx_deals_category_id ON deals(category_id);
CREATE INDEX idx_deals_country_id ON deals(country_id);
CREATE INDEX idx_deals_status ON deals(status);
CREATE INDEX idx_deals_end_date ON deals(end_date);
CREATE INDEX idx_deals_start_date ON deals(start_date);
CREATE INDEX idx_deals_deal_type ON deals(deal_type);
CREATE INDEX idx_deals_is_featured ON deals(is_featured);
CREATE INDEX idx_deals_is_hot_deal ON deals(is_hot_deal);
CREATE INDEX idx_deals_discount_percentage ON deals(discount_percentage DESC);
CREATE INDEX idx_deals_popularity_score ON deals(popularity_score DESC);
CREATE INDEX idx_deals_click_count ON deals(click_count DESC);
CREATE INDEX idx_deals_created_at ON deals(created_at DESC);
CREATE INDEX idx_deals_availability ON deals(availability);
CREATE INDEX idx_deals_tags ON deals USING GIN(tags);

-- Composite indexes for common queries
CREATE INDEX idx_deals_active_available ON deals(status, availability, end_date);
CREATE INDEX idx_deals_brand_active ON deals(brand_id, status, end_date);
CREATE INDEX idx_deals_category_active ON deals(category_id, status, end_date);
CREATE INDEX idx_deals_country_active ON deals(country_id, status, end_date);

-- Full-text search index
CREATE INDEX idx_deals_search ON deals USING gin(to_tsvector('english', title || ' ' || COALESCE(description, '')));

-- Create trigger to update updated_at timestamp
CREATE TRIGGER update_deals_updated_at 
    BEFORE UPDATE ON deals 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Create trigger to automatically calculate discount percentage
CREATE OR REPLACE FUNCTION calculate_discount_percentage()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.original_price IS NOT NULL AND NEW.sale_price IS NOT NULL AND NEW.original_price > 0 THEN
        NEW.discount_percentage := ROUND(((NEW.original_price - NEW.sale_price) / NEW.original_price) * 100, 2);
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER calculate_deals_discount_percentage
    BEFORE INSERT OR UPDATE ON deals
    FOR EACH ROW
    EXECUTE FUNCTION calculate_discount_percentage();

-- Create function to automatically expire deals
CREATE OR REPLACE FUNCTION expire_old_deals()
RETURNS INTEGER AS $$
DECLARE
    expired_count INTEGER;
BEGIN
    UPDATE deals 
    SET status = 'expired', updated_at = CURRENT_TIMESTAMP
    WHERE status = 'active' AND end_date IS NOT NULL AND end_date <= CURRENT_TIMESTAMP;
    
    GET DIAGNOSTICS expired_count = ROW_COUNT;
    RETURN expired_count;
END;
$$ LANGUAGE plpgsql;

-- Create function to increment deal interactions
CREATE OR REPLACE FUNCTION increment_deal_interaction(deal_id INTEGER, interaction_type VARCHAR(20))
RETURNS VOID AS $$
BEGIN
    CASE interaction_type
        WHEN 'click' THEN
            UPDATE deals SET click_count = click_count + 1, updated_at = CURRENT_TIMESTAMP WHERE id = deal_id;
        WHEN 'view' THEN
            UPDATE deals SET view_count = view_count + 1, updated_at = CURRENT_TIMESTAMP WHERE id = deal_id;
        WHEN 'like' THEN
            UPDATE deals SET like_count = like_count + 1, updated_at = CURRENT_TIMESTAMP WHERE id = deal_id;
        WHEN 'share' THEN
            UPDATE deals SET share_count = share_count + 1, updated_at = CURRENT_TIMESTAMP WHERE id = deal_id;
    END CASE;
END;
$$ LANGUAGE plpgsql;

-- Create function to update price history
CREATE OR REPLACE FUNCTION update_price_history(deal_id INTEGER, new_price DECIMAL(10,2))
RETURNS VOID AS $$
DECLARE
    current_history JSONB;
    new_entry JSONB;
BEGIN
    SELECT price_history INTO current_history FROM deals WHERE id = deal_id;
    
    IF current_history IS NULL THEN
        current_history := '[]'::jsonb;
    END IF;
    
    new_entry := jsonb_build_object(
        'price', new_price,
        'timestamp', CURRENT_TIMESTAMP
    );
    
    UPDATE deals 
    SET 
        price_history = current_history || new_entry,
        last_price_check = CURRENT_TIMESTAMP,
        updated_at = CURRENT_TIMESTAMP
    WHERE id = deal_id;
END;
$$ LANGUAGE plpgsql;

-- Create view for active deals with brand and category info
CREATE VIEW active_deals_view AS
SELECT 
    d.*,
    b.name as brand_name,
    b.logo_url as brand_logo,
    cat.name as category_name,
    cat.slug as category_slug,
    co.name as country_name,
    co.code as country_code,
    co.currency as country_currency
FROM deals d
LEFT JOIN brands b ON d.brand_id = b.id
LEFT JOIN categories cat ON d.category_id = cat.id
LEFT JOIN countries co ON d.country_id = co.id
WHERE d.status = 'active' AND (d.end_date IS NULL OR d.end_date > CURRENT_TIMESTAMP);
