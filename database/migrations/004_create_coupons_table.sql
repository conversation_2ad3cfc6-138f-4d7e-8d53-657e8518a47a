-- Create coupons table
CREATE TABLE IF NOT EXISTS coupons (
    id SERIAL PRIMARY KEY,
    title VARCHAR(200) NOT NULL,
    description TEXT,
    code VARCHAR(50), -- Coupon code (can be NULL for automatic discounts)
    discount_type VARCHAR(20) NOT NULL CHECK (discount_type IN ('percentage', 'fixed_amount', 'free_shipping', 'buy_one_get_one')),
    discount_value DECIMAL(10,2), -- Percentage or fixed amount value
    minimum_order_amount DECIMAL(10,2), -- Minimum purchase required
    maximum_discount_amount DECIMAL(10,2), -- Maximum discount cap for percentage discounts
    usage_limit INTEGER, -- Total usage limit (NULL for unlimited)
    usage_count INTEGER DEFAULT 0, -- Current usage count
    user_usage_limit INTEGER DEFAULT 1, -- Per-user usage limit
    
    -- Relationships
    brand_id INTEGER NOT NULL REFERENCES brands(id) ON DELETE CASCADE,
    category_id INTEGER REFERENCES categories(id) ON DELETE SET NULL,
    country_id INTEGER REFERENCES countries(id) ON DELETE SET NULL,
    
    -- Dates and timing
    start_date TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    expiry_date TIMESTAMP WITH TIME ZONE NOT NULL,
    
    -- URLs and tracking
    affiliate_url TEXT, -- Direct affiliate link
    terms_and_conditions TEXT,
    exclusions TEXT, -- What's excluded from the discount
    
    -- Metadata
    is_featured BOOLEAN DEFAULT FALSE,
    is_exclusive BOOLEAN DEFAULT FALSE, -- Exclusive to our site
    is_verified BOOLEAN DEFAULT FALSE, -- Manually verified as working
    popularity_score INTEGER DEFAULT 0, -- For sorting popular coupons
    click_count INTEGER DEFAULT 0, -- Number of times clicked
    success_rate DECIMAL(5,2) DEFAULT 0.0, -- Success rate percentage
    last_verified TIMESTAMP WITH TIME ZONE,
    
    -- SEO and display
    meta_title VARCHAR(200),
    meta_description TEXT,
    image_url VARCHAR(500),
    
    -- Status and timestamps
    status VARCHAR(20) NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'expired', 'pending')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for better performance
CREATE INDEX idx_coupons_brand_id ON coupons(brand_id);
CREATE INDEX idx_coupons_category_id ON coupons(category_id);
CREATE INDEX idx_coupons_country_id ON coupons(country_id);
CREATE INDEX idx_coupons_status ON coupons(status);
CREATE INDEX idx_coupons_expiry_date ON coupons(expiry_date);
CREATE INDEX idx_coupons_start_date ON coupons(start_date);
CREATE INDEX idx_coupons_discount_type ON coupons(discount_type);
CREATE INDEX idx_coupons_is_featured ON coupons(is_featured);
CREATE INDEX idx_coupons_is_exclusive ON coupons(is_exclusive);
CREATE INDEX idx_coupons_popularity_score ON coupons(popularity_score DESC);
CREATE INDEX idx_coupons_click_count ON coupons(click_count DESC);
CREATE INDEX idx_coupons_success_rate ON coupons(success_rate DESC);
CREATE INDEX idx_coupons_created_at ON coupons(created_at DESC);

-- Composite indexes for common queries
CREATE INDEX idx_coupons_active_unexpired ON coupons(status, expiry_date) WHERE status = 'active';
CREATE INDEX idx_coupons_brand_active ON coupons(brand_id, status, expiry_date);
CREATE INDEX idx_coupons_category_active ON coupons(category_id, status, expiry_date);
CREATE INDEX idx_coupons_country_active ON coupons(country_id, status, expiry_date);

-- Full-text search index
CREATE INDEX idx_coupons_search ON coupons USING gin(to_tsvector('english', title || ' ' || COALESCE(description, '')));

-- Create trigger to update updated_at timestamp
CREATE TRIGGER update_coupons_updated_at 
    BEFORE UPDATE ON coupons 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Create function to automatically expire coupons
CREATE OR REPLACE FUNCTION expire_old_coupons()
RETURNS INTEGER AS $$
DECLARE
    expired_count INTEGER;
BEGIN
    UPDATE coupons 
    SET status = 'expired', updated_at = CURRENT_TIMESTAMP
    WHERE status = 'active' AND expiry_date <= CURRENT_TIMESTAMP;
    
    GET DIAGNOSTICS expired_count = ROW_COUNT;
    RETURN expired_count;
END;
$$ LANGUAGE plpgsql;

-- Create function to increment click count
CREATE OR REPLACE FUNCTION increment_coupon_clicks(coupon_id INTEGER)
RETURNS VOID AS $$
BEGIN
    UPDATE coupons 
    SET click_count = click_count + 1, updated_at = CURRENT_TIMESTAMP
    WHERE id = coupon_id;
END;
$$ LANGUAGE plpgsql;

-- Create function to update success rate
CREATE OR REPLACE FUNCTION update_coupon_success_rate(coupon_id INTEGER, was_successful BOOLEAN)
RETURNS VOID AS $$
DECLARE
    current_rate DECIMAL(5,2);
    current_clicks INTEGER;
    new_rate DECIMAL(5,2);
BEGIN
    SELECT success_rate, click_count INTO current_rate, current_clicks
    FROM coupons WHERE id = coupon_id;
    
    IF current_clicks > 0 THEN
        IF was_successful THEN
            new_rate := ((current_rate * current_clicks) + 100) / (current_clicks + 1);
        ELSE
            new_rate := (current_rate * current_clicks) / (current_clicks + 1);
        END IF;
        
        UPDATE coupons 
        SET success_rate = new_rate, updated_at = CURRENT_TIMESTAMP
        WHERE id = coupon_id;
    END IF;
END;
$$ LANGUAGE plpgsql;

-- Create view for active coupons with brand and category info
CREATE VIEW active_coupons_view AS
SELECT 
    c.*,
    b.name as brand_name,
    b.logo_url as brand_logo,
    cat.name as category_name,
    cat.slug as category_slug,
    co.name as country_name,
    co.code as country_code,
    co.currency as country_currency
FROM coupons c
LEFT JOIN brands b ON c.brand_id = b.id
LEFT JOIN categories cat ON c.category_id = cat.id
LEFT JOIN countries co ON c.country_id = co.id
WHERE c.status = 'active' AND c.expiry_date > CURRENT_TIMESTAMP;
