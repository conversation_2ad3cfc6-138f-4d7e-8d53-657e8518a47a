-- Create brands table
CREATE TABLE IF NOT EXISTS brands (
    id SERIAL PRIMARY KEY,
    name <PERSON><PERSON><PERSON><PERSON>(100) NOT NULL UNIQUE,
    description TEXT,
    logo_url VARCHAR(500),
    website_url VARCHAR(500),
    affiliate_url VARCHAR(500), -- For affiliate tracking
    commission_rate DECIMAL(5,2), -- Commission percentage for affiliate programs
    popularity_score INTEGER DEFAULT 0, -- For sorting popular brands
    average_discount DECIMAL(5,2), -- Average discount percentage offered
    total_coupons INTEGER DEFAULT 0, -- Cached count of active coupons
    total_deals INTEGER DEFAULT 0, -- Cached count of active deals
    last_updated TIMESTAMP WITH TIME ZONE, -- Last time coupons/deals were updated
    social_facebook VARCHAR(200),
    social_twitter VARCHAR(200),
    social_instagram VARCHAR(200),
    contact_email VARCHAR(100),
    customer_service_phone VARCHAR(20),
    headquarters_country_id INTEGER REFERENCES countries(id),
    founded_year INTEGER,
    employee_count VARCHAR(20), -- e.g., '1-50', '51-200', '201-500', etc.
    annual_revenue VARCHAR(20), -- e.g., '$1M-$10M', '$10M-$100M', etc.
    industry VARCHAR(100),
    tags TEXT[], -- Array of tags for better categorization
    status VARCHAR(20) NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'pending')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for better performance
CREATE INDEX idx_brands_name ON brands(name);
CREATE INDEX idx_brands_status ON brands(status);
CREATE INDEX idx_brands_popularity_score ON brands(popularity_score DESC);
CREATE INDEX idx_brands_average_discount ON brands(average_discount DESC);
CREATE INDEX idx_brands_total_coupons ON brands(total_coupons DESC);
CREATE INDEX idx_brands_total_deals ON brands(total_deals DESC);
CREATE INDEX idx_brands_headquarters_country_id ON brands(headquarters_country_id);
CREATE INDEX idx_brands_industry ON brands(industry);
CREATE INDEX idx_brands_tags ON brands USING GIN(tags);

-- Create trigger to update updated_at timestamp
CREATE TRIGGER update_brands_updated_at 
    BEFORE UPDATE ON brands 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Insert initial brand data for popular European and American brands
INSERT INTO brands (name, description, website_url, headquarters_country_id, industry, tags) VALUES
-- American brands
('Amazon', 'Global e-commerce and cloud computing giant', 'https://amazon.com', 
 (SELECT id FROM countries WHERE code = 'US'), 'E-commerce', 
 ARRAY['e-commerce', 'technology', 'retail', 'cloud']),

('Nike', 'Athletic footwear and apparel company', 'https://nike.com', 
 (SELECT id FROM countries WHERE code = 'US'), 'Sportswear', 
 ARRAY['sports', 'footwear', 'apparel', 'athletic']),

('Apple', 'Technology company specializing in consumer electronics', 'https://apple.com', 
 (SELECT id FROM countries WHERE code = 'US'), 'Technology', 
 ARRAY['technology', 'electronics', 'smartphones', 'computers']),

('Walmart', 'Multinational retail corporation', 'https://walmart.com', 
 (SELECT id FROM countries WHERE code = 'US'), 'Retail', 
 ARRAY['retail', 'grocery', 'discount', 'supermarket']),

('Target', 'General merchandise retailer', 'https://target.com', 
 (SELECT id FROM countries WHERE code = 'US'), 'Retail', 
 ARRAY['retail', 'home', 'fashion', 'grocery']),

('Best Buy', 'Consumer electronics retailer', 'https://bestbuy.com', 
 (SELECT id FROM countries WHERE code = 'US'), 'Electronics Retail', 
 ARRAY['electronics', 'technology', 'appliances', 'gaming']),

('Macy''s', 'Department store chain', 'https://macys.com', 
 (SELECT id FROM countries WHERE code = 'US'), 'Fashion Retail', 
 ARRAY['fashion', 'department-store', 'clothing', 'accessories']),

-- European brands
('H&M', 'Swedish multinational clothing retailer', 'https://hm.com', 
 (SELECT id FROM countries WHERE code = 'SE'), 'Fashion Retail', 
 ARRAY['fashion', 'clothing', 'fast-fashion', 'affordable']),

('IKEA', 'Swedish furniture and home goods retailer', 'https://ikea.com', 
 (SELECT id FROM countries WHERE code = 'SE'), 'Furniture Retail', 
 ARRAY['furniture', 'home', 'design', 'affordable']),

('Zara', 'Spanish clothing and accessories retailer', 'https://zara.com', 
 (SELECT id FROM countries WHERE code = 'ES'), 'Fashion Retail', 
 ARRAY['fashion', 'clothing', 'fast-fashion', 'trendy']),

('Adidas', 'German multinational sportswear corporation', 'https://adidas.com', 
 (SELECT id FROM countries WHERE code = 'DE'), 'Sportswear', 
 ARRAY['sports', 'footwear', 'apparel', 'athletic']),

('BMW', 'German luxury vehicle manufacturer', 'https://bmw.com', 
 (SELECT id FROM countries WHERE code = 'DE'), 'Automotive', 
 ARRAY['automotive', 'luxury', 'cars', 'german']),

('Mercedes-Benz', 'German luxury automotive brand', 'https://mercedes-benz.com', 
 (SELECT id FROM countries WHERE code = 'DE'), 'Automotive', 
 ARRAY['automotive', 'luxury', 'cars', 'premium']),

('Volkswagen', 'German automotive manufacturer', 'https://volkswagen.com', 
 (SELECT id FROM countries WHERE code = 'DE'), 'Automotive', 
 ARRAY['automotive', 'cars', 'german', 'reliable']),

('Uniqlo', 'Japanese casual wear designer and retailer', 'https://uniqlo.com', 
 (SELECT id FROM countries WHERE code = 'US'), 'Fashion Retail', 
 ARRAY['fashion', 'clothing', 'casual', 'basics']),

('ASOS', 'British online fashion retailer', 'https://asos.com', 
 (SELECT id FROM countries WHERE code = 'GB'), 'Fashion Retail', 
 ARRAY['fashion', 'online', 'young-adults', 'trendy']);

-- Create function to update brand statistics
CREATE OR REPLACE FUNCTION update_brand_stats(brand_id INTEGER)
RETURNS VOID AS $$
BEGIN
    UPDATE brands SET
        total_coupons = (
            SELECT COUNT(*) FROM coupons 
            WHERE brand_id = brands.id AND status = 'active' AND expiry_date > CURRENT_TIMESTAMP
        ),
        total_deals = (
            SELECT COUNT(*) FROM deals 
            WHERE brand_id = brands.id AND status = 'active'
        ),
        last_updated = CURRENT_TIMESTAMP
    WHERE id = brand_id;
END;
$$ LANGUAGE plpgsql;
