-- Migration: Update schema to remove foreign keys and optimize structure
-- Created: 2024-07-04

-- Drop existing foreign key constraints if they exist
ALTER TABLE IF EXISTS brands DROP CONSTRAINT IF EXISTS fk_brands_headquarters_country;
ALTER TABLE IF EXISTS coupons DROP CONSTRAINT IF EXISTS fk_coupons_brand;
ALTER TABLE IF EXISTS coupons DROP CONSTRAINT IF EXISTS fk_coupons_category;
ALTER TABLE IF EXISTS coupons DROP CONSTRAINT IF EXISTS fk_coupons_country;
ALTER TABLE IF EXISTS deals DROP CONSTRAINT IF EXISTS fk_deals_brand;
ALTER TABLE IF EXISTS deals DROP CONSTRAINT IF EXISTS fk_deals_category;
ALTER TABLE IF EXISTS deals DROP CONSTRAINT IF EXISTS fk_deals_country;
ALTER TABLE IF EXISTS categories DROP CONSTRAINT IF EXISTS fk_categories_parent;

-- Update countries table
ALTER TABLE countries ADD COLUMN IF NOT EXISTS flag VARCHAR(10) NOT NULL DEFAULT '🏳️';
ALTER TABLE countries ADD COLUMN IF NOT EXISTS locale VARCHAR(10) NOT NULL DEFAULT 'en-US';
ALTER TABLE countries ADD COLUMN IF NOT EXISTS timezone VARCHAR(50) NOT NULL DEFAULT 'UTC';

-- Update categories table (remove hierarchical structure)
ALTER TABLE categories DROP COLUMN IF EXISTS parent_id;

-- Update brands table
ALTER TABLE brands ADD COLUMN IF NOT EXISTS unique_name VARCHAR(100);
ALTER TABLE brands ADD COLUMN IF NOT EXISTS category_id INTEGER;
ALTER TABLE brands ADD COLUMN IF NOT EXISTS country_id INTEGER;
ALTER TABLE brands ADD COLUMN IF NOT EXISTS platform_type VARCHAR(50);
ALTER TABLE brands ADD COLUMN IF NOT EXISTS platform_brand_id VARCHAR(100);

-- Remove unnecessary brand columns
ALTER TABLE brands DROP COLUMN IF EXISTS social_facebook;
ALTER TABLE brands DROP COLUMN IF EXISTS social_twitter;
ALTER TABLE brands DROP COLUMN IF EXISTS social_instagram;
ALTER TABLE brands DROP COLUMN IF EXISTS contact_email;
ALTER TABLE brands DROP COLUMN IF EXISTS customer_service_phone;
ALTER TABLE brands DROP COLUMN IF EXISTS headquarters_country_id;
ALTER TABLE brands DROP COLUMN IF EXISTS founded_year;
ALTER TABLE brands DROP COLUMN IF EXISTS employee_count;
ALTER TABLE brands DROP COLUMN IF EXISTS annual_revenue;
ALTER TABLE brands DROP COLUMN IF EXISTS industry;

-- Create unique index for brand unique_name
CREATE UNIQUE INDEX IF NOT EXISTS idx_brands_unique_name ON brands(unique_name);

-- Update coupons table (remove unnecessary columns)
ALTER TABLE coupons DROP COLUMN IF EXISTS usage_limit;
ALTER TABLE coupons DROP COLUMN IF EXISTS usage_count;
ALTER TABLE coupons DROP COLUMN IF EXISTS user_usage_limit;
ALTER TABLE coupons DROP COLUMN IF EXISTS affiliate_url;
ALTER TABLE coupons DROP COLUMN IF EXISTS terms_and_conditions;
ALTER TABLE coupons DROP COLUMN IF EXISTS exclusions;

-- Update deals table (simplify structure)
ALTER TABLE deals DROP COLUMN IF EXISTS availability;
ALTER TABLE deals DROP COLUMN IF EXISTS shipping_cost;
ALTER TABLE deals DROP COLUMN IF EXISTS shipping_info;
ALTER TABLE deals DROP COLUMN IF EXISTS return_policy;
ALTER TABLE deals DROP COLUMN IF EXISTS warranty_info;
ALTER TABLE deals DROP COLUMN IF EXISTS product_specifications;
ALTER TABLE deals DROP COLUMN IF EXISTS product_features;
ALTER TABLE deals DROP COLUMN IF EXISTS product_dimensions;
ALTER TABLE deals DROP COLUMN IF EXISTS product_weight;
ALTER TABLE deals DROP COLUMN IF EXISTS product_color;
ALTER TABLE deals DROP COLUMN IF EXISTS product_size;
ALTER TABLE deals DROP COLUMN IF EXISTS product_material;
ALTER TABLE deals DROP COLUMN IF EXISTS product_brand;
ALTER TABLE deals DROP COLUMN IF EXISTS product_model;
ALTER TABLE deals DROP COLUMN IF EXISTS product_sku;
ALTER TABLE deals DROP COLUMN IF EXISTS product_upc;
ALTER TABLE deals DROP COLUMN IF EXISTS product_isbn;
ALTER TABLE deals DROP COLUMN IF EXISTS product_mpn;
ALTER TABLE deals DROP COLUMN IF EXISTS product_gtin;

-- Create indexes for better performance (without foreign keys)
CREATE INDEX IF NOT EXISTS idx_brands_category_id ON brands(category_id);
CREATE INDEX IF NOT EXISTS idx_brands_country_id ON brands(country_id);
CREATE INDEX IF NOT EXISTS idx_brands_platform_type ON brands(platform_type);
CREATE INDEX IF NOT EXISTS idx_brands_status ON brands(status);

CREATE INDEX IF NOT EXISTS idx_coupons_brand_id ON coupons(brand_id);
CREATE INDEX IF NOT EXISTS idx_coupons_category_id ON coupons(category_id);
CREATE INDEX IF NOT EXISTS idx_coupons_country_id ON coupons(country_id);
CREATE INDEX IF NOT EXISTS idx_coupons_status ON coupons(status);
CREATE INDEX IF NOT EXISTS idx_coupons_expiry_date ON coupons(expiry_date);
CREATE INDEX IF NOT EXISTS idx_coupons_is_featured ON coupons(is_featured);
CREATE INDEX IF NOT EXISTS idx_coupons_is_exclusive ON coupons(is_exclusive);

CREATE INDEX IF NOT EXISTS idx_deals_brand_id ON deals(brand_id);
CREATE INDEX IF NOT EXISTS idx_deals_category_id ON deals(category_id);
CREATE INDEX IF NOT EXISTS idx_deals_country_id ON deals(country_id);
CREATE INDEX IF NOT EXISTS idx_deals_status ON deals(status);
CREATE INDEX IF NOT EXISTS idx_deals_end_date ON deals(end_date);
CREATE INDEX IF NOT EXISTS idx_deals_is_featured ON deals(is_featured);
CREATE INDEX IF NOT EXISTS idx_deals_is_hot_deal ON deals(is_hot_deal);

CREATE INDEX IF NOT EXISTS idx_categories_slug ON categories(slug);
CREATE INDEX IF NOT EXISTS idx_categories_status ON categories(status);

CREATE INDEX IF NOT EXISTS idx_countries_code ON countries(code);
CREATE INDEX IF NOT EXISTS idx_countries_status ON countries(status);

-- Create analytics tables if they don't exist
CREATE TABLE IF NOT EXISTS click_analytics (
    id SERIAL PRIMARY KEY,
    entity_type VARCHAR(20) NOT NULL,
    entity_id INTEGER NOT NULL,
    user_ip INET,
    user_agent TEXT,
    referrer_url TEXT,
    country_code VARCHAR(3),
    device_type VARCHAR(20),
    browser VARCHAR(50),
    os VARCHAR(50),
    clicked_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS search_analytics (
    id SERIAL PRIMARY KEY,
    query TEXT NOT NULL,
    results_count INTEGER DEFAULT 0,
    user_ip INET,
    user_agent TEXT,
    country_code VARCHAR(3),
    device_type VARCHAR(20),
    search_filters JSONB,
    clicked_result_id INTEGER,
    clicked_result_type VARCHAR(20),
    searched_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS daily_stats (
    id SERIAL PRIMARY KEY,
    stat_date DATE NOT NULL UNIQUE,
    total_coupons INTEGER DEFAULT 0,
    active_coupons INTEGER DEFAULT 0,
    expired_coupons INTEGER DEFAULT 0,
    total_deals INTEGER DEFAULT 0,
    active_deals INTEGER DEFAULT 0,
    expired_deals INTEGER DEFAULT 0,
    total_clicks INTEGER DEFAULT 0,
    total_searches INTEGER DEFAULT 0,
    unique_visitors INTEGER DEFAULT 0,
    top_brand_id INTEGER,
    top_category_id INTEGER,
    top_country_code VARCHAR(3),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for analytics tables
CREATE INDEX IF NOT EXISTS idx_click_analytics_entity ON click_analytics(entity_type, entity_id);
CREATE INDEX IF NOT EXISTS idx_click_analytics_clicked_at ON click_analytics(clicked_at);
CREATE INDEX IF NOT EXISTS idx_click_analytics_country_code ON click_analytics(country_code);

CREATE INDEX IF NOT EXISTS idx_search_analytics_query ON search_analytics(query);
CREATE INDEX IF NOT EXISTS idx_search_analytics_searched_at ON search_analytics(searched_at);
CREATE INDEX IF NOT EXISTS idx_search_analytics_country_code ON search_analytics(country_code);

CREATE INDEX IF NOT EXISTS idx_daily_stats_stat_date ON daily_stats(stat_date);

-- Update existing data to set unique_name for brands
UPDATE brands SET unique_name = LOWER(REGEXP_REPLACE(name, '[^a-zA-Z0-9]+', '-', 'g')) 
WHERE unique_name IS NULL OR unique_name = '';

-- Remove leading/trailing hyphens from unique_name
UPDATE brands SET unique_name = TRIM(BOTH '-' FROM unique_name);

-- Handle duplicate unique_names by appending numbers
WITH ranked_brands AS (
    SELECT id, unique_name, 
           ROW_NUMBER() OVER (PARTITION BY unique_name ORDER BY id) as rn
    FROM brands
)
UPDATE brands 
SET unique_name = CASE 
    WHEN rb.rn = 1 THEN rb.unique_name
    ELSE rb.unique_name || '-' || rb.rn
END
FROM ranked_brands rb
WHERE brands.id = rb.id AND rb.rn > 1;
