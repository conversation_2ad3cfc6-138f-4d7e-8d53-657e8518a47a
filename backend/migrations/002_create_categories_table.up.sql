-- Create categories table with hierarchical structure
CREATE TABLE IF NOT EXISTS categories (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    slug VARCHAR(120) NOT NULL UNIQUE,
    parent_id INTEGER REFERENCES categories(id) ON DELETE SET NULL,
    icon VARCHAR(100), -- Icon class or image URL
    color VARCHAR(7), -- Hex color code for category theming
    sort_order INTEGER DEFAULT 0,
    status VARCHAR(20) NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'inactive')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for better performance
CREATE INDEX idx_categories_slug ON categories(slug);
CREATE INDEX idx_categories_parent_id ON categories(parent_id);
CREATE INDEX idx_categories_status ON categories(status);
CREATE INDEX idx_categories_sort_order ON categories(sort_order);
CREATE INDEX idx_categories_name ON categories(name);

-- <PERSON>reate trigger to update updated_at timestamp
CREATE TRIGGER update_categories_updated_at 
    BEFORE UPDATE ON categories 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Insert initial category data
INSERT INTO categories (name, description, slug, parent_id, icon, color, sort_order) VALUES
-- Top-level categories
('Fashion & Clothing', 'Clothing, shoes, accessories and fashion items', 'fashion-clothing', NULL, 'fas fa-tshirt', '#E91E63', 1),
('Electronics & Tech', 'Computers, phones, gadgets and electronic devices', 'electronics-tech', NULL, 'fas fa-laptop', '#2196F3', 2),
('Home & Garden', 'Home improvement, furniture, garden and household items', 'home-garden', NULL, 'fas fa-home', '#4CAF50', 3),
('Health & Beauty', 'Cosmetics, skincare, health products and wellness', 'health-beauty', NULL, 'fas fa-heart', '#FF5722', 4),
('Sports & Outdoors', 'Sports equipment, outdoor gear and fitness products', 'sports-outdoors', NULL, 'fas fa-running', '#FF9800', 5),
('Travel & Leisure', 'Hotels, flights, vacation packages and entertainment', 'travel-leisure', NULL, 'fas fa-plane', '#9C27B0', 6),
('Food & Dining', 'Restaurants, food delivery, groceries and beverages', 'food-dining', NULL, 'fas fa-utensils', '#795548', 7),
('Automotive', 'Cars, parts, accessories and automotive services', 'automotive', NULL, 'fas fa-car', '#607D8B', 8),
('Books & Education', 'Books, courses, educational materials and learning', 'books-education', NULL, 'fas fa-book', '#3F51B5', 9),
('Baby & Kids', 'Baby products, toys, kids clothing and family items', 'baby-kids', NULL, 'fas fa-baby', '#E91E63', 10);

-- Get category IDs for subcategories
DO $$
DECLARE
    fashion_id INTEGER;
    electronics_id INTEGER;
    home_id INTEGER;
    health_id INTEGER;
BEGIN
    SELECT id INTO fashion_id FROM categories WHERE slug = 'fashion-clothing';
    SELECT id INTO electronics_id FROM categories WHERE slug = 'electronics-tech';
    SELECT id INTO home_id FROM categories WHERE slug = 'home-garden';
    SELECT id INTO health_id FROM categories WHERE slug = 'health-beauty';

    -- Fashion subcategories
    INSERT INTO categories (name, description, slug, parent_id, sort_order) VALUES
    ('Women''s Clothing', 'Dresses, tops, pants and women''s fashion', 'womens-clothing', fashion_id, 1),
    ('Men''s Clothing', 'Shirts, pants, suits and men''s fashion', 'mens-clothing', fashion_id, 2),
    ('Shoes & Footwear', 'Sneakers, boots, heels and all types of shoes', 'shoes-footwear', fashion_id, 3),
    ('Accessories', 'Bags, jewelry, watches and fashion accessories', 'accessories', fashion_id, 4),

    -- Electronics subcategories
    ('Smartphones & Tablets', 'Mobile devices, tablets and accessories', 'smartphones-tablets', electronics_id, 1),
    ('Computers & Laptops', 'Desktop computers, laptops and computer accessories', 'computers-laptops', electronics_id, 2),
    ('Gaming', 'Video games, consoles and gaming accessories', 'gaming', electronics_id, 3),
    ('Audio & Video', 'Headphones, speakers, TVs and audio equipment', 'audio-video', electronics_id, 4),

    -- Home subcategories
    ('Furniture', 'Sofas, tables, chairs and home furniture', 'furniture', home_id, 1),
    ('Kitchen & Dining', 'Cookware, appliances and kitchen essentials', 'kitchen-dining', home_id, 2),
    ('Bedding & Bath', 'Sheets, towels and bathroom accessories', 'bedding-bath', home_id, 3),
    ('Garden & Outdoor', 'Plants, tools and outdoor furniture', 'garden-outdoor', home_id, 4),

    -- Health & Beauty subcategories
    ('Skincare', 'Face care, moisturizers and skincare products', 'skincare', health_id, 1),
    ('Makeup & Cosmetics', 'Makeup, lipstick, foundation and beauty products', 'makeup-cosmetics', health_id, 2),
    ('Health & Wellness', 'Vitamins, supplements and health products', 'health-wellness', health_id, 3),
    ('Hair Care', 'Shampoo, styling products and hair treatments', 'hair-care', health_id, 4);
END $$;

-- Create function to get category hierarchy
CREATE OR REPLACE FUNCTION get_category_path(category_id INTEGER)
RETURNS TEXT AS $$
DECLARE
    path TEXT := '';
    current_id INTEGER := category_id;
    current_name TEXT;
    parent_id INTEGER;
BEGIN
    WHILE current_id IS NOT NULL LOOP
        SELECT name, parent_id INTO current_name, parent_id FROM categories WHERE id = current_id;
        IF path = '' THEN
            path := current_name;
        ELSE
            path := current_name || ' > ' || path;
        END IF;
        current_id := parent_id;
    END LOOP;
    RETURN path;
END;
$$ LANGUAGE plpgsql;
