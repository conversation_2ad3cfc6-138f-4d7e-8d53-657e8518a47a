-- Create countries table
CREATE TABLE IF NOT EXISTS countries (
    id SERIAL PRIMARY KEY,
    name VA<PERSON>HA<PERSON>(100) NOT NULL UNIQUE,
    code VARCHAR(3) NOT NULL UNIQUE, -- ISO 3166-1 alpha-2 code (e.g., 'US', 'DE')
    currency VARCHAR(3) NOT NULL, -- ISO 4217 currency code (e.g., 'USD', 'EUR')
    status VARCHAR(20) NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'inactive')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for better performance
CREATE INDEX idx_countries_code ON countries(code);
CREATE INDEX idx_countries_status ON countries(status);
CREATE INDEX idx_countries_name ON countries(name);

-- <PERSON>reate trigger to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_countries_updated_at 
    BEFORE UPDATE ON countries 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Insert initial data for European and American countries
INSERT INTO countries (name, code, currency) VALUES
('United States', 'US', 'USD'),
('Canada', 'CA', 'CAD'),
('United Kingdom', 'GB', 'GBP'),
('Germany', 'DE', 'EUR'),
('France', 'FR', 'EUR'),
('Italy', 'IT', 'EUR'),
('Spain', 'ES', 'EUR'),
('Netherlands', 'NL', 'EUR'),
('Belgium', 'BE', 'EUR'),
('Austria', 'AT', 'EUR'),
('Switzerland', 'CH', 'CHF'),
('Sweden', 'SE', 'SEK'),
('Norway', 'NO', 'NOK'),
('Denmark', 'DK', 'DKK'),
('Poland', 'PL', 'PLN'),
('Czech Republic', 'CZ', 'CZK'),
('Hungary', 'HU', 'HUF'),
('Portugal', 'PT', 'EUR'),
('Ireland', 'IE', 'EUR'),
('Finland', 'FI', 'EUR')
ON CONFLICT (code) DO NOTHING;
