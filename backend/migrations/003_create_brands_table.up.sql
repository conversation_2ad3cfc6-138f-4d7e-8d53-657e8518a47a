-- Create brands table
CREATE TABLE IF NOT EXISTS brands (
    id SERIAL PRIMARY KEY,
    name VA<PERSON><PERSON><PERSON>(100) NOT NULL UNIQUE,
    description TEXT,
    logo_url VARCHAR(500),
    website_url VARCHAR(500),
    affiliate_url VARCHAR(500),
    commission_rate DECIMAL(5,2),
    popularity_score INTEGER DEFAULT 0,
    average_discount DECIMAL(5,2),
    total_coupons INTEGER DEFAULT 0,
    total_deals INTEGER DEFAULT 0,
    last_updated TIMESTAMP WITH TIME ZONE,
    social_facebook VARCHAR(200),
    social_twitter VARCHAR(200),
    social_instagram VARCHAR(200),
    contact_email VARCHAR(100),
    customer_service_phone VARCHAR(20),
    headquarters_country_id INTEGER REFERENCES countries(id),
    founded_year INTEGER,
    employee_count VARCHAR(20),
    annual_revenue VARCHAR(20),
    industry VARCHAR(100),
    tags TEXT[],
    status VARCHAR(20) NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'pending')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes
CREATE INDEX idx_brands_name ON brands(name);
CREATE INDEX idx_brands_status ON brands(status);
CREATE INDEX idx_brands_popularity_score ON brands(popularity_score DESC);
CREATE INDEX idx_brands_headquarters_country_id ON brands(headquarters_country_id);
CREATE INDEX idx_brands_tags ON brands USING GIN(tags);

-- Create trigger
CREATE TRIGGER update_brands_updated_at 
    BEFORE UPDATE ON brands 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Insert sample brands
INSERT INTO brands (name, description, website_url, headquarters_country_id, industry, tags) VALUES
('Amazon', 'Global e-commerce and cloud computing giant', 'https://amazon.com', 
 (SELECT id FROM countries WHERE code = 'US'), 'E-commerce', 
 ARRAY['e-commerce', 'technology', 'retail', 'cloud']),
('Nike', 'Athletic footwear and apparel company', 'https://nike.com', 
 (SELECT id FROM countries WHERE code = 'US'), 'Sportswear', 
 ARRAY['sports', 'footwear', 'apparel', 'athletic']),
('H&M', 'Swedish multinational clothing retailer', 'https://hm.com', 
 (SELECT id FROM countries WHERE code = 'SE'), 'Fashion Retail', 
 ARRAY['fashion', 'clothing', 'fast-fashion', 'affordable']),
('IKEA', 'Swedish furniture and home goods retailer', 'https://ikea.com', 
 (SELECT id FROM countries WHERE code = 'SE'), 'Furniture Retail', 
 ARRAY['furniture', 'home', 'design', 'affordable']),
('Adidas', 'German multinational sportswear corporation', 'https://adidas.com', 
 (SELECT id FROM countries WHERE code = 'DE'), 'Sportswear', 
 ARRAY['sports', 'footwear', 'apparel', 'athletic'])
ON CONFLICT (name) DO NOTHING;
