-- Updated seed data for MaxCoupon platform
-- Created: 2024-07-04

-- Clear existing data
TRUNCATE TABLE click_analytics, search_analytics, daily_stats, coupons, deals, brands, categories, countries RESTART IDENTITY CASCADE;

-- Insert countries with flags and locales
INSERT INTO countries (name, code, flag, currency, locale, timezone, status) VALUES
('United States', 'US', '🇺🇸', 'USD', 'en-US', 'America/New_York', 'active'),
('Canada', 'CA', '🇨🇦', 'CAD', 'en-CA', 'America/Toronto', 'active'),
('United Kingdom', 'GB', '🇬🇧', 'GBP', 'en-GB', 'Europe/London', 'active'),
('Germany', 'DE', '🇩🇪', 'EUR', 'de-DE', 'Europe/Berlin', 'active'),
('France', 'FR', '🇫🇷', 'EUR', 'fr-FR', 'Europe/Paris', 'active'),
('Sweden', 'SE', '🇸🇪', 'SEK', 'sv-SE', 'Europe/Stockholm', 'active');

-- Insert categories (single level only)
INSERT INTO categories (name, description, slug, icon, color, sort_order, status) VALUES
('Fashion & Clothing', 'Clothing, shoes, accessories and fashion items', 'fashion-clothing', '👗', '#E91E63', 1, 'active'),
('Electronics & Tech', 'Computers, phones, gadgets and electronic devices', 'electronics-tech', '📱', '#2196F3', 2, 'active'),
('Home & Garden', 'Home improvement, furniture, garden and household items', 'home-garden', '🏠', '#4CAF50', 3, 'active'),
('Health & Beauty', 'Cosmetics, skincare, health products and wellness', 'health-beauty', '💄', '#FF5722', 4, 'active'),
('Sports & Outdoors', 'Sports equipment, outdoor gear and fitness products', 'sports-outdoors', '⚽', '#FF9800', 5, 'active'),
('Travel & Leisure', 'Hotels, flights, vacation packages and entertainment', 'travel-leisure', '✈️', '#9C27B0', 6, 'active'),
('Food & Dining', 'Restaurants, food delivery, groceries and beverages', 'food-dining', '🍕', '#795548', 7, 'active'),
('Books & Education', 'Books, courses, educational materials and learning', 'books-education', '📚', '#3F51B5', 8, 'active');

-- Insert brands with unique_name and platform info
INSERT INTO brands (name, unique_name, description, logo_url, website_url, affiliate_url, commission_rate, popularity_score, average_discount, total_coupons, total_deals, category_id, country_id, platform_type, platform_brand_id, tags, status) VALUES
('Amazon', 'amazon', 'Global e-commerce giant with millions of products', '/images/brands/amazon.png', 'https://amazon.com', 'https://amazon.com/?tag=maxcoupon', 4.5, 98, 25.0, 156, 89, 2, 1, 'direct', 'AMZ001', '{"electronics", "books", "home", "fashion"}', 'active'),
('Nike', 'nike', 'Leading athletic footwear and apparel brand', '/images/brands/nike.png', 'https://nike.com', 'https://nike.com/?ref=maxcoupon', 6.0, 95, 30.0, 78, 45, 5, 1, 'impact', 'NIKE001', '{"sports", "footwear", "apparel"}', 'active'),
('H&M', 'hm', 'Swedish multinational clothing retailer', '/images/brands/hm.png', 'https://hm.com', 'https://hm.com/?ref=maxcoupon', 5.5, 88, 20.0, 92, 67, 1, 6, 'awin', 'HM001', '{"fashion", "clothing", "accessories"}', 'active'),
('IKEA', 'ikea', 'Swedish furniture and home goods retailer', '/images/brands/ikea.png', 'https://ikea.com', 'https://ikea.com/?ref=maxcoupon', 3.0, 85, 15.0, 34, 28, 3, 6, 'direct', 'IKEA001', '{"furniture", "home", "kitchen"}', 'active'),
('Adidas', 'adidas', 'German multinational sportswear corporation', '/images/brands/adidas.png', 'https://adidas.com', 'https://adidas.com/?ref=maxcoupon', 6.5, 92, 28.0, 65, 41, 5, 4, 'impact', 'ADIDAS001', '{"sports", "footwear", "apparel"}', 'active'),
('Apple', 'apple', 'Technology company known for innovative products', '/images/brands/apple.png', 'https://apple.com', 'https://apple.com/?ref=maxcoupon', 2.5, 96, 10.0, 23, 15, 2, 1, 'direct', 'APPLE001', '{"electronics", "technology", "accessories"}', 'active'),
('Zara', 'zara', 'Spanish fast fashion clothing retailer', '/images/brands/zara.png', 'https://zara.com', 'https://zara.com/?ref=maxcoupon', 4.0, 82, 22.0, 48, 32, 1, 5, 'awin', 'ZARA001', '{"fashion", "clothing", "accessories"}', 'active'),
('Samsung', 'samsung', 'South Korean electronics and technology conglomerate', '/images/brands/samsung.png', 'https://samsung.com', 'https://samsung.com/?ref=maxcoupon', 3.5, 89, 18.0, 41, 29, 2, 1, 'impact', 'SAMSUNG001', '{"electronics", "technology", "mobile"}', 'active'),
('Booking.com', 'booking', 'Global online travel agency', '/images/brands/booking.png', 'https://booking.com', 'https://booking.com/?aid=maxcoupon', 8.0, 91, 12.0, 67, 123, 6, 3, 'direct', 'BOOKING001', '{"travel", "hotels", "accommodation"}', 'active'),
('Sephora', 'sephora', 'French multinational retailer of personal care and beauty products', '/images/brands/sephora.png', 'https://sephora.com', 'https://sephora.com/?ref=maxcoupon', 7.0, 87, 25.0, 89, 56, 4, 5, 'awin', 'SEPHORA001', '{"beauty", "cosmetics", "skincare"}', 'active');

-- Insert sample coupons
INSERT INTO coupons (title, description, code, discount_type, discount_value, minimum_order_amount, maximum_discount_amount, brand_id, category_id, country_id, start_date, expiry_date, is_featured, is_exclusive, is_verified, popularity_score, click_count, success_rate, meta_title, meta_description, image_url, status) VALUES
('Amazon Prime Day Special - 20% Off Electronics', 'Get 20% off on all electronics during Prime Day sale', 'PRIME20', 'percentage', 20.00, 50.00, 100.00, 1, 2, 1, CURRENT_TIMESTAMP, '2024-12-31 23:59:59', true, true, true, 95, 1250, 95.5, 'Amazon 20% Off Electronics Coupon', 'Save 20% on electronics with this exclusive Amazon Prime Day coupon code', '/images/coupons/amazon-electronics.jpg', 'active'),
('Nike Summer Sale - 25% Off Athletic Wear', 'Get 25% off on all athletic wear and footwear', 'SUMMER25', 'percentage', 25.00, 75.00, 150.00, 2, 5, 1, CURRENT_TIMESTAMP, '2024-12-25 23:59:59', true, false, true, 92, 890, 92.3, 'Nike 25% Off Summer Sale Coupon', 'Save 25% on Nike athletic wear with this summer sale coupon', '/images/coupons/nike-summer.jpg', 'active'),
('H&M New Collection - $10 Off $50', 'Get $10 off when you spend $50 or more on new collection', 'NEW10', 'fixed_amount', 10.00, 50.00, 10.00, 3, 1, 6, CURRENT_TIMESTAMP, '2024-12-20 23:59:59', true, true, true, 88, 567, 88.7, 'H&M $10 Off New Collection Coupon', 'Save $10 on H&M new collection with minimum $50 purchase', '/images/coupons/hm-new.jpg', 'active'),
('IKEA Kitchen Sale - 20% Off Kitchen Items', 'Save 20% on all kitchen furniture and accessories', 'KITCHEN20', 'percentage', 20.00, 100.00, 200.00, 4, 3, 6, CURRENT_TIMESTAMP, '2024-12-30 23:59:59', false, false, true, 85, 423, 90.2, 'IKEA 20% Off Kitchen Sale Coupon', 'Save 20% on IKEA kitchen items with this exclusive coupon', '/images/coupons/ikea-kitchen.jpg', 'active'),
('Adidas Flash Sale - 30% Off Sneakers', 'Limited time 30% off on all sneakers and running shoes', 'FLASH30', 'percentage', 30.00, 80.00, 120.00, 5, 5, 4, CURRENT_TIMESTAMP, '2024-12-15 23:59:59', true, true, true, 94, 756, 93.8, 'Adidas 30% Off Sneakers Flash Sale', 'Save 30% on Adidas sneakers with this limited time flash sale coupon', '/images/coupons/adidas-flash.jpg', 'active');

-- Insert sample deals
INSERT INTO deals (title, description, original_price, sale_price, discount_percentage, currency, product_url, image_url, brand_id, category_id, country_id, start_date, end_date, is_featured, is_hot_deal, is_flash_sale, deal_type, popularity_score, click_count, view_count, is_verified, meta_title, meta_description, status) VALUES
('Apple iPhone 15 Pro - Limited Time Deal', 'Latest iPhone 15 Pro with advanced camera system and A17 Pro chip', 999.00, 849.00, 15.02, 'USD', 'https://apple.com/iphone-15-pro', '/images/deals/iphone-15-pro.jpg', 6, 2, 1, CURRENT_TIMESTAMP, '2024-12-31 23:59:59', true, true, false, 'flash_sale', 96, 2340, 5670, true, 'Apple iPhone 15 Pro Deal - Save $150', 'Get the latest iPhone 15 Pro at a discounted price with this limited time deal', 'active'),
('Samsung 65" QLED TV - Black Friday Special', '65-inch QLED 4K Smart TV with Quantum Dot technology', 1299.00, 899.00, 30.79, 'USD', 'https://samsung.com/tv/qled-65', '/images/deals/samsung-qled-tv.jpg', 8, 2, 1, CURRENT_TIMESTAMP, '2024-11-30 23:59:59', true, true, true, 'seasonal', 89, 1876, 4523, true, 'Samsung 65" QLED TV Black Friday Deal', 'Save over $400 on Samsung 65-inch QLED TV this Black Friday', 'active'),
('Nike Air Max 270 - End of Season Sale', 'Popular Nike Air Max 270 sneakers in multiple colors', 150.00, 89.99, 40.01, 'USD', 'https://nike.com/air-max-270', '/images/deals/nike-air-max-270.jpg', 2, 5, 1, CURRENT_TIMESTAMP, '2024-12-20 23:59:59', false, false, false, 'clearance', 87, 1234, 3456, true, 'Nike Air Max 270 Sale - 40% Off', 'Get Nike Air Max 270 sneakers at 40% off in this end of season sale', 'active'),
('IKEA MALM Bed Frame - Weekly Special', 'Modern bed frame with clean lines and timeless design', 179.00, 129.00, 27.93, 'USD', 'https://ikea.com/malm-bed-frame', '/images/deals/ikea-malm-bed.jpg', 4, 3, 1, CURRENT_TIMESTAMP, '2024-12-07 23:59:59', false, false, false, 'weekly_deal', 78, 567, 1234, true, 'IKEA MALM Bed Frame Weekly Deal', 'Save on IKEA MALM bed frame with this weekly special offer', 'active');

-- Insert sample analytics data
INSERT INTO click_analytics (entity_type, entity_id, user_ip, user_agent, referrer_url, country_code, device_type, browser, os, clicked_at) VALUES
('coupon', 1, '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', 'https://google.com', 'US', 'desktop', 'Chrome', 'Windows', CURRENT_TIMESTAMP - INTERVAL '1 hour'),
('coupon', 2, '*************', 'Mozilla/5.0 (iPhone; CPU iPhone OS 17_0 like Mac OS X)', 'https://facebook.com', 'US', 'mobile', 'Safari', 'iOS', CURRENT_TIMESTAMP - INTERVAL '2 hours'),
('deal', 1, '*************', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7)', 'https://twitter.com', 'CA', 'desktop', 'Safari', 'macOS', CURRENT_TIMESTAMP - INTERVAL '3 hours'),
('brand', 1, '*************', 'Mozilla/5.0 (Android 13; Mobile; rv:109.0)', 'https://instagram.com', 'GB', 'mobile', 'Firefox', 'Android', CURRENT_TIMESTAMP - INTERVAL '4 hours');

INSERT INTO search_analytics (query, results_count, user_ip, user_agent, country_code, device_type, search_filters, clicked_result_id, clicked_result_type, searched_at) VALUES
('nike shoes', 45, '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', 'US', 'desktop', '{"category": "sports", "discount_min": 20}', 2, 'coupon', CURRENT_TIMESTAMP - INTERVAL '1 hour'),
('amazon electronics', 78, '*************', 'Mozilla/5.0 (iPhone; CPU iPhone OS 17_0 like Mac OS X)', 'US', 'mobile', '{"brand": "amazon"}', 1, 'coupon', CURRENT_TIMESTAMP - INTERVAL '2 hours'),
('ikea furniture', 23, '*************', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7)', 'CA', 'desktop', '{"category": "home"}', 4, 'deal', CURRENT_TIMESTAMP - INTERVAL '3 hours');

INSERT INTO daily_stats (stat_date, total_coupons, active_coupons, expired_coupons, total_deals, active_deals, expired_deals, total_clicks, total_searches, unique_visitors, top_brand_id, top_category_id, top_country_code) VALUES
(CURRENT_DATE - INTERVAL '1 day', 1250, 1180, 70, 567, 523, 44, 5670, 2340, 1890, 1, 2, 'US'),
(CURRENT_DATE - INTERVAL '2 days', 1245, 1175, 70, 563, 519, 44, 5234, 2156, 1756, 1, 2, 'US'),
(CURRENT_DATE - INTERVAL '3 days', 1240, 1170, 70, 559, 515, 44, 4987, 2034, 1623, 2, 5, 'US');
