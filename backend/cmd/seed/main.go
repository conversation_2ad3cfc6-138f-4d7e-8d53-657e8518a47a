package main

import (
	"database/sql"
	"flag"
	"fmt"
	"io/ioutil"
	"log"
	"path/filepath"
	"strings"

	"maxcoupon-backend/internal/config"

	_ "github.com/lib/pq"
)

func main() {
	var seedFile = flag.String("file", "", "Specific seed file to run (optional)")
	var force = flag.Bool("force", false, "Force seeding even if data exists")
	flag.Parse()

	// Load configuration
	cfg, err := config.Load()
	if err != nil {
		log.Fatalf("Failed to load configuration: %v", err)
	}

	// Connect to database
	db, err := sql.Open("postgres", cfg.Database.GetDSN())
	if err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}
	defer db.Close()

	// Test connection
	if err := db.Ping(); err != nil {
		log.Fatalf("Failed to ping database: %v", err)
	}

	fmt.Println("Starting database seeding...")

	if *seedFile != "" {
		// Run specific seed file
		if err := runSeedFile(db, *seedFile, *force); err != nil {
			log.Fatalf("Failed to run seed file %s: %v", *seedFile, err)
		}
	} else {
		// Run all seed files
		if err := runAllSeedFiles(db, *force); err != nil {
			log.Fatalf("Failed to run seed files: %v", err)
		}
	}

	fmt.Println("Database seeding completed successfully!")
}

func runAllSeedFiles(db *sql.DB, force bool) error {
	seedDir := "database/seeds"
	
	// Get all SQL files in seeds directory
	files, err := filepath.Glob(filepath.Join(seedDir, "*.sql"))
	if err != nil {
		return fmt.Errorf("failed to find seed files: %w", err)
	}

	if len(files) == 0 {
		fmt.Println("No seed files found")
		return nil
	}

	// Sort files to ensure consistent execution order
	for i := 0; i < len(files); i++ {
		for j := i + 1; j < len(files); j++ {
			if files[i] > files[j] {
				files[i], files[j] = files[j], files[i]
			}
		}
	}

	// Execute each seed file
	for _, file := range files {
		fmt.Printf("Running seed file: %s\n", filepath.Base(file))
		if err := runSeedFile(db, file, force); err != nil {
			return fmt.Errorf("failed to run seed file %s: %w", file, err)
		}
	}

	return nil
}

func runSeedFile(db *sql.DB, filename string, force bool) error {
	// Check if we should skip seeding based on existing data
	if !force && shouldSkipSeeding(db, filename) {
		fmt.Printf("Skipping %s - data already exists (use -force to override)\n", filepath.Base(filename))
		return nil
	}

	// Read seed file
	content, err := ioutil.ReadFile(filename)
	if err != nil {
		return fmt.Errorf("failed to read seed file: %w", err)
	}

	// Split content into individual statements
	statements := strings.Split(string(content), ";")

	// Execute each statement
	for _, stmt := range statements {
		stmt = strings.TrimSpace(stmt)
		if stmt == "" || strings.HasPrefix(stmt, "--") {
			continue
		}

		if _, err := db.Exec(stmt); err != nil {
			// Log the error but continue with other statements
			fmt.Printf("Warning: Failed to execute statement in %s: %v\n", filename, err)
			fmt.Printf("Statement: %s\n", stmt[:min(len(stmt), 100)])
		}
	}

	return nil
}

func shouldSkipSeeding(db *sql.DB, filename string) bool {
	// Determine which table to check based on filename
	basename := filepath.Base(filename)
	
	var tableName string
	var countQuery string

	switch {
	case strings.Contains(basename, "countries"):
		tableName = "countries"
	case strings.Contains(basename, "categories"):
		tableName = "categories"
	case strings.Contains(basename, "brands"):
		tableName = "brands"
	case strings.Contains(basename, "coupons"):
		tableName = "coupons"
	case strings.Contains(basename, "deals"):
		tableName = "deals"
	case strings.Contains(basename, "sample_data"):
		// For sample data, check if any coupons exist
		tableName = "coupons"
	default:
		// If we can't determine the table, don't skip
		return false
	}

	countQuery = fmt.Sprintf("SELECT COUNT(*) FROM %s", tableName)

	var count int
	err := db.QueryRow(countQuery).Scan(&count)
	if err != nil {
		// If query fails, don't skip seeding
		return false
	}

	// Skip if table has data
	return count > 0
}

func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}

// Usage examples:
// go run cmd/seed/main.go                                    # Run all seed files
// go run cmd/seed/main.go -force                            # Force run all seed files
// go run cmd/seed/main.go -file=database/seeds/sample_data.sql # Run specific file
