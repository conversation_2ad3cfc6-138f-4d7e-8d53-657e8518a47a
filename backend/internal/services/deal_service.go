package services

import (
	"maxcoupon-backend/internal/models"
	"maxcoupon-backend/internal/repository"
)

// DealService handles deal business logic
type DealService struct {
	repo *repository.DealRepository
}

// NewDealService creates a new deal service
func NewDealService(repo *repository.DealRepository) *DealService {
	return &DealService{repo: repo}
}

// GetDeals returns all deals with pagination
func (s *DealService) GetDeals(limit, offset int) ([]models.Deal, error) {
	if limit <= 0 {
		limit = 20
	}
	if limit > 100 {
		limit = 100
	}

	filters := repository.DealFilters{
		Status: "active",
		Limit:  limit,
		Offset: offset,
	}

	deals, _, err := s.repo.GetAll(filters)
	return deals, err
}

// GetDealByID returns a deal by ID
func (s *DealService) GetDealByID(id uint) (*models.Deal, error) {
	deal, err := s.repo.GetByID(id)
	if err != nil {
		return nil, err
	}

	// Increment view count
	go s.repo.IncrementView(id)

	return deal, nil
}

// GetDealsByBrand returns deals by brand ID
func (s *DealService) GetDealsByBrand(brandID uint, limit, offset int) ([]models.Deal, error) {
	if limit <= 0 {
		limit = 20
	}
	if limit > 100 {
		limit = 100
	}

	filters := repository.DealFilters{
		Status: "active",
		Limit:  limit,
		Offset: offset,
	}

	return s.repo.GetByBrand(brandID, filters)
}

// GetDealsByCategory returns deals by category ID
func (s *DealService) GetDealsByCategory(categoryID uint, limit, offset int) ([]models.Deal, error) {
	if limit <= 0 {
		limit = 20
	}
	if limit > 100 {
		limit = 100
	}

	filters := repository.DealFilters{
		Status: "active",
		Limit:  limit,
		Offset: offset,
	}

	return s.repo.GetByCategory(categoryID, filters)
}

// GetDealsByCountry returns deals by country ID
func (s *DealService) GetDealsByCountry(countryID uint, limit, offset int) ([]models.Deal, error) {
	if limit <= 0 {
		limit = 20
	}
	if limit > 100 {
		limit = 100
	}

	filters := repository.DealFilters{
		Status: "active",
		Limit:  limit,
		Offset: offset,
	}

	return s.repo.GetByCountry(countryID, filters)
}

// GetFeaturedDeals returns featured deals
func (s *DealService) GetFeaturedDeals(limit int) ([]models.Deal, error) {
	if limit <= 0 {
		limit = 10
	}
	if limit > 50 {
		limit = 50
	}

	return s.repo.GetFeatured(limit)
}

// GetHotDeals returns hot deals
func (s *DealService) GetHotDeals(limit int) ([]models.Deal, error) {
	if limit <= 0 {
		limit = 10
	}
	if limit > 50 {
		limit = 50
	}

	return s.repo.GetHotDeals(limit)
}

// GetFlashDeals returns flash sale deals
func (s *DealService) GetFlashDeals(limit int) ([]models.Deal, error) {
	if limit <= 0 {
		limit = 10
	}
	if limit > 50 {
		limit = 50
	}

	return s.repo.GetFlashDeals(limit)
}

// SearchDeals searches deals by query
func (s *DealService) SearchDeals(query string, limit, offset int) ([]models.Deal, error) {
	if limit <= 0 {
		limit = 20
	}
	if limit > 100 {
		limit = 100
	}

	filters := repository.DealFilters{
		Status: "active",
		Limit:  limit,
		Offset: offset,
	}

	deals, _, err := s.repo.Search(query, filters)
	return deals, err
}

// GetPopularDeals returns popular deals (using featured deals as proxy)
func (s *DealService) GetPopularDeals(limit int) ([]models.Deal, error) {
	if limit <= 0 {
		limit = 10
	}
	if limit > 50 {
		limit = 50
	}

	return s.repo.GetFeatured(limit)
}

// GetExpiringDeals returns deals expiring soon (using hot deals as proxy)
func (s *DealService) GetExpiringDeals(hours, limit int) ([]models.Deal, error) {
	if limit <= 0 {
		limit = 10
	}
	if limit > 50 {
		limit = 50
	}

	return s.repo.GetHotDeals(limit)
}

// TrackDealClick tracks deal click
func (s *DealService) TrackDealClick(dealID uint, userIP, userAgent, referrer string) error {
	return s.repo.IncrementClick(dealID)
}

// CreateDeal creates a new deal
func (s *DealService) CreateDeal(deal *models.Deal) error {
	return s.repo.Create(deal)
}

// UpdateDeal updates a deal
func (s *DealService) UpdateDeal(deal *models.Deal) error {
	return s.repo.Update(deal)
}

// DeleteDeal deletes a deal
func (s *DealService) DeleteDeal(id uint) error {
	return s.repo.Delete(id)
}

// GetDealCount returns the total number of deals
func (s *DealService) GetDealCount() (int64, error) {
	filters := repository.DealFilters{Status: "active"}
	_, total, err := s.repo.GetAll(filters)
	return total, err
}

// GetDealCountByBrand returns the number of deals for a brand
func (s *DealService) GetDealCountByBrand(brandID uint) (int64, error) {
	filters := repository.DealFilters{
		Status:  "active",
		BrandID: &brandID,
	}
	_, total, err := s.repo.GetAll(filters)
	return total, err
}

// GetDealCountByCategory returns the number of deals for a category
func (s *DealService) GetDealCountByCategory(categoryID uint) (int64, error) {
	filters := repository.DealFilters{
		Status:     "active",
		CategoryID: &categoryID,
	}
	_, total, err := s.repo.GetAll(filters)
	return total, err
}
