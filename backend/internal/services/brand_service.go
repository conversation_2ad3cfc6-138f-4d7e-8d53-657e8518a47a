package services

import (
	"maxcoupon-backend/internal/models"
	"maxcoupon-backend/internal/repository"
	"strconv"
)

// BrandService handles business logic for brands
type BrandService struct {
	repo *repository.BrandRepository
}

// NewBrandService creates a new brand service
func NewBrandService(repo *repository.BrandRepository) *BrandService {
	return &BrandService{repo: repo}
}

// GetAllBrands returns all active brands with pagination
func (s *BrandService) GetAllBrands(page, limit int, categoryID, countryID *uint) ([]models.Brand, int64, error) {
	return s.repo.GetAllWithPagination(page, limit, categoryID, countryID)
}

// GetBrandByID returns a brand by ID
func (s *BrandService) GetBrandByID(idStr string) (*models.Brand, error) {
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		return nil, err
	}
	return s.repo.GetByID(uint(id))
}

// GetBrandByUniqueName returns a brand by unique name
func (s *BrandService) GetBrandByUniqueName(uniqueName string) (*models.Brand, error) {
	return s.repo.GetByUniqueName(uniqueName)
}

// GetPopularBrands returns popular brands
func (s *BrandService) GetPopularBrands(limit int) ([]models.Brand, error) {
	return s.repo.GetPopular(limit)
}

// GetBrandsByCategory returns brands by category
func (s *BrandService) GetBrandsByCategory(categoryID uint, page, limit int) ([]models.Brand, int64, error) {
	return s.repo.GetByCategory(categoryID, page, limit)
}

// SearchBrands searches brands by name
func (s *BrandService) SearchBrands(query string, page, limit int) ([]models.Brand, int64, error) {
	return s.repo.Search(query, page, limit)
}
