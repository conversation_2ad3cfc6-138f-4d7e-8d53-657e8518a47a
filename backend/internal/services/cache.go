package services

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"maxcoupon-backend/internal/config"

	"github.com/go-redis/redis/v8"
	"github.com/sirupsen/logrus"
)

// CacheService handles caching operations
type CacheService struct {
	client *redis.Client
	config *config.CacheConfig
}

// NewCacheService creates a new cache service
func NewCacheService(client *redis.Client, config *config.CacheConfig) *CacheService {
	return &CacheService{
		client: client,
		config: config,
	}
}

// Set stores a value in cache with TTL
func (cs *CacheService) Set(ctx context.Context, key string, value interface{}, ttl time.Duration) error {
	data, err := json.Marshal(value)
	if err != nil {
		return fmt.Errorf("failed to marshal cache value: %w", err)
	}

	if ttl == 0 {
		ttl = cs.config.DefaultTTL
	}

	return cs.client.Set(ctx, key, data, ttl).Err()
}

// Get retrieves a value from cache
func (cs *CacheService) Get(ctx context.Context, key string, dest interface{}) error {
	data, err := cs.client.Get(ctx, key).Result()
	if err != nil {
		return err
	}

	return json.Unmarshal([]byte(data), dest)
}

// Delete removes a key from cache
func (cs *CacheService) Delete(ctx context.Context, keys ...string) error {
	return cs.client.Del(ctx, keys...).Err()
}

// Exists checks if a key exists in cache
func (cs *CacheService) Exists(ctx context.Context, key string) bool {
	count, err := cs.client.Exists(ctx, key).Result()
	if err != nil {
		logrus.WithError(err).Error("Failed to check cache key existence")
		return false
	}
	return count > 0
}

// Increment increments a counter in cache
func (cs *CacheService) Increment(ctx context.Context, key string) (int64, error) {
	return cs.client.Incr(ctx, key).Result()
}

// SetExpire sets expiration for a key
func (cs *CacheService) SetExpire(ctx context.Context, key string, ttl time.Duration) error {
	return cs.client.Expire(ctx, key, ttl).Err()
}

// GetKeys returns all keys matching a pattern
func (cs *CacheService) GetKeys(ctx context.Context, pattern string) ([]string, error) {
	return cs.client.Keys(ctx, pattern).Result()
}

// FlushPattern deletes all keys matching a pattern
func (cs *CacheService) FlushPattern(ctx context.Context, pattern string) error {
	keys, err := cs.GetKeys(ctx, pattern)
	if err != nil {
		return err
	}

	if len(keys) > 0 {
		return cs.Delete(ctx, keys...)
	}

	return nil
}

// HealthCheck checks if Redis is healthy
func (cs *CacheService) HealthCheck() error {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	return cs.client.Ping(ctx).Err()
}

// GetStats returns cache statistics
func (cs *CacheService) GetStats() map[string]interface{} {
	stats := make(map[string]interface{})

	if poolStats := cs.client.PoolStats(); poolStats != nil {
		stats["pool"] = map[string]interface{}{
			"hits":        poolStats.Hits,
			"misses":      poolStats.Misses,
			"timeouts":    poolStats.Timeouts,
			"total_conns": poolStats.TotalConns,
			"idle_conns":  poolStats.IdleConns,
			"stale_conns": poolStats.StaleConns,
		}
	}

	return stats
}

// Cache key generators
func (cs *CacheService) CouponKey(id uint) string {
	return fmt.Sprintf("coupon:%d", id)
}

func (cs *CacheService) CouponsKey(filters string) string {
	return fmt.Sprintf("coupons:%s", filters)
}

func (cs *CacheService) DealKey(id uint) string {
	return fmt.Sprintf("deal:%d", id)
}

func (cs *CacheService) DealsKey(filters string) string {
	return fmt.Sprintf("deals:%s", filters)
}

func (cs *CacheService) BrandKey(id uint) string {
	return fmt.Sprintf("brand:%d", id)
}

func (cs *CacheService) BrandsKey(filters string) string {
	return fmt.Sprintf("brands:%s", filters)
}

func (cs *CacheService) CategoryKey(id uint) string {
	return fmt.Sprintf("category:%d", id)
}

func (cs *CacheService) CategoriesKey() string {
	return "categories:all"
}

func (cs *CacheService) CountryKey(id uint) string {
	return fmt.Sprintf("country:%d", id)
}

func (cs *CacheService) CountriesKey() string {
	return "countries:all"
}

func (cs *CacheService) SearchKey(query string, filters string) string {
	return fmt.Sprintf("search:%s:%s", query, filters)
}

func (cs *CacheService) PopularSearchesKey() string {
	return "popular_searches"
}

func (cs *CacheService) StatsKey(date string) string {
	return fmt.Sprintf("stats:%s", date)
}
