package services

import (
	"maxcoupon-backend/internal/config"
	"maxcoupon-backend/internal/repository"
)

// ServiceContainer holds all application services
type ServiceContainer struct {
	// Core services
	Country   *CountryService
	Category  *CategoryService
	Brand     *BrandService
	Coupon    *CouponService
	Deal      *DealService
	Search    *SearchService
	Analytics *AnalyticsService
	Cache     *CacheService
	Jobs      *JobService
	
	// Configuration and database
	Config *config.Config
	DB     *repository.Database
}

// NewServiceContainer creates a new service container with all services initialized
func NewServiceContainer(db *repository.Database, cfg *config.Config) *ServiceContainer {
	// Initialize cache service first as other services depend on it
	cacheService := NewCacheService(db.Redis, &cfg.Cache)
	
	// Initialize core services
	countryService := NewCountryService(db.Countries, cacheService)
	categoryService := NewCategoryService(db.Categories, cacheService)
	brandService := NewBrandService(db.Brands, cacheService)
	couponService := NewCouponService(db.Coupons, cacheService)
	dealService := NewDealService(db.Deals, cacheService)
	searchService := NewSearchService(db, cacheService)
	analyticsService := NewAnalyticsService(db.Analytics, cacheService)
	
	// Initialize job service
	jobService := NewJobService(db, &cfg.Jobs)
	
	return &ServiceContainer{
		Country:   countryService,
		Category:  categoryService,
		Brand:     brandService,
		Coupon:    couponService,
		Deal:      dealService,
		Search:    searchService,
		Analytics: analyticsService,
		Cache:     cacheService,
		Jobs:      jobService,
		Config:    cfg,
		DB:        db,
	}
}

// Close gracefully shuts down all services
func (sc *ServiceContainer) Close() error {
	// Stop background jobs
	if sc.Jobs != nil {
		sc.Jobs.StopScheduler()
	}
	
	// Close database connections
	if sc.DB != nil {
		return sc.DB.Close()
	}
	
	return nil
}

// HealthCheck performs health checks on all services
func (sc *ServiceContainer) HealthCheck() map[string]interface{} {
	health := make(map[string]interface{})
	
	// Database health
	if sc.DB != nil {
		if err := sc.DB.HealthCheck(nil); err != nil {
			health["database"] = map[string]interface{}{
				"status": "unhealthy",
				"error":  err.Error(),
			}
		} else {
			health["database"] = map[string]interface{}{
				"status": "healthy",
			}
		}
	}
	
	// Cache health
	if sc.Cache != nil {
		if err := sc.Cache.HealthCheck(); err != nil {
			health["cache"] = map[string]interface{}{
				"status": "unhealthy",
				"error":  err.Error(),
			}
		} else {
			health["cache"] = map[string]interface{}{
				"status": "healthy",
			}
		}
	}
	
	// Jobs health
	if sc.Jobs != nil {
		health["jobs"] = map[string]interface{}{
			"status":  "healthy",
			"running": sc.Jobs.IsRunning(),
		}
	}
	
	return health
}

// GetStats returns statistics from all services
func (sc *ServiceContainer) GetStats() map[string]interface{} {
	stats := make(map[string]interface{})
	
	// Database stats
	if sc.DB != nil {
		stats["database"] = sc.DB.GetStats()
	}
	
	// Cache stats
	if sc.Cache != nil {
		stats["cache"] = sc.Cache.GetStats()
	}
	
	// Jobs stats
	if sc.Jobs != nil {
		stats["jobs"] = sc.Jobs.GetStats()
	}
	
	return stats
}
