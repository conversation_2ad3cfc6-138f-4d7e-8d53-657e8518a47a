package services

import (
	"maxcoupon-backend/internal/config"
	"maxcoupon-backend/internal/repository"
)

// ServiceContainer holds all application services
type ServiceContainer struct {
	// Core services
	Country  *CountryService
	Category *CategoryService
	Brand    *BrandService
	Coupon   *CouponService
	Deal     *DealService

	// Configuration and database
	Config *config.Config
	DB     *repository.Database
}

// NewServiceContainer creates a new service container with all services initialized
func NewServiceContainer(db *repository.Database, cfg *config.Config) *ServiceContainer {
	// Initialize core services
	countryService := NewCountryService(db.Countries)
	categoryService := NewCategoryService(db.Categories)
	brandService := NewBrandService(db.Brands)
	couponService := NewCouponService(db.Coupons)
	dealService := NewDealService(db.Deals)

	return &ServiceContainer{
		Country:  countryService,
		Category: categoryService,
		Brand:    brandService,
		Coupon:   couponService,
		Deal:     dealService,
		Config:   cfg,
		DB:       db,
	}
}

// Close gracefully shuts down all services
func (sc *ServiceContainer) Close() error {
	// Close database connections
	if sc.DB != nil {
		return sc.DB.Close()
	}

	return nil
}

// HealthCheck performs health checks on all services
func (sc *ServiceContainer) HealthCheck() map[string]interface{} {
	health := make(map[string]interface{})

	// Database health
	if sc.DB != nil {
		if err := sc.DB.HealthCheck(nil); err != nil {
			health["database"] = map[string]interface{}{
				"status": "unhealthy",
				"error":  err.Error(),
			}
		} else {
			health["database"] = map[string]interface{}{
				"status": "healthy",
			}
		}
	}

	return health
}

// GetStats returns statistics from all services
func (sc *ServiceContainer) GetStats() map[string]interface{} {
	stats := make(map[string]interface{})

	// Database stats
	if sc.DB != nil {
		stats["database"] = sc.DB.GetStats()
	}

	return stats
}
