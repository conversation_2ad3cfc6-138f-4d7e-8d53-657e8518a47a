package services

import (
	"maxcoupon-backend/internal/models"
	"maxcoupon-backend/internal/repository"
	"strconv"
)

// CategoryService handles business logic for categories
type CategoryService struct {
	repo *repository.CategoryRepository
}

// NewCategoryService creates a new category service
func NewCategoryService(repo *repository.CategoryRepository) *CategoryService {
	return &CategoryService{repo: repo}
}

// GetAllCategories returns all active categories
func (s *CategoryService) GetAllCategories() ([]models.Category, error) {
	return s.repo.GetAll()
}

// GetCategoryByID returns a category by ID
func (s *CategoryService) GetCategoryByID(idStr string) (*models.Category, error) {
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		return nil, err
	}
	return s.repo.GetByID(uint(id))
}

// GetCategoryBySlug returns a category by slug
func (s *CategoryService) GetCategoryBySlug(slug string) (*models.Category, error) {
	return s.repo.GetBySlug(slug)
}

// CreateCategory creates a new category
func (s *CategoryService) CreateCategory(category *models.Category) error {
	return s.repo.Create(category)
}

// UpdateCategory updates a category
func (s *CategoryService) UpdateCategory(category *models.Category) error {
	return s.repo.Update(category)
}

// DeleteCategory soft deletes a category
func (s *CategoryService) DeleteCategory(idStr string) error {
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		return err
	}
	return s.repo.Delete(uint(id))
}
