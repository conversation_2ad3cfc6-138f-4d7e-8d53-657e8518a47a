package services

import (
	"maxcoupon-backend/internal/models"
	"maxcoupon-backend/internal/repository"
	"strconv"
)

// CouponService handles business logic for coupons
type CouponService struct {
	repo *repository.CouponRepository
}

// NewCouponService creates a new coupon service
func NewCouponService(repo *repository.CouponRepository) *CouponService {
	return &CouponService{repo: repo}
}

// GetAllCoupons returns all active coupons with pagination
func (s *CouponService) GetAllCoupons(page, limit int, brandID, categoryID, countryID *uint, featured, exclusive *bool) ([]models.Coupon, int64, error) {
	return s.repo.GetAllWithPagination(page, limit, brandID, categoryID, countryID, featured, exclusive)
}

// GetCouponByID returns a coupon by ID
func (s *CouponService) GetCouponByID(idStr string) (*models.Coupon, error) {
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		return nil, err
	}
	return s.repo.GetByID(uint(id))
}

// GetFeaturedCoupons returns featured coupons
func (s *CouponService) GetFeaturedCoupons(limit int) ([]models.Coupon, error) {
	return s.repo.GetFeatured(limit)
}

// GetCouponsByBrand returns coupons by brand
func (s *CouponService) GetCouponsByBrand(brandID uint, page, limit int) ([]models.Coupon, int64, error) {
	return s.repo.GetByBrand(brandID, page, limit)
}

// GetCouponsByCategory returns coupons by category
func (s *CouponService) GetCouponsByCategory(categoryID uint, page, limit int) ([]models.Coupon, int64, error) {
	return s.repo.GetByCategory(categoryID, page, limit)
}

// SearchCoupons searches coupons
func (s *CouponService) SearchCoupons(query string, page, limit int) ([]models.Coupon, int64, error) {
	return s.repo.Search(query, page, limit)
}

// TrackCouponClick tracks coupon click
func (s *CouponService) TrackCouponClick(couponID uint, userIP, userAgent, referrer string) error {
	return s.repo.TrackClick(couponID, userIP, userAgent, referrer)
}
