package services

import (
	"maxcoupon-backend/internal/models"
	"maxcoupon-backend/internal/repository"
	"strconv"
)

// CouponService handles business logic for coupons
type CouponService struct {
	repo *repository.CouponRepository
}

// NewCouponService creates a new coupon service
func NewCouponService(repo *repository.CouponRepository) *CouponService {
	return &CouponService{repo: repo}
}

// GetAllCoupons returns all active coupons with pagination
func (s *CouponService) GetAllCoupons(page, limit int, brandID, categoryID, countryID *uint, featured, exclusive *bool) ([]models.Coupon, int64, error) {
	filters := repository.CouponFilters{
		Limit:  limit,
		Offset: (page - 1) * limit,
		Status: "active",
	}

	if brandID != nil {
		filters.BrandID = brandID
	}
	if categoryID != nil {
		filters.CategoryID = categoryID
	}
	if countryID != nil {
		filters.CountryID = countryID
	}
	if featured != nil {
		filters.IsFeatured = featured
	}

	return s.repo.GetAll(filters)
}

// GetCouponByID returns a coupon by ID
func (s *CouponService) GetCouponByID(idStr string) (*models.Coupon, error) {
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		return nil, err
	}
	return s.repo.GetByID(uint(id))
}

// GetFeaturedCoupons returns featured coupons
func (s *CouponService) GetFeaturedCoupons(limit int) ([]models.Coupon, error) {
	return s.repo.GetFeatured(limit)
}

// GetCouponsByBrand returns coupons by brand
func (s *CouponService) GetCouponsByBrand(brandID uint, page, limit int) ([]models.Coupon, int64, error) {
	filters := repository.CouponFilters{
		Limit:  limit,
		Offset: (page - 1) * limit,
		Status: "active",
	}
	coupons, err := s.repo.GetByBrand(brandID, filters)
	return coupons, int64(len(coupons)), err
}

// GetCouponsByCategory returns coupons by category
func (s *CouponService) GetCouponsByCategory(categoryID uint, page, limit int) ([]models.Coupon, int64, error) {
	filters := repository.CouponFilters{
		Limit:  limit,
		Offset: (page - 1) * limit,
		Status: "active",
	}
	coupons, err := s.repo.GetByCategory(categoryID, filters)
	return coupons, int64(len(coupons)), err
}

// SearchCoupons searches coupons with optional filters
func (s *CouponService) SearchCoupons(query string, brandID, categoryID, countryID *uint, page, limit int) ([]models.Coupon, int64, error) {
	filters := repository.CouponFilters{
		Search: query,
		Limit:  limit,
		Offset: (page - 1) * limit,
		Status: "active",
	}

	if brandID != nil {
		filters.BrandID = brandID
	}
	if categoryID != nil {
		filters.CategoryID = categoryID
	}
	if countryID != nil {
		filters.CountryID = countryID
	}

	return s.repo.Search(query, filters)
}

// GetPopularCoupons returns popular coupons based on click count and popularity score
func (s *CouponService) GetPopularCoupons(limit int) ([]models.Coupon, error) {
	return s.repo.GetPopular(limit)
}

// TrackCouponClick tracks coupon click
func (s *CouponService) TrackCouponClick(couponID uint, userIP, userAgent, referrer string) error {
	return s.repo.IncrementClick(couponID)
}
