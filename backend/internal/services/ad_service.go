package services

import (
	"fmt"
	"time"

	"maxcoupon-backend/internal/models"

	"gorm.io/gorm"
)

type AdService struct {
	db *gorm.DB
}

func NewAdService(db *gorm.DB) *AdService {
	return &AdService{db: db}
}

// GetActiveAdForModal returns the best ad to show in modal based on click rate
// This implements the smart rotation logic to keep click rate under 30%
func (s *AdService) GetActiveAdForModal() (*models.AdCode, error) {
	var ads []models.AdCode

	// Get all active modal ads ordered by priority and click rate
	err := s.db.Where("is_active = ? AND position = ?", true, "modal").
		Order("priority DESC, click_rate ASC").
		Find(&ads).Error

	if err != nil {
		return nil, err
	}

	if len(ads) == 0 {
		return nil, fmt.Errorf("no active modal ads found")
	}

	// Find the first ad with click rate under max_click_rate
	for _, ad := range ads {
		if ad.ClickRate < ad.MaxClickRate {
			return &ad, nil
		}
	}

	// If all ads exceed max click rate, return the one with lowest click rate
	// This ensures we always have an ad to show
	return &ads[0], nil
}

// GetOptimalAds returns the optimal ads based on click rate
// This method completely handles ad selection logic on backend
func (s *AdService) GetOptimalAds(position string, count int) ([]models.AdCode, error) {
	var ads []models.AdCode

	// Get all active ads for the specified position ordered by priority and click rate
	err := s.db.Where("is_active = ? AND position = ?", true, position).
		Order("priority DESC, click_rate ASC").
		Find(&ads).Error

	if err != nil {
		return nil, err
	}

	if len(ads) == 0 {
		return nil, fmt.Errorf("no active ads found for position: %s", position)
	}

	// Select optimal ads based on click rate
	var selectedAds []models.AdCode

	// First, try to get ads with click rate under max_click_rate
	for _, ad := range ads {
		if len(selectedAds) >= count {
			break
		}
		if ad.ClickRate < ad.MaxClickRate {
			selectedAds = append(selectedAds, ad)
		}
	}

	// If we don't have enough ads under max click rate, fill with lowest click rate ads
	if len(selectedAds) < count {
		for _, ad := range ads {
			if len(selectedAds) >= count {
				break
			}
			// Check if this ad is already selected
			alreadySelected := false
			for _, selected := range selectedAds {
				if selected.ID == ad.ID {
					alreadySelected = true
					break
				}
			}
			if !alreadySelected {
				selectedAds = append(selectedAds, ad)
			}
		}
	}

	return selectedAds, nil
}

// RecordAdShow records when an ad is shown to user (when modal opens)
func (s *AdService) RecordAdShow(adCodeID uint, entityType string, entityID uint, userIP, userAgent, referrer string) error {
	// Record interaction
	interaction := models.AdInteraction{
		AdCodeID:   adCodeID,
		EntityType: entityType,
		EntityID:   entityID,
		UserIP:     userIP,
		UserAgent:  userAgent,
		Action:     "show",
		Referrer:   referrer,
	}

	if err := s.db.Create(&interaction).Error; err != nil {
		return err
	}

	// Update ad statistics
	return s.updateAdStats(adCodeID)
}

// RecordAdClick records when an ad is clicked by user
func (s *AdService) RecordAdClick(adCodeID uint, entityType string, entityID uint, userIP, userAgent, referrer string) error {
	// Record interaction
	interaction := models.AdInteraction{
		AdCodeID:   adCodeID,
		EntityType: entityType,
		EntityID:   entityID,
		UserIP:     userIP,
		UserAgent:  userAgent,
		Action:     "click",
		Referrer:   referrer,
	}

	if err := s.db.Create(&interaction).Error; err != nil {
		return err
	}

	// Update ad statistics
	return s.updateAdStats(adCodeID)
}

// updateAdStats recalculates and updates ad statistics
func (s *AdService) updateAdStats(adCodeID uint) error {
	var showCount, clickCount int64

	// Count shows
	s.db.Model(&models.AdInteraction{}).
		Where("ad_code_id = ? AND action = ?", adCodeID, "show").
		Count(&showCount)

	// Count clicks
	s.db.Model(&models.AdInteraction{}).
		Where("ad_code_id = ? AND action = ?", adCodeID, "click").
		Count(&clickCount)

	// Calculate click rate
	var clickRate float64
	if showCount > 0 {
		clickRate = (float64(clickCount) / float64(showCount)) * 100
	}

	// Update ad code statistics
	now := time.Now()
	updates := map[string]interface{}{
		"show_count":  showCount,
		"click_count": clickCount,
		"click_rate":  clickRate,
		"updated_at":  now,
	}

	// Update last_shown_at or last_clicked_at based on latest interaction
	var lastInteraction models.AdInteraction
	if err := s.db.Where("ad_code_id = ?", adCodeID).
		Order("created_at DESC").
		First(&lastInteraction).Error; err == nil {

		if lastInteraction.Action == "show" {
			updates["last_shown_at"] = lastInteraction.CreatedAt
		} else if lastInteraction.Action == "click" {
			updates["last_clicked_at"] = lastInteraction.CreatedAt
		}
	}

	return s.db.Model(&models.AdCode{}).
		Where("id = ?", adCodeID).
		Updates(updates).Error
}

// GetAdStats returns statistics for all ads
func (s *AdService) GetAdStats() ([]models.AdCode, error) {
	var ads []models.AdCode
	err := s.db.Order("priority DESC, click_rate ASC").Find(&ads).Error
	return ads, err
}

// CreateAdCode creates a new ad code
func (s *AdService) CreateAdCode(ad *models.AdCode) error {
	return s.db.Create(ad).Error
}

// UpdateAdCode updates an existing ad code
func (s *AdService) UpdateAdCode(id uint, updates map[string]interface{}) error {
	return s.db.Model(&models.AdCode{}).Where("id = ?", id).Updates(updates).Error
}

// DeleteAdCode soft deletes an ad code
func (s *AdService) DeleteAdCode(id uint) error {
	return s.db.Delete(&models.AdCode{}, id).Error
}

// GetAdByID returns an ad by ID
func (s *AdService) GetAdByID(id uint) (*models.AdCode, error) {
	var ad models.AdCode
	err := s.db.First(&ad, id).Error
	if err != nil {
		return nil, err
	}
	return &ad, nil
}

// InitializeDefaultAds creates default ad codes if none exist
func (s *AdService) InitializeDefaultAds() error {
	var count int64
	s.db.Model(&models.AdCode{}).Where("position = ?", "modal").Count(&count)

	if count == 0 {
		defaultAds := []models.AdCode{
			{
				Name:         "AdSense Banner 1",
				AdCode:       "<!-- AdSense Code 1 -->",
				AdType:       "adsense",
				Position:     "modal",
				IsActive:     true,
				Priority:     1,
				MaxClickRate: 30.0,
			},
			{
				Name:         "AdSense Banner 2",
				AdCode:       "<!-- AdSense Code 2 -->",
				AdType:       "adsense",
				Position:     "modal",
				IsActive:     true,
				Priority:     2,
				MaxClickRate: 30.0,
			},
			{
				Name:         "AdSense Banner 3",
				AdCode:       "<!-- AdSense Code 3 -->",
				AdType:       "adsense",
				Position:     "modal",
				IsActive:     true,
				Priority:     3,
				MaxClickRate: 30.0,
			},
		}

		for _, ad := range defaultAds {
			if err := s.db.Create(&ad).Error; err != nil {
				return err
			}
		}
	}

	return nil
}
