package services

import (
	"maxcoupon-backend/internal/models"
	"maxcoupon-backend/internal/repository"
	"strconv"
)

// CountryService handles business logic for countries
type CountryService struct {
	repo *repository.CountryRepository
}

// NewCountryService creates a new country service
func NewCountryService(repo *repository.CountryRepository) *CountryService {
	return &CountryService{repo: repo}
}

// GetAllCountries returns all active countries
func (s *CountryService) GetAllCountries() ([]models.Country, error) {
	return s.repo.GetAll()
}

// GetCountryByID returns a country by ID
func (s *CountryService) GetCountryByID(idStr string) (*models.Country, error) {
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		return nil, err
	}
	return s.repo.GetByID(uint(id))
}

// GetCountryByCode returns a country by code
func (s *CountryService) GetCountryByCode(code string) (*models.Country, error) {
	return s.repo.GetByCode(code)
}
