package models

import (
	"database/sql/driver"
	"encoding/json"
	"errors"
	"strings"
	"time"

	"github.com/lib/pq"
)

// Base model with common fields
type BaseModel struct {
	ID        uint      `json:"id" gorm:"primaryKey"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

// Country represents a country entity
type Country struct {
	BaseModel
	Name     string `json:"name" gorm:"size:100;not null;uniqueIndex"`
	Code     string `json:"code" gorm:"size:3;not null;uniqueIndex"`
	Flag     string `json:"flag" gorm:"size:10;not null"` // Country flag emoji
	Currency string `json:"currency" gorm:"size:3;not null"`
	Locale   string `json:"locale" gorm:"size:10;not null"`   // e.g., en-US, de-DE
	Timezone string `json:"timezone" gorm:"size:50;not null"` // e.g., America/New_York
	Status   string `json:"status" gorm:"size:20;not null;default:active"`
}

// Category represents a category entity (single level only)
type Category struct {
	BaseModel
	Name        string `json:"name" gorm:"size:100;not null"`
	Description string `json:"description" gorm:"type:text"`
	Slug        string `json:"slug" gorm:"size:120;not null;uniqueIndex"`
	Icon        string `json:"icon" gorm:"size:100"`
	Color       string `json:"color" gorm:"size:7"`
	SortOrder   int    `json:"sort_order" gorm:"default:0"`
	Status      string `json:"status" gorm:"size:20;not null;default:active"`
}

// StringArray is a custom type for PostgreSQL string arrays
type StringArray []string

// Scan implements the Scanner interface for database/sql
func (a *StringArray) Scan(value interface{}) error {
	if value == nil {
		*a = nil
		return nil
	}

	switch v := value.(type) {
	case []byte:
		// Handle PostgreSQL array format like {item1,item2,item3}
		str := string(v)
		if str == "{}" || str == "" {
			*a = StringArray{}
			return nil
		}
		// Remove braces and split by comma
		str = strings.Trim(str, "{}")
		if str == "" {
			*a = StringArray{}
			return nil
		}
		*a = StringArray(strings.Split(str, ","))
		return nil
	case string:
		// Handle string representation
		if v == "{}" || v == "" {
			*a = StringArray{}
			return nil
		}
		v = strings.Trim(v, "{}")
		if v == "" {
			*a = StringArray{}
			return nil
		}
		*a = StringArray(strings.Split(v, ","))
		return nil
	default:
		return pq.Array(a).Scan(value)
	}
}

// Value implements the driver Valuer interface
func (a StringArray) Value() (driver.Value, error) {
	if a == nil {
		return nil, nil
	}
	return pq.Array(a).Value()
}

// JSONMap is a custom type for JSONB fields
type JSONMap map[string]interface{}

// Scan implements the Scanner interface for database/sql
func (j *JSONMap) Scan(value interface{}) error {
	if value == nil {
		*j = nil
		return nil
	}

	bytes, ok := value.([]byte)
	if !ok {
		return errors.New("type assertion to []byte failed")
	}

	return json.Unmarshal(bytes, j)
}

// Value implements the driver Valuer interface
func (j JSONMap) Value() (driver.Value, error) {
	if j == nil {
		return nil, nil
	}
	return json.Marshal(j)
}

// Brand represents a brand entity
type Brand struct {
	BaseModel
	Name            string      `json:"name" gorm:"size:100;not null"`
	UniqueName      string      `json:"unique_name" gorm:"size:100;not null;uniqueIndex"`
	Description     string      `json:"description" gorm:"type:text"`
	LogoURL         string      `json:"logo_url" gorm:"size:500"`
	WebsiteURL      string      `json:"website_url" gorm:"size:500"`
	AffiliateURL    string      `json:"affiliate_url" gorm:"size:500"`
	CommissionRate  *float64    `json:"commission_rate" gorm:"type:decimal(5,2)"`
	PopularityScore int         `json:"popularity_score" gorm:"default:0"`
	AverageDiscount *float64    `json:"average_discount" gorm:"type:decimal(5,2)"`
	TotalCoupons    int         `json:"total_coupons" gorm:"default:0"`
	TotalDeals      int         `json:"total_deals" gorm:"default:0"`
	LastUpdated     *time.Time  `json:"last_updated"`
	CategoryID      *uint       `json:"category_id"`                       // Reference to category
	CountryID       *uint       `json:"country_id"`                        // Reference to country
	PlatformType    string      `json:"platform_type" gorm:"size:50"`      // awin, impact, etc.
	PlatformBrandID string      `json:"platform_brand_id" gorm:"size:100"` // Platform's brand ID
	Tags            StringArray `json:"tags" gorm:"type:text[]"`
	Status          string      `json:"status" gorm:"size:20;not null;default:active"`
}

// Coupon represents a coupon entity
type Coupon struct {
	BaseModel
	Title                 string     `json:"title" gorm:"size:200;not null"`
	Description           string     `json:"description" gorm:"type:text"`
	Code                  *string    `json:"code" gorm:"size:50"`
	DiscountType          string     `json:"discount_type" gorm:"size:20;not null"`
	DiscountValue         *float64   `json:"discount_value" gorm:"type:decimal(10,2)"`
	MinimumOrderAmount    *float64   `json:"minimum_order_amount" gorm:"type:decimal(10,2)"`
	MaximumDiscountAmount *float64   `json:"maximum_discount_amount" gorm:"type:decimal(10,2)"`
	BrandID               uint       `json:"brand_id" gorm:"not null"`
	Brand                 Brand      `json:"brand" gorm:"foreignKey:BrandID"`
	CategoryID            *uint      `json:"category_id"`
	Category              *Category  `json:"category" gorm:"foreignKey:CategoryID"`
	CountryID             *uint      `json:"country_id"`
	Country               *Country   `json:"country" gorm:"foreignKey:CountryID"`
	StartDate             time.Time  `json:"start_date" gorm:"default:CURRENT_TIMESTAMP"`
	ExpiryDate            time.Time  `json:"expiry_date" gorm:"not null"`
	IsFeatured            bool       `json:"is_featured" gorm:"default:false"`
	IsExclusive           bool       `json:"is_exclusive" gorm:"default:false"`
	IsVerified            bool       `json:"is_verified" gorm:"default:false"`
	PopularityScore       int        `json:"popularity_score" gorm:"default:0"`
	ClickCount            int        `json:"click_count" gorm:"default:0"`
	SuccessRate           float64    `json:"success_rate" gorm:"type:decimal(5,2);default:0.0"`
	LastVerified          *time.Time `json:"last_verified"`
	MetaTitle             string     `json:"meta_title" gorm:"size:200"`
	MetaDescription       string     `json:"meta_description" gorm:"type:text"`
	ImageURL              string     `json:"image_url" gorm:"size:500"`
	Status                string     `json:"status" gorm:"size:20;not null;default:active"`
}

// Deal represents a deal entity (simplified)
type Deal struct {
	BaseModel
	Title              string     `json:"title" gorm:"size:200;not null"`
	Description        string     `json:"description" gorm:"type:text"`
	OriginalPrice      *float64   `json:"original_price" gorm:"type:decimal(10,2)"`
	SalePrice          *float64   `json:"sale_price" gorm:"type:decimal(10,2)"`
	DiscountPercentage *float64   `json:"discount_percentage" gorm:"type:decimal(5,2)"`
	Currency           string     `json:"currency" gorm:"size:3;default:USD"`
	ProductURL         string     `json:"product_url" gorm:"type:text"`
	ImageURL           string     `json:"image_url" gorm:"size:500"`
	BrandID            uint       `json:"brand_id" gorm:"not null"`
	CategoryID         *uint      `json:"category_id"`
	CountryID          *uint      `json:"country_id"`
	StartDate          time.Time  `json:"start_date" gorm:"default:CURRENT_TIMESTAMP"`
	EndDate            *time.Time `json:"end_date"`
	IsFeatured         bool       `json:"is_featured" gorm:"default:false"`
	IsHotDeal          bool       `json:"is_hot_deal" gorm:"default:false"`
	IsFlashSale        bool       `json:"is_flash_sale" gorm:"default:false"`
	DealType           string     `json:"deal_type" gorm:"size:30;default:sale"`
	PopularityScore    int        `json:"popularity_score" gorm:"default:0"`
	ClickCount         int        `json:"click_count" gorm:"default:0"`
	ViewCount          int        `json:"view_count" gorm:"default:0"`
	IsVerified         bool       `json:"is_verified" gorm:"default:false"`
	MetaTitle          string     `json:"meta_title" gorm:"size:200"`
	MetaDescription    string     `json:"meta_description" gorm:"type:text"`
	Status             string     `json:"status" gorm:"size:20;not null;default:active"`
}

// ClickAnalytics represents click tracking data
type ClickAnalytics struct {
	ID          uint      `json:"id" gorm:"primaryKey"`
	EntityType  string    `json:"entity_type" gorm:"size:20;not null"`
	EntityID    uint      `json:"entity_id" gorm:"not null"`
	UserIP      string    `json:"user_ip" gorm:"type:inet"`
	UserAgent   string    `json:"user_agent" gorm:"type:text"`
	ReferrerURL string    `json:"referrer_url" gorm:"type:text"`
	CountryCode string    `json:"country_code" gorm:"size:3"`
	DeviceType  string    `json:"device_type" gorm:"size:20"`
	Browser     string    `json:"browser" gorm:"size:50"`
	OS          string    `json:"os" gorm:"size:50"`
	ClickedAt   time.Time `json:"clicked_at" gorm:"default:CURRENT_TIMESTAMP"`
}

// SearchAnalytics represents search tracking data
type SearchAnalytics struct {
	ID                uint      `json:"id" gorm:"primaryKey"`
	Query             string    `json:"query" gorm:"type:text;not null"`
	ResultsCount      int       `json:"results_count" gorm:"default:0"`
	UserIP            string    `json:"user_ip" gorm:"type:inet"`
	UserAgent         string    `json:"user_agent" gorm:"type:text"`
	CountryCode       string    `json:"country_code" gorm:"size:3"`
	DeviceType        string    `json:"device_type" gorm:"size:20"`
	SearchFilters     JSONMap   `json:"search_filters" gorm:"type:jsonb"`
	ClickedResultID   *uint     `json:"clicked_result_id"`
	ClickedResultType string    `json:"clicked_result_type" gorm:"size:20"`
	SearchedAt        time.Time `json:"searched_at" gorm:"default:CURRENT_TIMESTAMP"`
}

// DailyStats represents daily statistics
type DailyStats struct {
	ID             uint      `json:"id" gorm:"primaryKey"`
	StatDate       time.Time `json:"stat_date" gorm:"type:date;not null;uniqueIndex"`
	TotalCoupons   int       `json:"total_coupons" gorm:"default:0"`
	ActiveCoupons  int       `json:"active_coupons" gorm:"default:0"`
	ExpiredCoupons int       `json:"expired_coupons" gorm:"default:0"`
	TotalDeals     int       `json:"total_deals" gorm:"default:0"`
	ActiveDeals    int       `json:"active_deals" gorm:"default:0"`
	ExpiredDeals   int       `json:"expired_deals" gorm:"default:0"`
	TotalClicks    int       `json:"total_clicks" gorm:"default:0"`
	TotalSearches  int       `json:"total_searches" gorm:"default:0"`
	UniqueVisitors int       `json:"unique_visitors" gorm:"default:0"`
	TopBrandID     *uint     `json:"top_brand_id"`
	TopCategoryID  *uint     `json:"top_category_id"`
	TopCountryCode string    `json:"top_country_code" gorm:"size:3"`
	CreatedAt      time.Time `json:"created_at" gorm:"default:CURRENT_TIMESTAMP"`
}
