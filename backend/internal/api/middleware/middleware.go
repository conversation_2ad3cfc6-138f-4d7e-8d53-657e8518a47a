package middleware

import (
	"net/http"
	"strconv"
	"strings"
	"time"

	"maxcoupon-backend/internal/config"

	"github.com/gin-contrib/cors"
	"github.com/gin-contrib/gzip"
	"github.com/gin-contrib/requestid"
	"github.com/gin-gonic/gin"
	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promauto"
	"github.com/sirupsen/logrus"
	"golang.org/x/time/rate"
)

// Prometheus metrics
var (
	httpRequestsTotal = promauto.NewCounterVec(
		prometheus.CounterOpts{
			Name: "http_requests_total",
			Help: "Total number of HTTP requests",
		},
		[]string{"method", "endpoint", "status"},
	)

	httpRequestDuration = promauto.NewHistogramVec(
		prometheus.HistogramOpts{
			Name:    "http_request_duration_seconds",
			Help:    "HTTP request duration in seconds",
			Buckets: prometheus.DefBuckets,
		},
		[]string{"method", "endpoint"},
	)

	activeConnections = promauto.NewGauge(
		prometheus.GaugeOpts{
			Name: "http_active_connections",
			Help: "Number of active HTTP connections",
		},
	)
)

// SetupMiddleware configures all middleware for the Gin router
func SetupMiddleware(router *gin.Engine, cfg *config.Config) {
	// Request ID middleware
	router.Use(requestid.New())

	// CORS middleware
	router.Use(setupCORS(&cfg.CORS))

	// Gzip compression middleware
	router.Use(gzip.Gzip(gzip.DefaultCompression))

	// Logging middleware
	router.Use(LoggingMiddleware())

	// Rate limiting middleware
	router.Use(RateLimitMiddleware(cfg.RateLimit.RequestsPerMinute, cfg.RateLimit.Burst))

	// Security headers middleware
	router.Use(SecurityHeadersMiddleware())

	// Error handling middleware
	router.Use(ErrorHandlingMiddleware())

	// Recovery middleware (should be last)
	router.Use(gin.Recovery())
}

// setupCORS configures CORS middleware
func setupCORS(cfg *config.CORSConfig) gin.HandlerFunc {
	corsConfig := cors.Config{
		AllowOrigins:     cfg.AllowedOrigins,
		AllowMethods:     cfg.AllowedMethods,
		AllowHeaders:     cfg.AllowedHeaders,
		AllowCredentials: cfg.AllowCredentials,
		MaxAge:           12 * time.Hour,
	}

	// Allow specific origins in development
	if gin.Mode() == gin.DebugMode {
		corsConfig.AllowOrigins = []string{"http://localhost:3000", "http://localhost:4321", "http://localhost:4322"}
	}

	return cors.New(corsConfig)
}

// LoggingMiddleware provides structured logging for HTTP requests
func LoggingMiddleware() gin.HandlerFunc {
	return gin.LoggerWithFormatter(func(param gin.LogFormatterParams) string {
		logrus.WithFields(logrus.Fields{
			"timestamp":  param.TimeStamp.Format(time.RFC3339),
			"status":     param.StatusCode,
			"latency":    param.Latency,
			"client_ip":  param.ClientIP,
			"method":     param.Method,
			"path":       param.Path,
			"user_agent": param.Request.UserAgent(),
			"request_id": param.Request.Header.Get("X-Request-ID"),
			"error":      param.ErrorMessage,
		}).Info("HTTP Request")
		return ""
	})
}

// RateLimitMiddleware implements rate limiting
func RateLimitMiddleware(requestsPerMinute, burst int) gin.HandlerFunc {
	limiter := rate.NewLimiter(rate.Limit(requestsPerMinute)/60, burst)

	return func(c *gin.Context) {
		if !limiter.Allow() {
			c.JSON(http.StatusTooManyRequests, gin.H{
				"error":   "Rate limit exceeded",
				"message": "Too many requests. Please try again later.",
			})
			c.Abort()
			return
		}
		c.Next()
	}
}

// SecurityHeadersMiddleware adds security headers
func SecurityHeadersMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Header("X-Content-Type-Options", "nosniff")
		c.Header("X-Frame-Options", "DENY")
		c.Header("X-XSS-Protection", "1; mode=block")
		c.Header("Referrer-Policy", "strict-origin-when-cross-origin")
		c.Header("Content-Security-Policy", "default-src 'self'")
		c.Next()
	}
}

// ErrorHandlingMiddleware provides centralized error handling
func ErrorHandlingMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Next()

		// Handle any errors that occurred during request processing
		if len(c.Errors) > 0 {
			err := c.Errors.Last()

			logrus.WithFields(logrus.Fields{
				"error":      err.Error(),
				"path":       c.Request.URL.Path,
				"method":     c.Request.Method,
				"request_id": c.GetHeader("X-Request-ID"),
			}).Error("Request error")

			// Don't override status if already set
			if c.Writer.Status() == http.StatusOK {
				c.JSON(http.StatusInternalServerError, gin.H{
					"error":   "Internal server error",
					"message": "An unexpected error occurred",
				})
			}
		}
	}
}

// APIKeyMiddleware validates API key for protected endpoints
func APIKeyMiddleware(validAPIKey string) gin.HandlerFunc {
	return func(c *gin.Context) {
		apiKey := c.GetHeader("X-API-Key")
		if apiKey == "" {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error":   "Missing API key",
				"message": "API key is required",
			})
			c.Abort()
			return
		}

		if apiKey != validAPIKey {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error":   "Invalid API key",
				"message": "The provided API key is invalid",
			})
			c.Abort()
			return
		}

		c.Next()
	}
}

// AdminMiddleware validates admin API key for admin endpoints
func AdminMiddleware(adminAPIKey string) gin.HandlerFunc {
	return func(c *gin.Context) {
		apiKey := c.GetHeader("X-API-Key")
		if apiKey == "" {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error":   "Missing admin API key",
				"message": "Admin API key is required",
			})
			c.Abort()
			return
		}

		if apiKey != adminAPIKey {
			c.JSON(http.StatusForbidden, gin.H{
				"error":   "Invalid admin API key",
				"message": "Admin access required",
			})
			c.Abort()
			return
		}

		c.Next()
	}
}

// CacheMiddleware adds cache headers for static content
func CacheMiddleware(maxAge time.Duration) gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Header("Cache-Control", "public, max-age="+strconv.Itoa(int(maxAge.Seconds())))
		c.Next()
	}
}

// NoCacheMiddleware adds no-cache headers for dynamic content
func NoCacheMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Header("Cache-Control", "no-cache, no-store, must-revalidate")
		c.Header("Pragma", "no-cache")
		c.Header("Expires", "0")
		c.Next()
	}
}

// DeviceDetectionMiddleware detects device type from User-Agent
func DeviceDetectionMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		userAgent := strings.ToLower(c.GetHeader("User-Agent"))

		var deviceType string
		if strings.Contains(userAgent, "mobile") || strings.Contains(userAgent, "android") || strings.Contains(userAgent, "iphone") {
			deviceType = "mobile"
		} else if strings.Contains(userAgent, "tablet") || strings.Contains(userAgent, "ipad") {
			deviceType = "tablet"
		} else {
			deviceType = "desktop"
		}

		c.Set("device_type", deviceType)
		c.Next()
	}
}

// CountryDetectionMiddleware detects country from IP or headers
func CountryDetectionMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Try to get country from CloudFlare header
		country := c.GetHeader("CF-IPCountry")

		// Try to get country from other common headers
		if country == "" {
			country = c.GetHeader("X-Country-Code")
		}

		// Default to US if no country detected
		if country == "" {
			country = "US"
		}

		c.Set("country_code", strings.ToUpper(country))
		c.Next()
	}
}
