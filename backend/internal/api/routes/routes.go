package routes

import (
	"maxcoupon-backend/internal/api/handlers"
	"maxcoupon-backend/internal/api/middleware"
	"maxcoupon-backend/internal/config"
	"maxcoupon-backend/internal/services"

	"github.com/gin-gonic/gin"
)

// SetupRoutes configures all API routes
func SetupRoutes(router *gin.Engine, services *services.ServiceContainer, cfg *config.Config) {
	// Initialize handlers
	h := handlers.NewHandlers(services)

	// API v1 routes
	v1 := router.Group("/api/v1")
	{
		// Public routes (no authentication required)
		setupPublicRoutes(v1, h)

		// Protected routes (API key required)
		protected := v1.Group("")
		protected.Use(middleware.APIKeyMiddleware(cfg.Security.AdminAPIKey))
		setupProtectedRoutes(protected, h)

		// Admin routes (admin API key required)
		admin := v1.Group("/admin")
		admin.Use(middleware.AdminMiddleware(cfg.Security.AdminAPIKey))
		setupAdminRoutes(admin, h)
	}

	// Legacy API routes (for backward compatibility)
	api := router.Group("/api")
	{
		setupLegacyRoutes(api, h)
	}
}

// setupPublicRoutes configures public API routes
func setupPublicRoutes(rg *gin.RouterGroup, h *handlers.Handlers) {
	// Add device and country detection middleware
	rg.Use(middleware.DeviceDetectionMiddleware())
	rg.Use(middleware.CountryDetectionMiddleware())

	// Countries
	countries := rg.Group("/countries")
	{
		countries.GET("", h.Country.GetCountries)
		countries.GET("/:id", h.Country.GetCountryByID)
		countries.GET("/code/:code/coupons", h.Coupon.GetCouponsByCountry)
		countries.GET("/code/:code/deals", h.Deal.GetDealsByCountry)
	}

	// Categories
	categories := rg.Group("/categories")
	{
		categories.GET("", h.Category.GetCategories)
		categories.GET("/:id", h.Category.GetCategoryByID)
		categories.GET("/slug/:slug", h.Category.GetCategoryBySlug)
		categories.GET("/:id/coupons", h.Coupon.GetCouponsByCategory)
		categories.GET("/:id/deals", h.Deal.GetDealsByCategory)
		categories.GET("/:id/brands", h.Brand.GetBrandsByCategory)
	}

	// Brands
	brands := rg.Group("/brands")
	{
		brands.GET("", h.Brand.GetBrands)
		brands.GET("/:id", h.Brand.GetBrandByID)
		brands.GET("/name/:uniqueName", h.Brand.GetBrandByUniqueName)
		brands.GET("/:id/coupons", h.Coupon.GetCouponsByBrand)
		brands.GET("/:id/deals", h.Deal.GetDealsByBrand)
		brands.GET("/related/:id", h.Brand.GetRelatedBrands)
	}

	// Coupons
	coupons := rg.Group("/coupons")
	{
		coupons.GET("", h.Coupon.GetCoupons)
		coupons.GET("/:id", h.Coupon.GetCouponByID)
		coupons.POST("/:id/click", h.Coupon.TrackCouponClick)
		coupons.GET("/featured", h.Coupon.GetFeaturedCoupons)
		coupons.GET("/popular", h.Coupon.GetPopularCoupons)
		coupons.GET("/recent", h.Coupon.GetRecentCoupons)
		coupons.GET("/hot", h.Coupon.GetHotCoupons)
		coupons.GET("/exclusive", h.Coupon.GetExclusiveCoupons)
		coupons.GET("/search", h.Coupon.SearchCoupons)
	}

	// Deals
	deals := rg.Group("/deals")
	{
		deals.GET("", h.Deal.GetDeals)
		deals.GET("/:id", h.Deal.GetDealByID)
		deals.POST("/:id/click", h.Deal.TrackDealClick)
		deals.GET("/featured", h.Deal.GetFeaturedDeals)
		deals.GET("/hot", h.Deal.GetHotDeals)
		deals.GET("/flash", h.Deal.GetFlashDeals)
	}

	// Search
	search := rg.Group("/search")
	{
		search.GET("", h.Search.Search)
		search.GET("/suggestions", h.Search.GetSuggestions)
		search.GET("/popular", h.Search.GetPopularSearches)
	}

	// Statistics (public stats only)
	stats := rg.Group("/stats")
	{
		stats.GET("/summary", h.Analytics.GetPublicStats)
		stats.GET("/popular-brands", h.Analytics.GetPopularBrands)
		stats.GET("/popular-categories", h.Analytics.GetPopularCategories)
	}

	// Ads
	ads := rg.Group("/ads")
	{
		ads.GET("/modal", h.Ad.GetModalAd)
		ads.GET("/optimal", h.Ad.GetOptimalAds)
		ads.POST("/click", h.Ad.RecordAdClick)
	}
}

// setupProtectedRoutes configures protected API routes
func setupProtectedRoutes(rg *gin.RouterGroup, h *handlers.Handlers) {
	// Analytics
	analytics := rg.Group("/analytics")
	{
		analytics.GET("/clicks", h.Analytics.GetClickAnalytics)
		analytics.GET("/searches", h.Analytics.GetSearchAnalytics)
		analytics.GET("/daily-stats", h.Analytics.GetDailyStats)
	}
}

// setupAdminRoutes configures admin API routes
func setupAdminRoutes(rg *gin.RouterGroup, h *handlers.Handlers) {
	// Countries management
	countries := rg.Group("/countries")
	{
		countries.POST("", h.Country.CreateCountry)
		countries.PUT("/:id", h.Country.UpdateCountry)
		countries.DELETE("/:id", h.Country.DeleteCountry)
	}

	// Categories management
	categories := rg.Group("/categories")
	{
		categories.POST("", h.Category.CreateCategory)
		categories.PUT("/:id", h.Category.UpdateCategory)
		categories.DELETE("/:id", h.Category.DeleteCategory)
	}

	// Brands management
	brands := rg.Group("/brands")
	{
		brands.POST("", h.Brand.CreateBrand)
		brands.PUT("/:id", h.Brand.UpdateBrand)
		brands.DELETE("/:id", h.Brand.DeleteBrand)
	}

	// Coupons management
	coupons := rg.Group("/coupons")
	{
		coupons.POST("", h.Coupon.CreateCoupon)
		coupons.PUT("/:id", h.Coupon.UpdateCoupon)
		coupons.DELETE("/:id", h.Coupon.DeleteCoupon)
		coupons.POST("/:id/verify", h.Coupon.VerifyCoupon)
	}

	// Deals management
	deals := rg.Group("/deals")
	{
		deals.POST("", h.Deal.CreateDeal)
		deals.PUT("/:id", h.Deal.UpdateDeal)
		deals.DELETE("/:id", h.Deal.DeleteDeal)
		deals.POST("/:id/verify", h.Deal.VerifyDeal)
	}

	// System management
	system := rg.Group("/system")
	{
		system.POST("/expire-coupons", h.System.ExpireCoupons)
		system.POST("/refresh-deals", h.System.RefreshDeals)
		system.POST("/update-brands", h.System.UpdateBrands)
		system.POST("/warm-cache", h.System.WarmCache)
		system.GET("/stats", h.System.GetSystemStats)
	}

	// Ads management
	ads := rg.Group("/ads")
	{
		ads.GET("/stats", h.Ad.GetAdStats)
	}
}

// setupLegacyRoutes configures legacy API routes for backward compatibility
func setupLegacyRoutes(rg *gin.RouterGroup, h *handlers.Handlers) {
	// Legacy routes without versioning
	rg.GET("/coupons", h.Coupon.GetCoupons)
	rg.GET("/coupons/:id", h.Coupon.GetCouponByID)
	rg.GET("/deals", h.Deal.GetDeals)
	rg.GET("/deals/:id", h.Deal.GetDealByID)
	rg.GET("/brands", h.Brand.GetBrands)
	rg.GET("/brands/:id", h.Brand.GetBrandByID)
	rg.GET("/categories", h.Category.GetCategories)
	rg.GET("/categories/:id", h.Category.GetCategoryByID)
	rg.GET("/countries", h.Country.GetCountries)
	rg.GET("/countries/:id", h.Country.GetCountryByID)
	rg.GET("/search", h.Search.Search)
}
