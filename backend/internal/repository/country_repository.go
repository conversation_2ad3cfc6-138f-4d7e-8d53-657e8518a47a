package repository

import (
	"maxcoupon-backend/internal/models"

	"gorm.io/gorm"
)

// CountryRepository handles database operations for countries
type CountryRepository struct {
	db *gorm.DB
}

// NewCountryRepository creates a new country repository
func NewCountryRepository(db *gorm.DB) *CountryRepository {
	return &CountryRepository{db: db}
}

// GetAll returns all active countries
func (r *CountryRepository) GetAll() ([]models.Country, error) {
	var countries []models.Country
	err := r.db.Where("status = ?", "active").Order("name ASC").Find(&countries).Error
	return countries, err
}

// GetByID returns a country by ID
func (r *CountryRepository) GetByID(id uint) (*models.Country, error) {
	var country models.Country
	err := r.db.Where("id = ? AND status = ?", id, "active").First(&country).Error
	if err != nil {
		return nil, err
	}
	return &country, nil
}

// GetByCode returns a country by code
func (r *CountryRepository) GetByCode(code string) (*models.Country, error) {
	var country models.Country
	err := r.db.Where("code = ? AND status = ?", code, "active").First(&country).Error
	if err != nil {
		return nil, err
	}
	return &country, nil
}
