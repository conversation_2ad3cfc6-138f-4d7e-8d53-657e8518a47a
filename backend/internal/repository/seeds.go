package repository

import (
	"time"

	"maxcoupon-backend/internal/models"

	"gorm.io/gorm"
)

// SeedDatabase seeds the database with initial data
func SeedDatabase(db *gorm.DB) error {
	// Seed Countries
	countries := []models.Country{
		{
			Name:     "United States",
			Code:     "US",
			Flag:     "🇺🇸",
			Currency: "USD",
			Locale:   "en-US",
			Timezone: "America/New_York",
			Status:   "active",
		},
		{
			Name:     "United Kingdom",
			Code:     "GB",
			Flag:     "🇬🇧",
			Currency: "GBP",
			Locale:   "en-GB",
			Timezone: "Europe/London",
			Status:   "active",
		},
		{
			Name:     "Canada",
			Code:     "CA",
			Flag:     "🇨🇦",
			Currency: "CAD",
			Locale:   "en-CA",
			Timezone: "America/Toronto",
			Status:   "active",
		},
		{
			Name:     "Australia",
			Code:     "AU",
			Flag:     "🇦🇺",
			Currency: "AUD",
			Locale:   "en-AU",
			Timezone: "Australia/Sydney",
			Status:   "active",
		},
	}

	for _, country := range countries {
		var existingCountry models.Country
		if err := db.Where("code = ?", country.Code).First(&existingCountry).Error; err != nil {
			if err == gorm.ErrRecordNotFound {
				if err := db.Create(&country).Error; err != nil {
					return err
				}
			}
		}
	}

	// Seed Categories
	categories := []models.Category{
		{
			Name:        "Electronics",
			Description: "Latest gadgets, computers, and electronic devices",
			Slug:        "electronics",
			Icon:        "📱",
			Color:       "#3B82F6",
			SortOrder:   1,
			Status:      "active",
		},
		{
			Name:        "Fashion",
			Description: "Clothing, shoes, and fashion accessories",
			Slug:        "fashion",
			Icon:        "👗",
			Color:       "#EC4899",
			SortOrder:   2,
			Status:      "active",
		},
		{
			Name:        "Home & Garden",
			Description: "Furniture, home decor, and garden supplies",
			Slug:        "home-garden",
			Icon:        "🏠",
			Color:       "#10B981",
			SortOrder:   3,
			Status:      "active",
		},
		{
			Name:        "Sports & Outdoors",
			Description: "Athletic wear, sports equipment, and outdoor gear",
			Slug:        "sports-outdoors",
			Icon:        "⚽",
			Color:       "#F59E0B",
			SortOrder:   4,
			Status:      "active",
		},
		{
			Name:        "Beauty & Health",
			Description: "Cosmetics, skincare, and health products",
			Slug:        "beauty-health",
			Icon:        "💄",
			Color:       "#8B5CF6",
			SortOrder:   5,
			Status:      "active",
		},
		{
			Name:        "Travel",
			Description: "Hotels, flights, and travel experiences",
			Slug:        "travel",
			Icon:        "✈️",
			Color:       "#06B6D4",
			SortOrder:   6,
			Status:      "active",
		},
	}

	for _, category := range categories {
		var existingCategory models.Category
		if err := db.Where("slug = ?", category.Slug).First(&existingCategory).Error; err != nil {
			if err == gorm.ErrRecordNotFound {
				if err := db.Create(&category).Error; err != nil {
					return err
				}
			}
		}
	}

	// Get created countries and categories for foreign keys
	var usCountry models.Country
	var electronicsCategory, fashionCategory, homeCategory, sportsCategory models.Category
	db.Where("code = ?", "US").First(&usCountry)
	db.Where("slug = ?", "electronics").First(&electronicsCategory)
	db.Where("slug = ?", "fashion").First(&fashionCategory)
	db.Where("slug = ?", "home-garden").First(&homeCategory)
	db.Where("slug = ?", "sports-outdoors").First(&sportsCategory)

	// Seed Brands
	brands := []models.Brand{
		{
			Name:            "Amazon",
			UniqueName:      "amazon",
			Description:     "Everything Store - Electronics, Books, Home & More",
			LogoURL:         "/images/brands/amazon.png",
			WebsiteURL:      "https://amazon.com",
			AffiliateURL:    "https://amazon.com?tag=maxcoupon",
			CommissionRate:  &[]float64{4.0}[0],
			PopularityScore: 95,
			AverageDiscount: &[]float64{15.0}[0],
			TotalCoupons:    150,
			TotalDeals:      300,
			CategoryID:      &electronicsCategory.ID,
			CountryID:       &usCountry.ID,
			PlatformType:    "affiliate",
			Tags:            []string{"electronics", "books", "home", "prime"},
			Status:          "active",
		},
		{
			Name:            "Nike",
			UniqueName:      "nike",
			Description:     "Just Do It - Athletic Wear & Sports Equipment",
			LogoURL:         "/images/brands/nike.png",
			WebsiteURL:      "https://nike.com",
			AffiliateURL:    "https://nike.com?ref=maxcoupon",
			CommissionRate:  &[]float64{6.0}[0],
			PopularityScore: 88,
			AverageDiscount: &[]float64{20.0}[0],
			TotalCoupons:    45,
			TotalDeals:      80,
			CategoryID:      &sportsCategory.ID,
			CountryID:       &usCountry.ID,
			PlatformType:    "affiliate",
			Tags:            []string{"sports", "shoes", "athletic", "running"},
			Status:          "active",
		},
		{
			Name:            "H&M",
			UniqueName:      "hm",
			Description:     "Fashion and Quality at the Best Price",
			LogoURL:         "/images/brands/hm.png",
			WebsiteURL:      "https://hm.com",
			AffiliateURL:    "https://hm.com?ref=maxcoupon",
			CommissionRate:  &[]float64{5.0}[0],
			PopularityScore: 75,
			AverageDiscount: &[]float64{25.0}[0],
			TotalCoupons:    30,
			TotalDeals:      60,
			CategoryID:      &fashionCategory.ID,
			CountryID:       &usCountry.ID,
			PlatformType:    "affiliate",
			Tags:            []string{"fashion", "clothing", "trendy", "affordable"},
			Status:          "active",
		},
		{
			Name:            "IKEA",
			UniqueName:      "ikea",
			Description:     "Affordable Swedish Home Furniture & Decor",
			LogoURL:         "/images/brands/ikea.png",
			WebsiteURL:      "https://ikea.com",
			AffiliateURL:    "https://ikea.com?ref=maxcoupon",
			CommissionRate:  &[]float64{3.5}[0],
			PopularityScore: 82,
			AverageDiscount: &[]float64{18.0}[0],
			TotalCoupons:    25,
			TotalDeals:      50,
			CategoryID:      &homeCategory.ID,
			CountryID:       &usCountry.ID,
			PlatformType:    "affiliate",
			Tags:            []string{"furniture", "home", "swedish", "affordable"},
			Status:          "active",
		},
	}

	for _, brand := range brands {
		var existingBrand models.Brand
		if err := db.Where("unique_name = ?", brand.UniqueName).First(&existingBrand).Error; err != nil {
			if err == gorm.ErrRecordNotFound {
				if err := db.Create(&brand).Error; err != nil {
					return err
				}
			}
		}
	}

	// Get created brands for foreign keys
	var amazonBrand, nikeBrand, hmBrand, ikeaBrand models.Brand
	db.Where("unique_name = ?", "amazon").First(&amazonBrand)
	db.Where("unique_name = ?", "nike").First(&nikeBrand)
	db.Where("unique_name = ?", "hm").First(&hmBrand)
	db.Where("unique_name = ?", "ikea").First(&ikeaBrand)

	// Seed Coupons
	coupons := []models.Coupon{
		{
			Title:                 "Amazon Prime Day Special - 20% Off Electronics",
			Description:           "Get 20% off on all electronics during Prime Day sale. Valid on thousands of items including laptops, phones, and smart home devices.",
			Code:                  &[]string{"PRIME20"}[0],
			DiscountType:          "percentage",
			DiscountValue:         &[]float64{20.0}[0],
			MinimumOrderAmount:    &[]float64{50.0}[0],
			MaximumDiscountAmount: &[]float64{200.0}[0],
			BrandID:               amazonBrand.ID,
			CategoryID:            &electronicsCategory.ID,
			CountryID:             &usCountry.ID,
			StartDate:             time.Now(),
			ExpiryDate:            time.Now().AddDate(0, 0, 30),
			IsFeatured:            true,
			IsExclusive:           true,
			IsVerified:            true,
			PopularityScore:       95,
			ClickCount:            1250,
			SuccessRate:           95.0,
			LastVerified:          &[]time.Time{time.Now()}[0],
			MetaTitle:             "Amazon Prime Day 20% Off Electronics Coupon",
			MetaDescription:       "Save 20% on electronics with this exclusive Amazon Prime Day coupon code",
			ImageURL:              "/images/coupons/amazon-prime.jpg",
			Status:                "active",
		},
		{
			Title:                 "Nike Summer Sale - 25% Off Athletic Wear",
			Description:           "Get 25% off on all athletic wear and footwear. Perfect for your summer workout gear and running shoes.",
			Code:                  &[]string{"SUMMER25"}[0],
			DiscountType:          "percentage",
			DiscountValue:         &[]float64{25.0}[0],
			MinimumOrderAmount:    &[]float64{75.0}[0],
			MaximumDiscountAmount: &[]float64{150.0}[0],
			BrandID:               nikeBrand.ID,
			CategoryID:            &sportsCategory.ID,
			CountryID:             &usCountry.ID,
			StartDate:             time.Now(),
			ExpiryDate:            time.Now().AddDate(0, 0, 25),
			IsFeatured:            true,
			IsExclusive:           false,
			IsVerified:            true,
			PopularityScore:       88,
			ClickCount:            890,
			SuccessRate:           92.0,
			LastVerified:          &[]time.Time{time.Now()}[0],
			MetaTitle:             "Nike Summer Sale 25% Off Athletic Wear",
			MetaDescription:       "Save 25% on Nike athletic wear and footwear with this summer sale coupon",
			ImageURL:              "/images/coupons/nike-summer.jpg",
			Status:                "active",
		},
		{
			Title:              "H&M New Collection - $10 Off $50",
			Description:        "Get $10 off when you spend $50 or more on new collection items. Latest fashion trends at unbeatable prices.",
			Code:               &[]string{"NEW10"}[0],
			DiscountType:       "fixed",
			DiscountValue:      &[]float64{10.0}[0],
			MinimumOrderAmount: &[]float64{50.0}[0],
			BrandID:            hmBrand.ID,
			CategoryID:         &fashionCategory.ID,
			CountryID:          &usCountry.ID,
			StartDate:          time.Now(),
			ExpiryDate:         time.Now().AddDate(0, 0, 20),
			IsFeatured:         true,
			IsExclusive:        true,
			IsVerified:         true,
			PopularityScore:    75,
			ClickCount:         567,
			SuccessRate:        88.0,
			LastVerified:       &[]time.Time{time.Now()}[0],
			MetaTitle:          "H&M New Collection $10 Off $50 Coupon",
			MetaDescription:    "Save $10 on H&M new collection with minimum $50 purchase",
			ImageURL:           "/images/coupons/hm-new.jpg",
			Status:             "active",
		},
		{
			Title:                 "IKEA Kitchen Sale - 20% Off Kitchen Items",
			Description:           "Save 20% on all kitchen furniture and accessories. Transform your kitchen with Swedish design.",
			Code:                  &[]string{"KITCHEN20"}[0],
			DiscountType:          "percentage",
			DiscountValue:         &[]float64{20.0}[0],
			MinimumOrderAmount:    &[]float64{100.0}[0],
			MaximumDiscountAmount: &[]float64{300.0}[0],
			BrandID:               ikeaBrand.ID,
			CategoryID:            &homeCategory.ID,
			CountryID:             &usCountry.ID,
			StartDate:             time.Now(),
			ExpiryDate:            time.Now().AddDate(0, 0, 30),
			IsFeatured:            true,
			IsExclusive:           false,
			IsVerified:            true,
			PopularityScore:       82,
			ClickCount:            423,
			SuccessRate:           90.0,
			LastVerified:          &[]time.Time{time.Now()}[0],
			MetaTitle:             "IKEA Kitchen Sale 20% Off Kitchen Items",
			MetaDescription:       "Save 20% on IKEA kitchen furniture and accessories",
			ImageURL:              "/images/coupons/ikea-kitchen.jpg",
			Status:                "active",
		},
	}

	for _, coupon := range coupons {
		var existingCoupon models.Coupon
		if err := db.Where("code = ?", coupon.Code).First(&existingCoupon).Error; err != nil {
			if err == gorm.ErrRecordNotFound {
				if err := db.Create(&coupon).Error; err != nil {
					return err
				}
			}
		}
	}

	return nil
}
