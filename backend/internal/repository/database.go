package repository

import (
	"context"
	"fmt"
	"time"

	"maxcoupon-backend/internal/config"
	"maxcoupon-backend/internal/models"

	"github.com/go-redis/redis/v8"
	"github.com/sirupsen/logrus"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

// Database holds database connections and repositories
type Database struct {
	DB    *gorm.DB
	Redis *redis.Client

	// Repositories
	Countries *CountryRepository
	Brands    *BrandRepository
	Coupons   *CouponRepository
	Deals     *DealRepository
}

// NewDatabase creates a new database instance with all repositories
func NewDatabase(cfg *config.Config) (*Database, error) {
	// Connect to PostgreSQL
	db, err := connectPostgreSQL(&cfg.Database)
	if err != nil {
		return nil, fmt.Errorf("failed to connect to PostgreSQL: %w", err)
	}

	// Connect to Redis
	redisClient, err := connectRedis(&cfg.Redis)
	if err != nil {
		return nil, fmt.Errorf("failed to connect to Redis: %w", err)
	}

	// Auto-migrate database schema
	if err := autoMigrate(db); err != nil {
		return nil, fmt.Errorf("failed to migrate database: %w", err)
	}

	// Initialize repositories
	database := &Database{
		DB:    db,
		Redis: redisClient,

		Countries: NewCountryRepository(db),
		Brands:    NewBrandRepository(db),
		Coupons:   NewCouponRepository(db, redisClient),
		Deals:     NewDealRepository(db, redisClient),
	}

	return database, nil
}

// connectPostgreSQL establishes connection to PostgreSQL database
func connectPostgreSQL(cfg *config.DatabaseConfig) (*gorm.DB, error) {
	dsn := cfg.GetDSN()

	// Configure GORM logger
	gormLogger := logger.Default.LogMode(logger.Info)
	if logrus.GetLevel() == logrus.DebugLevel {
		gormLogger = logger.Default.LogMode(logger.Info)
	}

	db, err := gorm.Open(postgres.Open(dsn), &gorm.Config{
		Logger: gormLogger,
		NowFunc: func() time.Time {
			return time.Now().UTC()
		},
	})
	if err != nil {
		return nil, err
	}

	// Get underlying sql.DB to configure connection pool
	sqlDB, err := db.DB()
	if err != nil {
		return nil, err
	}

	// Configure connection pool
	sqlDB.SetMaxOpenConns(cfg.MaxOpenConns)
	sqlDB.SetMaxIdleConns(cfg.MaxIdleConns)
	sqlDB.SetConnMaxLifetime(cfg.ConnMaxLifetime)

	// Test connection
	if err := sqlDB.Ping(); err != nil {
		return nil, err
	}

	logrus.Info("Successfully connected to PostgreSQL database")
	return db, nil
}

// connectRedis establishes connection to Redis
func connectRedis(cfg *config.RedisConfig) (*redis.Client, error) {
	client := redis.NewClient(&redis.Options{
		Addr:        cfg.GetRedisAddr(),
		Password:    cfg.Password,
		DB:          cfg.DB,
		MaxRetries:  cfg.MaxRetries,
		PoolSize:    cfg.PoolSize,
		PoolTimeout: cfg.PoolTimeout,
	})

	// Test connection
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	if err := client.Ping(ctx).Err(); err != nil {
		return nil, err
	}

	logrus.Info("Successfully connected to Redis")
	return client, nil
}

// autoMigrate runs database migrations
func autoMigrate(db *gorm.DB) error {
	logrus.Info("Running database migrations...")

	err := db.AutoMigrate(
		&models.Country{},
		&models.Category{},
		&models.Brand{},
		&models.Coupon{},
		&models.Deal{},
		&models.ClickAnalytics{},
	)

	if err != nil {
		return err
	}

	logrus.Info("Database migrations completed successfully")

	// Seed database with initial data
	if err := SeedDatabase(db); err != nil {
		logrus.WithError(err).Warn("Failed to seed database")
	} else {
		logrus.Info("Database seeded successfully")
	}

	return nil
}

// Close closes all database connections
func (d *Database) Close() error {
	// Close PostgreSQL connection
	if sqlDB, err := d.DB.DB(); err == nil {
		if err := sqlDB.Close(); err != nil {
			logrus.WithError(err).Error("Failed to close PostgreSQL connection")
		}
	}

	// Close Redis connection
	if err := d.Redis.Close(); err != nil {
		logrus.WithError(err).Error("Failed to close Redis connection")
	}

	logrus.Info("Database connections closed")
	return nil
}

// HealthCheck performs health checks on all database connections
func (d *Database) HealthCheck(ctx context.Context) error {
	// Check PostgreSQL
	if sqlDB, err := d.DB.DB(); err != nil {
		return fmt.Errorf("failed to get PostgreSQL connection: %w", err)
	} else if err := sqlDB.PingContext(ctx); err != nil {
		return fmt.Errorf("PostgreSQL health check failed: %w", err)
	}

	// Check Redis
	if err := d.Redis.Ping(ctx).Err(); err != nil {
		return fmt.Errorf("Redis health check failed: %w", err)
	}

	return nil
}

// GetStats returns database statistics
func (d *Database) GetStats() map[string]interface{} {
	stats := make(map[string]interface{})

	// PostgreSQL stats
	if sqlDB, err := d.DB.DB(); err == nil {
		dbStats := sqlDB.Stats()
		stats["postgresql"] = map[string]interface{}{
			"open_connections":     dbStats.OpenConnections,
			"in_use":               dbStats.InUse,
			"idle":                 dbStats.Idle,
			"wait_count":           dbStats.WaitCount,
			"wait_duration":        dbStats.WaitDuration.String(),
			"max_idle_closed":      dbStats.MaxIdleClosed,
			"max_idle_time_closed": dbStats.MaxIdleTimeClosed,
			"max_lifetime_closed":  dbStats.MaxLifetimeClosed,
		}
	}

	// Redis stats
	if poolStats := d.Redis.PoolStats(); poolStats != nil {
		stats["redis"] = map[string]interface{}{
			"hits":        poolStats.Hits,
			"misses":      poolStats.Misses,
			"timeouts":    poolStats.Timeouts,
			"total_conns": poolStats.TotalConns,
			"idle_conns":  poolStats.IdleConns,
			"stale_conns": poolStats.StaleConns,
		}
	}

	return stats
}

// Transaction executes a function within a database transaction
func (d *Database) Transaction(fn func(*gorm.DB) error) error {
	return d.DB.Transaction(fn)
}

// BeginTransaction starts a new database transaction
func (d *Database) BeginTransaction() *gorm.DB {
	return d.DB.Begin()
}
