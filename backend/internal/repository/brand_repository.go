package repository

import (
	"maxcoupon-backend/internal/models"

	"gorm.io/gorm"
)

// BrandRepository handles database operations for brands
type BrandRepository struct {
	db *gorm.DB
}

// NewBrandRepository creates a new brand repository
func NewBrandRepository(db *gorm.DB) *BrandRepository {
	return &BrandRepository{db: db}
}

// GetAllWithPagination returns all active brands with pagination
func (r *BrandRepository) GetAllWithPagination(page, limit int, categoryID, countryID *uint) ([]models.Brand, int64, error) {
	var brands []models.Brand
	var total int64

	query := r.db.Where("status = ?", "active")

	if categoryID != nil {
		query = query.Where("category_id = ?", *categoryID)
	}

	if countryID != nil {
		query = query.Where("country_id = ?", *countryID)
	}

	// Count total
	err := query.Model(&models.Brand{}).Count(&total).Error
	if err != nil {
		return nil, 0, err
	}

	// Get paginated results
	offset := (page - 1) * limit
	err = query.Order("popularity_score DESC, name ASC").
		Offset(offset).
		Limit(limit).
		Find(&brands).Error

	return brands, total, err
}

// GetByID returns a brand by ID
func (r *BrandRepository) GetByID(id uint) (*models.Brand, error) {
	var brand models.Brand
	err := r.db.Where("id = ? AND status = ?", id, "active").First(&brand).Error
	if err != nil {
		return nil, err
	}
	return &brand, nil
}

// GetByUniqueName returns a brand by unique name
func (r *BrandRepository) GetByUniqueName(uniqueName string) (*models.Brand, error) {
	var brand models.Brand
	err := r.db.Where("unique_name = ? AND status = ?", uniqueName, "active").First(&brand).Error
	if err != nil {
		return nil, err
	}
	return &brand, nil
}

// GetPopular returns popular brands
func (r *BrandRepository) GetPopular(limit int) ([]models.Brand, error) {
	var brands []models.Brand
	err := r.db.Where("status = ?", "active").
		Order("popularity_score DESC").
		Limit(limit).
		Find(&brands).Error
	return brands, err
}

// GetByCategory returns brands by category
func (r *BrandRepository) GetByCategory(categoryID uint, page, limit int) ([]models.Brand, int64, error) {
	var brands []models.Brand
	var total int64

	query := r.db.Where("category_id = ? AND status = ?", categoryID, "active")

	// Count total
	err := query.Model(&models.Brand{}).Count(&total).Error
	if err != nil {
		return nil, 0, err
	}

	// Get paginated results
	offset := (page - 1) * limit
	err = query.Order("popularity_score DESC").
		Offset(offset).
		Limit(limit).
		Find(&brands).Error

	return brands, total, err
}

// Search searches brands by name
func (r *BrandRepository) Search(query string, page, limit int) ([]models.Brand, int64, error) {
	var brands []models.Brand
	var total int64

	searchQuery := r.db.Where("status = ? AND (name ILIKE ? OR description ILIKE ?)", 
		"active", "%"+query+"%", "%"+query+"%")

	// Count total
	err := searchQuery.Model(&models.Brand{}).Count(&total).Error
	if err != nil {
		return nil, 0, err
	}

	// Get paginated results
	offset := (page - 1) * limit
	err = searchQuery.Order("popularity_score DESC").
		Offset(offset).
		Limit(limit).
		Find(&brands).Error

	return brands, total, err
}
