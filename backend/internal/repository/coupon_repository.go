package repository

import (
	"fmt"
	"time"

	"maxcoupon-backend/internal/models"

	"github.com/go-redis/redis/v8"
	"gorm.io/gorm"
)

// CouponFilters represents filters for coupon queries
type CouponFilters struct {
	BrandID    *uint
	CategoryID *uint
	CountryID  *uint
	Status     string
	IsFeatured *bool
	IsExpired  *bool
	Search     string
	Limit      int
	Offset     int
	SortBy     string
	SortOrder  string
}

// CouponRepository handles coupon data access
type CouponRepository struct {
	db    *gorm.DB
	redis *redis.Client
}

// NewCouponRepository creates a new coupon repository
func NewCouponRepository(db *gorm.DB, redis *redis.Client) *CouponRepository {
	return &CouponRepository{db: db, redis: redis}
}

// GetAll returns coupons with filters
func (r *CouponRepository) GetAll(filters CouponFilters) ([]models.Coupon, int64, error) {
	var coupons []models.Coupon
	var total int64

	query := r.db.Model(&models.Coupon{}).
		Preload("Brand").
		Preload("Category").
		Preload("Country")

	// Apply filters
	query = r.applyFilters(query, filters)

	// Count total records
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// Apply sorting
	if filters.SortBy != "" {
		order := filters.SortBy
		if filters.SortOrder == "desc" {
			order += " DESC"
		} else {
			order += " ASC"
		}
		query = query.Order(order)
	} else {
		// Default sorting
		query = query.Order("is_featured DESC, popularity_score DESC, created_at DESC")
	}

	// Apply pagination
	if filters.Limit > 0 {
		query = query.Limit(filters.Limit)
	}
	if filters.Offset > 0 {
		query = query.Offset(filters.Offset)
	}

	err := query.Find(&coupons).Error
	return coupons, total, err
}

// GetByID returns a coupon by ID
func (r *CouponRepository) GetByID(id uint) (*models.Coupon, error) {
	var coupon models.Coupon
	err := r.db.Preload("Brand").
		Preload("Category").
		Preload("Country").
		Where("id = ?", id).
		First(&coupon).Error
	if err != nil {
		return nil, err
	}
	return &coupon, nil
}

// GetByBrand returns coupons by brand ID
func (r *CouponRepository) GetByBrand(brandID uint, filters CouponFilters) ([]models.Coupon, error) {
	filters.BrandID = &brandID
	coupons, _, err := r.GetAll(filters)
	return coupons, err
}

// GetByCategory returns coupons by category ID
func (r *CouponRepository) GetByCategory(categoryID uint, filters CouponFilters) ([]models.Coupon, error) {
	filters.CategoryID = &categoryID
	coupons, _, err := r.GetAll(filters)
	return coupons, err
}

// GetByCountry returns coupons by country ID
func (r *CouponRepository) GetByCountry(countryID uint, filters CouponFilters) ([]models.Coupon, error) {
	filters.CountryID = &countryID
	coupons, _, err := r.GetAll(filters)
	return coupons, err
}

// GetFeatured returns featured coupons
func (r *CouponRepository) GetFeatured(limit int) ([]models.Coupon, error) {
	featured := true
	filters := CouponFilters{
		IsFeatured: &featured,
		Status:     "active",
		Limit:      limit,
	}
	coupons, _, err := r.GetAll(filters)
	return coupons, err
}

// GetPopular returns popular coupons
func (r *CouponRepository) GetPopular(limit int) ([]models.Coupon, error) {
	filters := CouponFilters{
		Status:    "active",
		Limit:     limit,
		SortBy:    "popularity_score",
		SortOrder: "desc",
	}
	coupons, _, err := r.GetAll(filters)
	return coupons, err
}

// GetRecent returns recent coupons
func (r *CouponRepository) GetRecent(limit int) ([]models.Coupon, error) {
	filters := CouponFilters{
		Status:    "active",
		Limit:     limit,
		SortBy:    "created_at",
		SortOrder: "desc",
	}
	coupons, _, err := r.GetAll(filters)
	return coupons, err
}

// Create creates a new coupon
func (r *CouponRepository) Create(coupon *models.Coupon) error {
	return r.db.Create(coupon).Error
}

// Update updates a coupon
func (r *CouponRepository) Update(coupon *models.Coupon) error {
	return r.db.Save(coupon).Error
}

// Delete soft deletes a coupon
func (r *CouponRepository) Delete(id uint) error {
	return r.db.Model(&models.Coupon{}).Where("id = ?", id).Update("status", "inactive").Error
}

// IncrementClick increments the click count for a coupon
func (r *CouponRepository) IncrementClick(id uint) error {
	return r.db.Model(&models.Coupon{}).Where("id = ?", id).
		UpdateColumn("click_count", gorm.Expr("click_count + 1")).Error
}

// ExpireOldCoupons marks expired coupons as expired
func (r *CouponRepository) ExpireOldCoupons() (int64, error) {
	result := r.db.Model(&models.Coupon{}).
		Where("status = ? AND expiry_date <= ?", "active", time.Now()).
		Update("status", "expired")
	return result.RowsAffected, result.Error
}

// Search searches coupons by text
func (r *CouponRepository) Search(query string, filters CouponFilters) ([]models.Coupon, int64, error) {
	filters.Search = query
	return r.GetAll(filters)
}

// applyFilters applies filters to the query
func (r *CouponRepository) applyFilters(query *gorm.DB, filters CouponFilters) *gorm.DB {
	if filters.BrandID != nil {
		query = query.Where("brand_id = ?", *filters.BrandID)
	}

	if filters.CategoryID != nil {
		query = query.Where("category_id = ?", *filters.CategoryID)
	}

	if filters.CountryID != nil {
		query = query.Where("country_id = ?", *filters.CountryID)
	}

	if filters.Status != "" {
		query = query.Where("status = ?", filters.Status)
	} else {
		// Default to active coupons
		query = query.Where("status = ?", "active")
	}

	if filters.IsFeatured != nil {
		query = query.Where("is_featured = ?", *filters.IsFeatured)
	}

	if filters.IsExpired != nil {
		if *filters.IsExpired {
			query = query.Where("expiry_date <= ?", time.Now())
		} else {
			query = query.Where("expiry_date > ?", time.Now())
		}
	} else {
		// Default to non-expired coupons
		query = query.Where("expiry_date > ?", time.Now())
	}

	if filters.Search != "" {
		searchTerm := fmt.Sprintf("%%%s%%", filters.Search)
		query = query.Where(
			"title ILIKE ? OR description ILIKE ? OR code ILIKE ?",
			searchTerm, searchTerm, searchTerm,
		)
	}

	return query
}

// GetHot returns hot/trending coupons based on recent activity and popularity
func (r *CouponRepository) GetHot(limit int) ([]models.Coupon, error) {
	var coupons []models.Coupon

	err := r.db.Preload("Brand").
		Where("status = ? AND expiry_date > ?", "active", time.Now()).
		Order("click_count DESC").
		Order("created_at DESC").
		Limit(limit).
		Find(&coupons).Error

	return coupons, err
}

// GetExclusive returns exclusive coupons
func (r *CouponRepository) GetExclusive(limit int) ([]models.Coupon, error) {
	var coupons []models.Coupon

	err := r.db.Preload("Brand").
		Where("status = ? AND is_exclusive = ? AND expiry_date > ?", "active", true, time.Now()).
		Order("created_at DESC").
		Limit(limit).
		Find(&coupons).Error

	return coupons, err
}
