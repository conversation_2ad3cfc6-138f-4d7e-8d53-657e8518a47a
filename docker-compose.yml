version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: maxcoupon_postgres
    environment:
      POSTGRES_DB: maxcoupon
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/init:/docker-entrypoint-initdb.d
    networks:
      - maxcoupon_network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: maxcoupon_redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - maxcoupon_network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    command: redis-server --appendonly yes

  # Backend API (Go)
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile.dev
    container_name: maxcoupon_backend
    ports:
      - "8080:8080"
      - "9090:9090"  # Prometheus metrics
    environment:
      - DB_HOST=postgres
      - DB_PORT=5432
      - DB_NAME=maxcoupon
      - DB_USER=postgres
      - DB_PASSWORD=password
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - SERVER_HOST=0.0.0.0
      - SERVER_PORT=8080
      - SERVER_MODE=development
    volumes:
      - ./backend:/app
      - backend_cache:/go/pkg/mod
    networks:
      - maxcoupon_network
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Frontend (Astro)
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile.dev
    container_name: maxcoupon_frontend
    ports:
      - "4321:4321"
    environment:
      - NODE_ENV=development
      - API_BASE_URL=http://backend:8080/api
    volumes:
      - ./frontend:/app
      - frontend_node_modules:/app/node_modules
    networks:
      - maxcoupon_network
    depends_on:
      - backend
    restart: unless-stopped

  # Nginx Reverse Proxy (for production-like setup)
  nginx:
    image: nginx:alpine
    container_name: maxcoupon_nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./docker/nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./docker/nginx/conf.d:/etc/nginx/conf.d
      - ./docker/ssl:/etc/nginx/ssl
    networks:
      - maxcoupon_network
    depends_on:
      - frontend
      - backend
    restart: unless-stopped

  # Prometheus (Monitoring)
  prometheus:
    image: prom/prometheus:latest
    container_name: maxcoupon_prometheus
    ports:
      - "9091:9090"
    volumes:
      - ./docker/prometheus/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    networks:
      - maxcoupon_network
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--web.enable-lifecycle'

  # Grafana (Monitoring Dashboard)
  grafana:
    image: grafana/grafana:latest
    container_name: maxcoupon_grafana
    ports:
      - "3001:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
    volumes:
      - grafana_data:/var/lib/grafana
      - ./docker/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./docker/grafana/datasources:/etc/grafana/provisioning/datasources
    networks:
      - maxcoupon_network
    depends_on:
      - prometheus

volumes:
  postgres_data:
  redis_data:
  backend_cache:
  frontend_node_modules:
  prometheus_data:
  grafana_data:

networks:
  maxcoupon_network:
    driver: bridge
