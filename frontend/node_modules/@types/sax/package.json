{"name": "@types/sax", "version": "1.2.7", "description": "TypeScript definitions for sax", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/sax", "license": "MIT", "contributors": [{"name": "<PERSON> (Asana, Inc.)", "githubUsername": "vsiao", "url": "https://github.com/vsiao"}, {"name": "<PERSON><PERSON>", "githubUsername": "evert", "url": "https://github.com/evert"}, {"name": "<PERSON>", "githubUsername": "djcs<PERSON>", "url": "https://github.com/djcsdy"}, {"name": "<PERSON>", "githubUsername": "fvanderveen", "url": "https://github.com/fvanderveen"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/sax"}, "scripts": {}, "dependencies": {"@types/node": "*"}, "typesPublisherContentHash": "30ff89927c6c888d3113b0a9453e6166ca211ed5d328e36eed86e90eae239b88", "typeScriptVersion": "4.5"}