const PROPAGATED_ASSET_FLAG = "astroPropagatedAssets";
const CONTENT_RENDER_FLAG = "astroRenderContent";
const CONTENT_FLAG = "astroContentCollectionEntry";
const DATA_FLAG = "astroDataCollectionEntry";
const CONTENT_IMAGE_FLAG = "astroContentImageFlag";
const CONTENT_MODULE_FLAG = "astroContentModuleFlag";
const VIRTUAL_MODULE_ID = "astro:content";
const RESOLVED_VIRTUAL_MODULE_ID = "\0" + VIRTUAL_MODULE_ID;
const DATA_STORE_VIRTUAL_ID = "astro:data-layer-content";
const RESOLVED_DATA_STORE_VIRTUAL_ID = "\0" + DATA_STORE_VIRTUAL_ID;
const MODULES_MJS_ID = "astro:content-module-imports";
const MODULES_MJS_VIRTUAL_ID = "\0" + MODULES_MJS_ID;
const DEFERRED_MODULE = "astro:content-layer-deferred-module";
const ASSET_IMPORTS_VIRTUAL_ID = "astro:asset-imports";
const ASSET_IMPORTS_RESOLVED_STUB_ID = "\0" + ASSET_IMPORTS_VIRTUAL_ID;
const LINKS_PLACEHOLDER = "@@ASTRO-LINKS@@";
const STYLES_PLACEHOLDER = "@@ASTRO-STYLES@@";
const SCRIPTS_PLACEHOLDER = "@@ASTRO-SCRIPTS@@";
const IMAGE_IMPORT_PREFIX = "__ASTRO_IMAGE_";
const CONTENT_FLAGS = [
  CONTENT_FLAG,
  CONTENT_RENDER_FLAG,
  DATA_FLAG,
  PROPAGATED_ASSET_FLAG,
  CONTENT_IMAGE_FLAG,
  CONTENT_MODULE_FLAG
];
const CONTENT_TYPES_FILE = "astro/content.d.ts";
const DATA_STORE_FILE = "data-store.json";
const ASSET_IMPORTS_FILE = "assets.mjs";
const MODULES_IMPORTS_FILE = "modules.mjs";
const CONTENT_LAYER_TYPE = "content_layer";
export {
  ASSET_IMPORTS_FILE,
  ASSET_IMPORTS_RESOLVED_STUB_ID,
  ASSET_IMPORTS_VIRTUAL_ID,
  CONTENT_FLAG,
  CONTENT_FLAGS,
  CONTENT_IMAGE_FLAG,
  CONTENT_LAYER_TYPE,
  CONTENT_MODULE_FLAG,
  CONTENT_RENDER_FLAG,
  CONTENT_TYPES_FILE,
  DATA_FLAG,
  DATA_STORE_FILE,
  DATA_STORE_VIRTUAL_ID,
  DEFERRED_MODULE,
  IMAGE_IMPORT_PREFIX,
  LINKS_PLACEHOLDER,
  MODULES_IMPORTS_FILE,
  MODULES_MJS_ID,
  MODULES_MJS_VIRTUAL_ID,
  PROPAGATED_ASSET_FLAG,
  RESOLVED_DATA_STORE_VIRTUAL_ID,
  RESOLVED_VIRTUAL_MODULE_ID,
  SCRIPTS_PLACEHOLDER,
  STYLES_PLACEHOLDER,
  VIRTUAL_MODULE_ID
};
