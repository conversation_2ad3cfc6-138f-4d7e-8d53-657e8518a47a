function isDefinedIcon(icon) {
  return icon in icons;
}
function getIconElement(name) {
  const icon = icons[name];
  if (!icon) {
    return void 0;
  }
  const svgFragment = new DocumentFragment();
  svgFragment.append(document.createRange().createContextualFragment(icon));
  return svgFragment.firstElementChild;
}
const icons = {
  "astro:logo": `<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 85 107"><path fill="#fff" d="M27.6 91.1c-4.8-4.4-6.3-13.7-4.2-20.4 3.5 4.2 8.3 5.6 13.3 6.3 7.7 1.2 15.3.8 22.5-2.8l2.5-1.4c.7 2 .9 3.9.6 5.9-.6 4.9-3 8.7-6.9 11.5-1.5 1.2-3.2 2.2-4.8 3.3-4.9 3.3-6.2 7.2-4.4 12.9l.2.6a13 13 0 0 1-5.7-5 13.8 13.8 0 0 1-2.2-7.4c0-1.3 0-2.7-.2-4-.5-3.1-2-4.6-4.8-4.7a5.5 5.5 0 0 0-5.7 4.6l-.2.6Z"/><path fill="url(#a)" d="M27.6 91.1c-4.8-4.4-6.3-13.7-4.2-20.4 3.5 4.2 8.3 5.6 13.3 6.3 7.7 1.2 15.3.8 22.5-2.8l2.5-1.4c.7 2 .9 3.9.6 5.9-.6 4.9-3 8.7-6.9 11.5-1.5 1.2-3.2 2.2-4.8 3.3-4.9 3.3-6.2 7.2-4.4 12.9l.2.6a13 13 0 0 1-5.7-5 13.8 13.8 0 0 1-2.2-7.4c0-1.3 0-2.7-.2-4-.5-3.1-2-4.6-4.8-4.7a5.5 5.5 0 0 0-5.7 4.6l-.2.6Z"/><path fill="#fff" d="M0 69.6s14.3-7 28.7-7l10.8-33.5c.4-1.6 1.6-2.7 3-2.7 1.2 0 2.4 1.1 2.8 2.7l10.9 33.5c17 0 28.6 7 28.6 7L60.5 3.2c-.7-2-2-3.2-3.5-3.2H27.8c-1.6 0-2.7 1.3-3.4 3.2L0 69.6Z"/><defs><linearGradient id="a" x1="22.5" x2="69.1" y1="107" y2="84.9" gradientUnits="userSpaceOnUse"><stop stop-color="#D83333"/><stop offset="1" stop-color="#F041FF"/></linearGradient></defs></svg>`,
  warning: `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16"><path fill="currentColor" d="M8 .40625c-1.5019 0-2.97007.445366-4.21886 1.27978C2.53236 2.52044 1.55905 3.70642.984293 5.094.40954 6.48157.259159 8.00842.552165 9.48147.845172 10.9545 1.56841 12.3076 2.63041 13.3696c1.06201 1.062 2.41508 1.7852 3.88813 2.0782 1.47304.293 2.99989.1427 4.38746-.4321 1.3876-.5747 2.5736-1.5481 3.408-2.7968.8344-1.2488 1.2798-2.717 1.2798-4.2189-.0023-2.0133-.8031-3.9435-2.2267-5.36713C11.9435 1.20925 10.0133.408483 8 .40625ZM8 13.9062c-1.16814 0-2.31006-.3463-3.28133-.9953-.97128-.649-1.7283-1.5715-2.17533-2.6507-.44703-1.0792-.56399-2.26675-.3361-3.41245.22789-1.1457.79041-2.1981 1.61641-3.0241.82601-.826 1.8784-1.38852 3.0241-1.61641 1.1457-.2279 2.33325-.11093 3.41245.3361 1.0793.44703 2.0017 1.20405 2.6507 2.17532.649.97128.9954 2.11319.9954 3.28134-.0017 1.56592-.6245 3.0672-1.7318 4.1745S9.56592 13.9046 8 13.9062Zm-.84375-5.62495V4.625c0-.22378.0889-.43839.24713-.59662.15824-.15824.37285-.24713.59662-.24713.22378 0 .43839.08889.59662.24713.15824.15823.24713.37284.24713.59662v3.65625c0 .22378-.08889.43839-.24713.59662C8.43839 9.03611 8.22378 9.125 8 9.125c-.22377 0-.43838-.08889-.59662-.24713-.15823-.15823-.24713-.37284-.24713-.59662ZM9.125 11.0938c0 .2225-.06598.44-.18959.625-.12362.185-.29932.3292-.50489.4143-.20556.0852-.43176.1074-.64999.064-.21823-.0434-.41869-.1505-.57602-.3079-.15734-.1573-.26448-.3577-.30789-.576-.04341-.2182-.02113-.4444.06402-.65.08515-.2055.22934-.3812.41435-.5049.185-.1236.40251-.18955.62501-.18955.29837 0 .58452.11855.7955.32955.21098.2109.3295.4971.3295.7955Z"/></svg>`,
  "arrow-down": '<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 12 14"><path fill="currentColor" d="m11.0306 8.53063-4.5 4.49997c-.06968.0699-.15247.1254-.24364.1633-.09116.0378-.1889.0573-.28761.0573-.09871 0-.19645-.0195-.28762-.0573-.09116-.0379-.17395-.0934-.24363-.1633L.968098 8.53063c-.140896-.1409-.220051-.332-.220051-.53125 0-.19926.079155-.39036.220051-.53125.140892-.1409.331992-.22006.531252-.22006.19926 0 .39035.07916.53125.22006l3.21937 3.21937V1.5c0-.19891.07902-.38968.21967-.53033C5.61029.829018 5.80106.75 5.99997.75c.19891 0 .38968.079018.53033.21967.14065.14065.21967.33142.21967.53033v9.1875l3.21938-3.22c.14085-.1409.33195-.22005.53125-.22005.1993 0 .3904.07915.5312.22005.1409.1409.2201.33199.2201.53125s-.0792.39035-.2201.53125l-.0012.00063Z"/></svg>',
  bug: '<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 25 24"><path fill="currentColor" d="M13.7916 8.25006c0-.29667.088-.58668.2528-.83335.1648-.24668.3991-.43893.6732-.55247.2741-.11353.5757-.14323.8667-.08536.2909.05788.5582.20074.768.41052s.3526.47706.4105.76803c.0579.29097.0282.59257-.0854.86666-.1135.27409-.3057.50836-.5524.67318-.2467.16482-.5367.25279-.8334.25279-.3978 0-.7793-.15803-1.0606-.43934-.2813-.2813-.4394-.66283-.4394-1.06066Zm-3.75-1.5c-.29665 0-.58666.08798-.83333.2528-.24667.16482-.43893.39909-.55246.67318-.11354.27409-.14324.57569-.08536.86666.05788.29097.20074.55824.41052.76802.20977.20978.47705.35264.76802.41052.29101.05788.59261.02817.86671-.08536.274-.11353.5083-.30579.6731-.55246.1649-.24668.2528-.53668.2528-.83336 0-.39782-.158-.77935-.4393-1.06066-.2813-.2813-.6628-.43934-1.0607-.43934Zm11.25 6.75004c.0003.6512-.0733 1.3003-.2193 1.935l1.7953.7837c.1354.0592.2578.1445.3603.2511.1024.1065.1829.2322.2368.3698.0539.1377.0801.2846.0772.4323-.0028.1478-.0348.2936-.094.429-.0592.1354-.1446.2579-.2511.3603-.1065.1025-.2322.1829-.3698.2368-.1377.0539-.2846.0802-.4323.0773-.1478-.0029-.2936-.0349-.429-.0941l-1.6875-.7359c-.7348 1.3818-1.8317 2.5377-3.1732 3.3437s-2.8771 1.2319-4.4421 1.2319c-1.5651 0-3.10061-.4259-4.44213-1.2319-1.34151-.806-2.43843-1.9619-3.17321-3.3437l-1.6875.7359c-.13542.0592-.28119.0912-.42896.0941-.14778.0029-.29468-.0234-.43232-.0773-.13763-.0539-.2633-.1343-.36984-.2368-.10653-.1024-.19185-.2249-.25106-.3603-.05922-.1354-.09119-.2812-.09407-.429-.00289-.1477.02336-.2946.07725-.4323.05389-.1376.13436-.2633.23681-.3698.10246-.1066.22489-.1919.36032-.2511l1.79531-.7837c-.14354-.635-.21462-1.2841-.21187-1.935v-.375h-1.875c-.29837 0-.58452-.1186-.7955-.3295-.21098-.211-.3295-.4972-.3295-.7955 0-.2984.11852-.5846.3295-.7955.21098-.211.49713-.3295.7955-.3295h1.875v-.375c-.00029-.65126.0733-1.30041.21937-1.93504l-1.79531-.78375c-.27351-.11959-.4883-.34294-.59713-.6209-.10883-.27797-.10278-.58778.01682-.86128.11959-.27351.34294-.4883.6209-.59713.27797-.10883.58778-.10278.86128.01681l1.6875.73594c.73478-1.38183 1.8317-2.53769 3.17321-3.34373 1.34152-.80604 2.87703-1.23187 4.44213-1.23187 1.565 0 3.1006.42583 4.4421 1.23187 1.3415.80604 2.4384 1.9619 3.1732 3.34373l1.6875-.73594c.1354-.05921.2812-.09118.429-.09406.1477-.00289.2946.02336.4323.07725.1376.05389.2633.13435.3698.23681.1065.10245.1919.22489.2511.36032.0592.13542.0912.28118.094.42896.0029.14778-.0233.29468-.0772.43232-.0539.13763-.1344.2633-.2368.36984-.1025.10653-.2249.19185-.3603.25106l-1.7953.78375c.1435.63492.2146 1.28407.2118 1.93504v.375h1.875c.2984 0 .5845.1185.7955.3295.211.2109.3295.4971.3295.7955 0 .2983-.1185.5845-.3295.7955-.211.2109-.4971.3295-.7955.3295h-1.875v.375Zm-14.99997-2.625H19.0416v-.375c0-1.69079-.6716-3.3123-1.8672-4.50784-1.1955-1.19555-2.817-1.8672-4.5078-1.8672-1.6907 0-3.31224.67165-4.50778 1.8672C6.96328 7.1878 6.29163 8.80931 6.29163 10.5001v.375Zm5.24997 8.8987v-6.6487H6.29163v.375c.00211 1.4949.52876 2.9417 1.48816 4.0882.95939 1.1464 2.29071 1.9199 3.76181 2.1855Zm7.5-6.2737v-.375h-5.25v6.6487c1.4712-.2656 2.8025-1.0391 3.7619-2.1855.9594-1.1465 1.486-2.5933 1.4881-4.0882Z"/></svg>',
  "": '<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 25 24"><path fill="currentColor" d="m20.6293 7.455-5.25-5.25c-.1045-.10461-.2285-.1876-.3651-.24422-.1366-.05662-.283-.08577-.4308-.08578H5.58337c-.49728 0-.97419.19754-1.32582.54917-.35163.35164-.54918.82855-.54918 1.32583v16.5c0 .4973.19755.9742.54918 1.3258.35163.3517.82854.5492 1.32582.5492H19.0834c.4973 0 .9742-.1975 1.3258-.5492.3516-.3516.5492-.8285.5492-1.3258v-12c0-.29813-.1184-.58407-.3291-.795Zm-3.1397.045h-2.1562V5.34375L17.4896 7.5ZM5.95837 19.875V4.125h7.12503v4.5c0 .29837.1185.58452.3295.7955.211.21097.4971.3295.7955.3295h4.5v10.125H5.95837Zm9.04503-4.5459c.3426-.7185.4202-1.5349.2192-2.3051-.2011-.7702-.6679-1.4445-1.3179-1.9038-.65-.4594-1.4415-.6742-2.2346-.6066-.7931.0677-1.5368.4135-2.0996.9763-.56283.5629-.90863 1.3065-.9763 2.0996-.06766.7931.14716 1.5846.60651 2.2346.45936.6501 1.13369 1.1169 1.90389 1.3179.7701.201 1.5866.1234 2.305-.2192l1.125 1.125c.2114.2114.498.3301.7969.3301.2989 0 .5855-.1187.7969-.3301.2113-.2113.3301-.498.3301-.7969 0-.2988-.1188-.5855-.3301-.7968l-1.125-1.125Zm-4.17-1.4541c0-.2225.066-.44.1896-.625.1236-.185.2993-.3292.5049-.4144.2055-.0851.4317-.1074.65-.064.2182.0434.4186.1506.576.3079.1573.1573.2644.3578.3079.576.0434.2183.0211.4445-.0641.65-.0851.2056-.2293.3813-.4143.5049-.185.1236-.4025.1896-.625.1896-.2984 0-.5845-.1185-.7955-.3295-.211-.211-.3295-.4971-.3295-.7955Z"/></svg>',
  "check-circle": '<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14"><path fill="currentColor" d="M10.0306 4.96938c.0699.06967.1254.15247.1633.24363.0378.09116.0573.1889.0573.28762 0 .09871-.0195.19645-.0573.28761-.0379.09116-.0934.17396-.1633.24364L6.53063 9.53187c-.06968.06992-.15247.1254-.24364.16326-.09116.03785-.1889.05734-.28761.05734-.09871 0-.19645-.01949-.28762-.05734-.09116-.03786-.17395-.09334-.24363-.16326l-1.5-1.5c-.06977-.06976-.12511-.15258-.16286-.24373-.03776-.09116-.05719-.18885-.05719-.28752 0-.09866.01943-.19635.05719-.28751.03775-.09115.09309-.17397.16286-.24373.06976-.06977.15259-.12511.24374-.16287.09115-.03775.18885-.05719.28751-.05719s.19636.01944.28751.05719c.09115.03776.17397.0931.24374.16287L6 7.9375l2.96938-2.97c.06978-.06961.15259-.12478.24371-.16237.09111-.03758.18874-.05683.2873-.05666.09856.00018.19612.01978.28711.05768.09098.0379.1736.09337.2431.16323ZM13.75 7c0 1.33502-.3959 2.64007-1.1376 3.7501-.7417 1.11-1.7959 1.9752-3.02928 2.4861-1.23341.5109-2.5906.6446-3.89998.3841-1.30937-.2605-2.5121-.9033-3.45611-1.8473-.944-.944-1.586877-2.14677-1.847328-3.45614-.26045-1.30937-.126777-2.66657.384114-3.89997C1.27471 3.18349 2.13987 2.12928 3.2499 1.38758 4.35994.645881 5.66498.25 7 .25c1.78961.001985 3.5053.713781 4.7708 1.97922C13.0362 3.49466 13.748 5.2104 13.75 7Zm-1.5 0c0-1.03835-.3079-2.05339-.8848-2.91674-.5769-.86336-1.3968-1.53627-2.35611-1.93363-.95931-.39736-2.01491-.50133-3.03331-.29875-1.0184.20257-1.95386.70258-2.68809 1.43681-.73422.73422-1.23424 1.66969-1.43681 2.68809-.20257 1.0184-.0986 2.074.29876 3.03331.39736.95931 1.07026 1.77921 1.93362 2.35611.86336.5769 1.87839.8848 2.91674.8848 1.39193-.0015 2.72643-.5551 3.7107-1.5393C11.6949 9.72642 12.2485 8.39193 12.25 7Z"/></svg>',
  gear: '<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 22 22"><path fill="currentColor" d="M11 6.12507c-.9642 0-1.90671.28592-2.7084.82159-.80169.53567-1.42653 1.29704-1.79551 2.18783-.36898.89081-.46552 1.87101-.27742 2.81661.18811.9457.6524 1.8143 1.33419 2.4961.68178.6818 1.55042 1.1461 2.49604 1.3342.9457.1881 1.9259.0916 2.8167-.2774s1.6521-.9938 2.1878-1.7955c.5357-.8017.8216-1.7442.8216-2.7084-.0015-1.2925-.5156-2.53161-1.4295-3.44553-.9139-.91392-2.153-1.42801-3.4455-1.4295Zm0 7.50003c-.5192 0-1.02669-.154-1.45837-.4424-.43168-.2885-.76813-.6984-.96681-1.1781-.19868-.4796-.25067-1.0074-.14938-1.5166.10129-.50924.35129-.97697.71841-1.34408.36711-.36712.83484-.61712 1.34405-.71841.5092-.10129 1.037-.0493 1.5166.14938.4797.19868.8897.53513 1.1781.96681.2884.43168.4424.9392.4424 1.4584 0 .6962-.2766 1.3638-.7688 1.8561-.4923.4923-1.16.7689-1.8562.7689Zm8.625-2.551v-.1481l1.3125-1.64155c.1102-.13755.1865-.29905.2228-.4715s.0316-.35102-.0137-.52131c-.2369-.89334-.5909-1.75142-1.0528-2.55188-.089-.15264-.2127-.28218-.3611-.37811-.1484-.09594-.3173-.15557-.493-.17408l-2.0888-.23437-.104-.10406-.2344-2.08969c-.0186-.17556-.0783-.34426-.1743-.49247-.0959-.1482-.2254-.27175-.3779-.36066-.8005-.46341-1.6589-.81869-2.5528-1.056559-.1704-.044683-.349-.048704-.5213-.01174-.1723.036965-.3335.113881-.4706.224549l-1.6415 1.3125h-.1482l-1.64152-1.3125C9.14683.9524 8.98532.87608 8.81288.839767c-.17245-.036314-.35102-.031606-.52132.013744-.89357.238319-1.75165.593909-2.55187 1.057499-.15205.08854-.28121.2115-.37712.35901-.0959.14752-.15586.31547-.17507.49037l-.23437 2.08875-.10407.10406-2.08968.23437c-.17556.01865-.34426.07835-.49247.17428-.14821.09593-.27176.22539-.36066.37791-.46211.80072-.81613 1.65912-1.052812 2.55281-.045195.17016-.049823.34855-.013512.52082.03631.17227.112546.33362.222574.47106L2.375 10.926v.1481l-1.3125 1.6416c-.110173.1375-.186492.299-.222806.4715-.036313.1724-.031605.351.013744.5213.238622.8936.594522 1.7517 1.058442 2.5519.08844.1519.21126.281.3586.3769.14734.0959.3151.1559.48983.1753l2.08875.2325.10407.104.23437 2.0916c.01865.1756.07835.3443.17428.4925.09592.1482.22538.2717.37791.3606.80052.4634 1.65893.8187 2.55281 1.0566.17045.0447.349.0487.52129.0117.17228-.0369.33347-.1139.47059-.2245l1.64152-1.3125h.1482l1.6415 1.3125c.1376.1101.2991.1865.4715.2228.1725.0363.351.0316.5213-.0138.8934-.2368 1.7514-.5908 2.5519-1.0528.1524-.0883.2819-.2112.3782-.3587.0962-.1475.1565-.3156.1759-.4907l.2325-2.0887.104-.1041 2.0897-.239c.1751-.0194.3432-.0797.4907-.1759.1475-.0963.2704-.2258.3587-.3782.4634-.8005.8187-1.6589 1.0566-2.5528.0448-.1699.0493-.3479.013-.5198-.0363-.172-.1124-.333-.2221-.4702l-1.3125-1.6416Zm-2.2612-.4584c.015.256.015.5127 0 .7687-.0168.2784.0704.553.2446.7707l1.2038 1.5047c-.1136.3363-.2492.6648-.406.9834l-1.9153.2128c-.2773.0317-.5329.1654-.7171.375-.1704.1919-.3519.3735-.5438.5438-.2096.1842-.3433.4398-.375.7171l-.2119 1.9144c-.3185.1574-.647.2936-.9834.4078l-1.5047-1.2047c-.1997-.1593-.4477-.2459-.7031-.2456h-.0675c-.2561.015-.5127.015-.7688 0-.2781-.0165-.5525.0703-.7706.2438l-1.50469 1.2047c-.33634-.1137-.66486-.2493-.98343-.406l-.21282-1.9153c-.0317-.2773-.16536-.5329-.375-.7172-.19187-.1703-.37344-.3519-.54375-.5437-.18426-.2097-.43988-.3433-.71718-.375l-1.91438-.2119c-.15734-.3185-.29357-.647-.40781-.9834l1.20375-1.5047c.17424-.2177.26144-.4923.24469-.7707-.01501-.256-.01501-.5127 0-.7687.01675-.2783-.07045-.553-.24469-.77063L3.18781 8.34038c.11364-.33634.24924-.66486.40594-.98343l1.91531-.21281c.27731-.03171.53292-.16537.71719-.375.17031-.19188.35188-.37345.54375-.54375.20964-.18427.3433-.43989.375-.71719l.21188-1.91438c.31852-.15734.64704-.29357.98343-.40781L9.845 4.3907c.2181.17343.4925.26023.7706.24375.2561-.015.5127-.015.7688 0 .2782.01701.5528-.06985.7706-.24375l1.5047-1.20469c.3364.11424.6649.25047.9834.40781l.2128 1.91532c.0317.2773.1654.53292.375.71718.1919.17031.3735.35188.5438.54375.1843.20964.4399.3433.7172.375l1.9143.21188c.1574.31852.2936.64704.4079.98343l-1.2038 1.50469c-.1749.21743-.2628.49203-.2465.77063Z"/></svg>',
  lightbulb: '<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 13 16"><path fill="currentColor" d="M9.84994 14.5002c0 .1989-.07902.3897-.21967.5303-.14066.1407-.33142.2197-.53033.2197h-5c-.19891 0-.38968-.079-.53033-.2197-.14065-.1406-.21967-.3314-.21967-.5303 0-.1989.07902-.3897.21967-.5303.14065-.1407.33142-.2197.53033-.2197h5c.19891 0 .38967.079.53033.2197.14065.1406.21967.3314.21967.5303Zm2.49996-8.00001c.0023.87138-.1945 1.73175-.5755 2.51544-.381.78368-.9359 1.46997-1.6226 2.00647-.093.0708-.16853.162-.22085.2665-.05233.1046-.08004.2197-.08101.3366v.125c0 .3315-.1317.6495-.36612.8839-.23442.2344-.55236.3661-.88388.3661h-4c-.33152 0-.64947-.1317-.88389-.3661-.23442-.2344-.36611-.5524-.36611-.8839v-.125c-.00014-.115-.0267-.2284-.07763-.3314-.05094-.1031-.12488-.193-.21612-.263-.68477-.5334-1.23925-1.2155-1.62148-1.9948-.38223-.77929-.582201-1.63532-.584772-2.50331C.833063 3.41832 3.34994.825195 6.46181.750195c.76665-.018422 1.52923.116696 2.24287.397405.71365.2807 1.36392.70132 1.91262 1.23711.5486.53578.9846 1.1759 1.2821 1.88268.2976.70678.4508 1.46594.4505 2.2328Zm-1.5 0c.0002-.5669-.113-1.12811-.3331-1.65058-.22-.52247-.54226-.99565-.9479-1.39168-.40563-.39602-.8864-.70689-1.414-.91431-.52759-.20741-1.09135-.30718-1.65809-.29343-2.29937.055-4.15937 1.97188-4.14687 4.27375.00214.64152.15011 1.27416.43271 1.85009.2826.57592.69244 1.08006 1.19854 1.47429.25496.19678.46453.44618.61444.73128.14992.285.23665.599.25431.9206h3.50625c.018-.3222.10486-.6368.25472-.9226.14986-.2859.35924-.5362.61403-.73428.50754-.39672.91776-.90412 1.19936-1.4835.2817-.57938.4272-1.21543.4256-1.85963Zm-1.25434-.3325c-.06636-.56119-.28826-1.09265-.64067-1.53441-.35241-.44175-.82128-.7762-1.3537-.96559-.1861-.0608-.38859-.04643-.56423.04006-.17565.08648-.31051.23821-.37579.42278-.06527.18458-.05579.38736.02642.56504.08222.17767.23065.31616.4136.38587.26755.09379.50353.26056.68124.48146.17771.2209.29008.48712.32438.76854.02188.19776.12142.37872.27673.50308.0769.06157.16517.1074.25978.13486.09461.02747.1937.03602.29162.02519.09791-.01083.19274-.04085.27905-.08833.08632-.04748.16244-.1115.22402-.1884.06158-.07689.1074-.16517.13487-.25978.02746-.09461.03602-.1937.02518-.29162l-.0025.00125Z"/></svg>',
  "file-search": '<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 12 14"><path fill="currentColor" d="M11.5306 3.97 8.03063.47C7.96097.400261 7.87826.344936 7.78721.307186 7.69616.269437 7.59856.250005 7.5.25h-6C1.16848.25.850537.381696.616117.616117.381696.850537.25 1.16848.25 1.5v11c0 .3315.131696.6495.366117.8839.23442.2344.552363.3661.883883.3661h9c.3315 0 .6495-.1317.8839-.3661.2344-.2344.3661-.5524.3661-.8839v-8c0-.19876-.0789-.38938-.2194-.53ZM9.4375 4H8V2.5625L9.4375 4ZM1.75 12.25V1.75H6.5v3c0 .19891.07902.38968.21967.53033.14065.14065.33142.21967.53033.21967h3v6.75h-8.5Zm6.03-3.03063c.2284-.47897.28015-1.02326.14613-1.53671-.13403-.51344-.44521-.96299-.87858-1.26923-.43337-.30623-.96102-.44945-1.48975-.40433-.52872.04511-1.02449.27564-1.39971.65086-.37523.37522-.60576.87099-.65087 1.39972-.04511.52872.0981 1.05638.40434 1.48975.30624.43336.75579.74457 1.26923.87857.51344.134 1.05773.0823 1.53671-.1461l.75.75c.1409.1409.33199.22.53125.22s.39035-.0791.53125-.22c.1409-.1409.22005-.332.22005-.5313 0-.1992-.07915-.3903-.22005-.53123l-.75-.75ZM5 8.25c0-.14834.04399-.29334.1264-.41668.08241-.12333.19954-.21946.33659-.27623.13704-.05676.28784-.07162.43333-.04268.14548.02894.27912.10037.38401.20526.10489.10489.17632.23853.20526.38401.02894.14549.01408.29629-.04268.43333-.05677.13705-.1529.25418-.27623.33659C6.04334 8.95601 5.89834 9 5.75 9c-.19891 0-.38968-.07902-.53033-.21967C5.07902 8.63968 5 8.44891 5 8.25Z"/></svg>',
  star: '<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 15 15"><path fill="currentColor" d="M14.5873 6.00333c-.0759-.23292-.2187-.43838-.4105-.59083-.1918-.15245-.4241-.24519-.6682-.26667L9.9461 4.8377 8.55235 1.51645c-.09588-.22586-.25611-.4185-.46072-.553929-.2046-.135425-.44454-.207638-.68991-.207638-.24537 0-.48531.072213-.68991.207638-.20461.135429-.36484.328069-.46071.553929L4.85547 4.8377l-3.5625.30813c-.24538.02032-.479299.11265-.6724.26542-.193101.15276-.336784.35916-.413023.59328-.076238.23412-.081634.48554-.015512.72272s.200817.44954.387185.61045l2.7075 2.3625-.8125 3.515c-.05572.2394-.03965.4898.04619.7201.08585.2303.23767.4301.43648.5746.19881.1444.4358.2271.68132.2376.24553.0105.48871-.0516.69914-.1785l3.0625-1.86 3.06245 1.86c.2105.1267.4536.1886.699.178.2454-.0106.4822-.0933.6809-.2377.1987-.1444.3505-.3442.4363-.5743.0858-.2302.102-.4805.0463-.7198l-.8125-3.515 2.7075-2.3625c.1859-.16149.32-.37429.3853-.61168.0654-.23739.0592-.4888-.0178-.72269Zm-4.1718 2.66375c-.1714.14913-.299.34215-.3689.55831-.0699.21617-.07959.44731-.028.66857l.7119 3.08254-2.68378-1.63c-.1949-.1187-.41869-.1815-.64687-.1815-.22819 0-.45198.0628-.64688.1815l-2.68375 1.63.71188-3.08254c.05158-.22126.04189-.4524-.02803-.66857-.06993-.21616-.19745-.40918-.36885-.55831L2.00359 6.5902l3.13376-.27125c.22692-.01943.44417-.10073.62809-.23507.18393-.13433.32748-.31654.41503-.5268l1.21938-2.90563 1.21937 2.90563c.08755.21026.2311.39247.41503.5268.18392.13434.40117.21564.62809.23507l3.13376.27125-2.3806 2.07688Z"/></svg>',
  checkmark: '<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 10 8"><path fill="#fff" d="M9.47334.806574C9.41136.744088 9.33763.694492 9.25639.660646S9.08802.609375 9.00001.609375 8.82486.6268 8.74362.660646s-.15497.083442-.21695.145928L3.56001 5.77991 1.47334 3.68657c-.06435-.06216-.14031-.11103-.22354-.14383-.08324-.03281-.17212-.04889-.261578-.04735-.089454.00155-.177727.0207-.259779.05637-.082052.03566-.156277.08713-.218436.15148-.062159.06435-.111035.14031-.143837.22355-.032803.08323-.04889.17212-.047342.26157.001547.08945.020699.17773.056361.25978.035663.08205.087137.15627.151485.21843l2.559996 2.56c.06198.06249.13571.11209.21695.14593.08124.03385.16838.05127.25639.05127s.17514-.01742.25638-.05127c.08124-.03384.15498-.08344.21695-.14593l5.44-5.44c.06767-.06242.12168-.13819.15861-.22253.03694-.08433.05601-.1754.05601-.26747 0-.09206-.01907-.18313-.05601-.26747-.03693-.08433-.09094-.160098-.15861-.222526Z"/></svg>',
  "dots-three": '<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 16 4"><path fill="#fff" d="M9.5 2c0 .29667-.08797.58668-.2528.83336-.16482.24667-.39908.43893-.67317.55246-.27409.11353-.57569.14324-.86666.08536-.29098-.05788-.55825-.20074-.76803-.41052-.20978-.20978-.35264-.47705-.41052-.76802-.05788-.29098-.02817-.59258.08536-.86666.11353-.27409.30579-.508362.55247-.673184C7.41332.587974 7.70333.5 8 .5c.39783 0 .77936.158036 1.06066.43934C9.34196 1.22064 9.5 1.60218 9.5 2ZM1.625.5c-.29667 0-.58668.087974-.833354.252796-.246674.164822-.438933.399094-.552465.673184-.113531.27408-.1432361.57568-.085358.86666.057878.29097.200739.55824.410518.76802.209778.20978.477049.35264.768029.41052.29097.05788.59257.02817.86666-.08536.27408-.11353.50835-.30579.67318-.55246C3.03703 2.58668 3.125 2.29667 3.125 2c0-.39782-.15803-.77936-.43934-1.06066C2.40436.658036 2.02283.5 1.625.5Zm12.75 0c-.2967 0-.5867.087974-.8334.252796-.2466.164822-.4389.399094-.5524.673184-.1135.27408-.1433.57568-.0854.86666.0579.29097.2008.55824.4105.76802.2098.20978.4771.35264.7681.41052.2909.05788.5925.02817.8666-.08536s.5084-.30579.6732-.55246c.1648-.24668.2528-.53669.2528-.83336 0-.39782-.158-.77936-.4393-1.06066C15.1544.658036 14.7728.5 14.375.5Z"/></svg>',
  copy: '<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 10 11"><path fill="#fff" d="M9.125.8125h-6c-.14918 0-.29226.059263-.39775.164752-.10549.105488-.16475.248568-.16475.397748v1.6875H.875c-.149184 0-.292258.05926-.397748.16475C.371763 3.33274.3125 3.47582.3125 3.625v6c0 .14918.059263.29226.164752.3977.10549.1055.248564.1648.397748.1648h6c.14918 0 .29226-.0593.39775-.1648.10549-.10544.16475-.24852.16475-.3977V7.9375H9.125c.14918 0 .29226-.05926.39775-.16475.10549-.10549.16475-.24857.16475-.39775v-6c0-.14918-.05926-.29226-.16475-.397748C9.41726.871763 9.27418.8125 9.125.8125Zm-2.8125 8.25h-4.875v-4.875h4.875v4.875Zm2.25-2.25h-1.125V3.625c0-.14918-.05926-.29226-.16475-.39775-.10549-.10549-.24857-.16475-.39775-.16475H3.6875v-1.125h4.875v4.875Z"/></svg>',
  compress: '<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 32 32"><path fill="currentColor" d="M13.84 17.44c-.16-.07-.33-.1-.5-.1H8A1.33 1.33 0 1 0 8 20h2.12l-7.07 7.05a1.33 1.33 0 0 0 .44 2.19 1.33 1.33 0 0 0 1.46-.3L12 21.89V24a1.33 1.33 0 1 0 2.67 0v-5.33a1.37 1.37 0 0 0-.83-1.23Zm-.5-10.77A1.33 1.33 0 0 0 12 8v2.12L4.95 3.05a1.34 1.34 0 1 0-1.9 1.9L10.12 12H8a1.33 1.33 0 0 0 0 2.67h5.33c.18 0 .35-.04.5-.11a1.33 1.33 0 0 0 .84-1.23V8a1.33 1.33 0 0 0-1.34-1.33Zm4.82 7.89c.16.07.33.1.5.1H24A1.33 1.33 0 0 0 24 12h-2.12l7.07-7.05a1.34 1.34 0 1 0-1.9-1.9L20 10.12V8a1.33 1.33 0 0 0-2.67 0v5.33a1.33 1.33 0 0 0 .83 1.23ZM21.88 20H24a1.33 1.33 0 0 0 0-2.67h-5.33c-.18 0-.35.04-.51.11a1.33 1.33 0 0 0-.83 1.23V24A1.33 1.33 0 0 0 20 24v-2.12l7.05 7.07a1.33 1.33 0 0 0 2.19-.44 1.33 1.33 0 0 0-.3-1.46L21.89 20Z"/></svg>',
  grid: '<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 32 32"><path fill="currentColor" d="M13.33 17.33H4a1.33 1.33 0 0 0-1.33 1.34V28A1.33 1.33 0 0 0 4 29.33h9.33A1.33 1.33 0 0 0 14.67 28v-9.33a1.33 1.33 0 0 0-1.34-1.34ZM12 26.67H5.33V20H12v6.67Zm16-24h-9.33A1.33 1.33 0 0 0 17.33 4v9.33a1.33 1.33 0 0 0 1.34 1.34H28a1.33 1.33 0 0 0 1.33-1.34V4A1.33 1.33 0 0 0 28 2.67ZM26.67 12H20V5.33h6.67V12ZM28 17.33h-9.33a1.33 1.33 0 0 0-1.34 1.34V28a1.33 1.33 0 0 0 1.34 1.33H28A1.33 1.33 0 0 0 29.33 28v-9.33A1.33 1.33 0 0 0 28 17.33Zm-1.33 9.34H20V20h6.67v6.67Zm-13.34-24H4A1.33 1.33 0 0 0 2.67 4v9.33A1.33 1.33 0 0 0 4 14.67h9.33a1.33 1.33 0 0 0 1.34-1.34V4a1.33 1.33 0 0 0-1.34-1.33ZM12 12H5.33V5.33H12V12Z"/></svg>',
  puzzle: '<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 33 32"><path fill="currentColor" d="M23 29.33H7a4 4 0 0 1-4-4V12a4 4 0 0 1 4-4h1.33A5.33 5.33 0 0 1 19 8h4a1.33 1.33 0 0 1 1.33 1.33v4a5.33 5.33 0 0 1 0 10.67v4A1.33 1.33 0 0 1 23 29.33ZM7 10.67A1.33 1.33 0 0 0 5.67 12v13.33A1.33 1.33 0 0 0 7 26.67h14.67v-4.24a1.33 1.33 0 0 1 1.77-1.27 2.36 2.36 0 0 0 2.32-.3A2.67 2.67 0 0 0 27 19.02a2.67 2.67 0 0 0-.64-2.12 2.52 2.52 0 0 0-2.9-.74 1.33 1.33 0 0 1-1.77-1.26v-4.24h-4.26a1.33 1.33 0 0 1-1.34-1.78 2.36 2.36 0 0 0-.3-2.32 2.59 2.59 0 0 0-4-.57A2.67 2.67 0 0 0 11 8c0 .3.06.6.17.9a1.33 1.33 0 0 1-1.26 1.77H7Z"/></svg>',
  approveUser: '<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 33 32"><path fill="currentColor" d="M18.4 16.3a6.56 6.56 0 0 0 2.27-4.97 6.67 6.67 0 1 0-12.74 2.73c.39.86.96 1.62 1.67 2.23A10.67 10.67 0 0 0 3.33 26 1.33 1.33 0 0 0 6 26a8 8 0 0 1 16 0 1.33 1.33 0 0 0 2.67 0 10.67 10.67 0 0 0-6.27-9.7Zm-4.4-.97a4 4 0 1 1 0-8 4 4 0 0 1 0 8Zm15.61-3.16a1.33 1.33 0 0 0-1.9 0l-2.66 2.67-.82-.84a1.33 1.33 0 1 0-1.9 1.88l1.79 1.79a1.33 1.33 0 0 0 1.88 0l3.56-3.56a1.33 1.33 0 0 0 .05-1.94Z"/></svg>',
  checkCircle: '<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 33 32"><path fill="currentColor" d="m20.3 11.72-5.73 5.73-2.2-2.2a1.33 1.33 0 1 0-1.88 1.88l3.14 3.15a1.33 1.33 0 0 0 1.88 0l6.66-6.67a1.33 1.33 0 1 0-1.88-1.89Zm-3.63-9.05a13.33 13.33 0 1 0 0 26.66 13.33 13.33 0 0 0 0-26.66Zm0 24a10.67 10.67 0 1 1 0-21.34 10.67 10.67 0 0 1 0 21.34Z"/></svg>',
  resizeImage: '<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 33 32"><path fill="currentColor" d="M17.67 14.67H3A1.33 1.33 0 0 0 1.67 16v12A1.33 1.33 0 0 0 3 29.33h14.67A1.33 1.33 0 0 0 19 28V16a1.33 1.33 0 0 0-1.33-1.33Zm-7.42 12 2.58-2.58a.4.4 0 0 1 .66 0l2.56 2.58h-5.8Zm6.08-3.5-.96-.94a3.21 3.21 0 0 0-4.44 0l-4.45 4.44H4.33v-9.34h12v5.84ZM3 5.48a1.33 1.33 0 0 0 1.15-.65 1.4 1.4 0 0 0-.16-1.8A1.33 1.33 0 0 0 3 2.67 1.33 1.33 0 0 0 1.67 4v.13A1.33 1.33 0 0 0 3 5.48Zm10.55-.15h.25a1.33 1.33 0 0 0 0-2.66h-.25a1.33 1.33 0 0 0 0 2.66ZM3 11.71a1.33 1.33 0 0 0 1.33-1.34v-.29a1.33 1.33 0 0 0-2.66 0v.3A1.33 1.33 0 0 0 3 11.7Zm16.12-9.04h-.25a1.33 1.33 0 0 0 0 2.66h.25a1.33 1.33 0 0 0 0-2.66ZM8.22 5.33h.25a1.33 1.33 0 1 0 0-2.66H8.2a1.33 1.33 0 1 0 0 2.66Zm21.45 3.2a1.33 1.33 0 0 0-1.34 1.34v.28a1.33 1.33 0 0 0 2.67 0v-.28a1.33 1.33 0 0 0-1.33-1.34Zm-6.5 18.14h-.33a1.33 1.33 0 1 0 0 2.66h.32a1.33 1.33 0 0 0 0-2.66Zm6.36-24a1.33 1.33 0 1 0 .36 2.64A1.33 1.33 0 0 0 31 4.15V4a1.45 1.45 0 0 0-1.47-1.33Zm.14 11.86a1.33 1.33 0 0 0-1.34 1.34v.29a1.33 1.33 0 1 0 2.67 0v-.3a1.33 1.33 0 0 0-1.33-1.33ZM24.45 2.67h-.25a1.33 1.33 0 1 0 0 2.66h.25a1.33 1.33 0 1 0 0-2.66Zm5.22 24A1.33 1.33 0 0 0 28.34 28a1.33 1.33 0 0 0 1.33 1.33A1.45 1.45 0 0 0 31 27.87a1.33 1.33 0 0 0-1.33-1.2Zm0-6.08a1.33 1.33 0 0 0-1.34 1.33v.3a1.33 1.33 0 0 0 2.67 0v-.35a1.33 1.33 0 0 0-1.33-1.34v.06Z"/></svg>',
  searchFile: '<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 32 32"><path fill="currentColor" d="M16 26.67H6.67a1.33 1.33 0 0 1-1.34-1.34V6.67a1.33 1.33 0 0 1 1.34-1.34h6.66v4a4 4 0 0 0 4 4h4v1.34a1.33 1.33 0 0 0 2.67 0v-2.75a1.75 1.75 0 0 0-.08-.36v-.12a1.43 1.43 0 0 0-.25-.37l-8-8c-.11-.1-.24-.2-.38-.26h-.12a1.17 1.17 0 0 0-.44-.14H6.67a4 4 0 0 0-4 4v18.66a4 4 0 0 0 4 4H16a1.33 1.33 0 0 0 0-2.66ZM16 7.2l3.45 3.46h-2.12A1.33 1.33 0 0 1 16 9.33V7.21Zm-6.67 3.46a1.33 1.33 0 0 0 0 2.66h1.34a1.33 1.33 0 0 0 0-2.66H9.33Zm19.62 16.38-1.56-1.54a4.59 4.59 0 0 0-.72-5.51 4.65 4.65 0 0 0-8 3.32 4.61 4.61 0 0 0 6.84 4.07l1.54 1.56a1.33 1.33 0 0 0 2.19-.44 1.33 1.33 0 0 0-.3-1.46Zm-4.23-2.33a2.05 2.05 0 0 1-2.81 0 2 2 0 0 1 0-2.81 2 2 0 0 1 1.34-.58 1.96 1.96 0 0 1 1.47 3.39ZM17.33 16h-8a1.33 1.33 0 0 0 0 2.67h8a1.33 1.33 0 1 0 0-2.67Zm-2.66 8a1.33 1.33 0 0 0 0-2.67H9.33a1.33 1.33 0 0 0 0 2.67h5.34Z"/></svg>',
  image: '<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 33 32"><path fill="currentColor" d="M26 2.67H7.33a4 4 0 0 0-4 4v18.66a4 4 0 0 0 4 4H26c.22 0 .44-.02.65-.06l.4-.1h.16l.5-.18.17-.1c.13-.08.28-.14.41-.24.18-.13.35-.27.5-.42l.1-.12c.13-.14.25-.28.36-.43l.12-.17c.1-.15.18-.3.24-.47l.1-.2.16-.5v-.2c.07-.27.12-.54.13-.8V6.66a4 4 0 0 0-4-4Zm-18.67 24A1.33 1.33 0 0 1 6 25.33V19.6l4.39-4.4a1.33 1.33 0 0 1 1.89 0l11.47 11.48H7.33Zm20-1.34c0 .17-.03.33-.1.48a1.33 1.33 0 0 1-.22.35l-7.13-7.13 1.17-1.18a1.33 1.33 0 0 1 1.9 0l4.38 4.4v3.08Zm0-6.85L24.83 16a4.1 4.1 0 0 0-5.66 0L18 17.17l-3.84-3.84a4.1 4.1 0 0 0-5.65 0L6 15.81V6.67a1.33 1.33 0 0 1 1.33-1.34H26a1.33 1.33 0 0 1 1.33 1.34v11.81Z"/></svg>',
  robot: '<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 33 32"><path fill="currentColor" d="M12.67 20a1.33 1.33 0 1 0 0 2.67 1.33 1.33 0 0 0 0-2.67Zm-9.34-1.33A1.33 1.33 0 0 0 2 20v2.67a1.33 1.33 0 1 0 2.67 0V20a1.33 1.33 0 0 0-1.34-1.33Zm26.67 0A1.33 1.33 0 0 0 28.67 20v2.67a1.33 1.33 0 1 0 2.66 0V20A1.33 1.33 0 0 0 30 18.67Zm-6.67-9.34H18v-1.7a2.67 2.67 0 0 0 .55-4.18 2.67 2.67 0 0 0-4.19 3.2c.24.41.57.74.97.98v1.7H10a4 4 0 0 0-4 4v12a4 4 0 0 0 4 4h13.33a4 4 0 0 0 4-4v-12a4 4 0 0 0-4-4ZM18.96 12l-.67 2.67h-3.25L14.37 12h4.59Zm5.7 13.33a1.33 1.33 0 0 1-1.33 1.34H10a1.33 1.33 0 0 1-1.33-1.34v-12A1.33 1.33 0 0 1 10 12h1.63l1.04 4.32A1.33 1.33 0 0 0 14 17.33h5.33a1.33 1.33 0 0 0 1.34-1.01L21.7 12h1.62a1.33 1.33 0 0 1 1.34 1.33v12Zm-4-5.33a1.33 1.33 0 1 0 0 2.67 1.33 1.33 0 0 0 0-2.67Z"/></svg>',
  sitemap: '<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 33 32"><path fill="currentColor" d="M29.67 20H27v-4a1.33 1.33 0 0 0-1.33-1.33h-8V12h2.66a1.33 1.33 0 0 0 1.34-1.33v-8a1.33 1.33 0 0 0-1.34-1.34h-8A1.33 1.33 0 0 0 11 2.67v8A1.33 1.33 0 0 0 12.33 12H15v2.67H7A1.33 1.33 0 0 0 5.67 16v4H3a1.33 1.33 0 0 0-1.33 1.33v8A1.33 1.33 0 0 0 3 30.67h8a1.33 1.33 0 0 0 1.33-1.34v-8A1.33 1.33 0 0 0 11 20H8.33v-2.67h16V20h-2.66a1.33 1.33 0 0 0-1.34 1.33v8a1.33 1.33 0 0 0 1.34 1.34h8A1.33 1.33 0 0 0 31 29.33v-8A1.33 1.33 0 0 0 29.67 20Zm-20 2.67V28H4.33v-5.33h5.34Zm4-13.34V4H19v5.33h-5.33ZM28.33 28H23v-5.33h5.33V28Z"/></svg>',
  gauge: '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 256 256"><path fill="currentColor" d="M207 80.7A111.2 111.2 0 0 0 128 48h-.4C66.1 48.2 16 99 16 161.1V184a16 16 0 0 0 16 16h192a16 16 0 0 0 16-16v-24a111.3 111.3 0 0 0-33-79.3ZM224 184H119.7l54.8-75.3a8 8 0 0 0-13-9.4L100 184H32v-22.9c0-3 .1-6 .4-9.1H56a8 8 0 0 0 0-16H35.3A96.7 96.7 0 0 1 120 64.3V88a8 8 0 0 0 16 0V64.3a96.1 96.1 0 0 1 85 71.7h-21a8 8 0 0 0 0 16h23.7c.2 2.6.3 5.3.3 8Z"/></svg>',
  "person-arms-spread": '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 256 256"><path fill="currentColor" d="M160 40a32 32 0 1 0-32 32 32 32 0 0 0 32-32Zm-32 16a16 16 0 1 1 16-16 16 16 0 0 1-16 16Zm103.5 31.7A19.6 19.6 0 0 0 212 72H44a20 20 0 0 0-8.4 38.2h.1l50.8 22.3-21 79.7a20 20 0 0 0 36.5 16.6l26-44.9 26 44.9a20 20 0 0 0 36.4-16.5l-21-79.7 50.8-22.4a19.6 19.6 0 0 0 11.3-22.5Zm-17.8 8-57 25a8 8 0 0 0-4.4 9.3l22.8 87a7 7 0 0 0 .5 1.4 4 4 0 0 1-5 5.4 4 4 0 0 1-2.2-2 6.3 6.3 0 0 0-.4-.7L135 164a8 8 0 0 0-14 0l-33 57a6.3 6.3 0 0 0-.3.7 4 4 0 0 1-2.3 2 4 4 0 0 1-5-5.4 7 7 0 0 0 .5-1.4l22.8-86.9a8 8 0 0 0-4.5-9.4l-56.9-25A4 4 0 0 1 44 88h168a4 4 0 0 1 1.7 7.6Z"/></svg>',
  "arrow-left": '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 256 256"><path d="M224 128a8 8 0 0 1-8 8H59.31l58.35 58.34a8 8 0 0 1-11.32 11.32l-72-72a8 8 0 0 1 0-11.32l72-72a8 8 0 0 1 11.32 11.32L59.31 120H216a8 8 0 0 1 8 8Z"/></svg>',
  "houston-detective": '<svg xmlns="http://www.w3.org/2000/svg" width="80.73885" height="73.61774" fill="none"><path fill="url(#a)" d="M4.36139 21.7987c0-4.93847 4.00624-8.9419 8.94818-8.9419h49.21475c4.9424 0 8.9486 4.00343 8.9486 8.9419v32.4145c0 4.93841-4.0062 8.94186-8.9486 8.94186H13.30957c-4.94194 0-8.94818-4.00345-8.94818-8.94187z"/><path fill="url(#b)" d="M6.98283 22.62008c0-3.94523 3.2151-7.14348 7.18112-7.14348h47.50578c3.9662 0 7.18144 3.19825 7.18144 7.14348V53.3917c0 3.94545-3.21523 7.14387-7.18144 7.14387H14.16395c-3.96602 0-7.18112-3.19842-7.18112-7.14387z"/><path fill="#fff" d="M25.47554 43.24548c0 3.0745-2.4941 5.56686-5.57076 5.56686-3.0766 0-5.57077-2.49236-5.57077-5.56686s2.49416-5.56686 5.57077-5.56686c3.07666 0 5.57076 2.49236 5.57076 5.56686zm12.25545 3.34c1.84596 0 3.34243-1.49538 3.34243-3.3401h2.22833c0 3.0745-2.49415 5.56686-5.57076 5.56686-3.07666 0-5.57077-2.49236-5.57077-5.56686h2.22828c0 1.84472 1.49647 3.3401 3.34249 3.3401zm23.39735-3.34c0 3.0745-2.49419 5.56686-5.57082 5.56686-3.07658 0-5.57064-2.49236-5.57064-5.56686s2.49406-5.56686 5.57064-5.56686c3.07664 0 5.57082 2.49236 5.57082 5.56686z"/><g filter="url(#c)" opacity=".5" transform="matrix(.5 0 0 .5 0 .1236)"><path fill="#000" d="M104.83 26.2341c3.182 2.7053-1.591 12.5125-28.6385 12.5125-27.0471 0-31.8199-9.807-28.6381-12.5125 3.1818-2.7056 12.8217 1.6907 28.6381 1.6907 15.8164 0 25.4555-4.3961 28.6385-1.6907z"/></g><g filter="url(#d)" transform="matrix(.5 0 0 .5 0 .1236)"><path fill="#663c0d" d="M79.1496 5.30492c-.7721.51031-1.7701.50914-2.5512.01272-4.5064-2.86385-11.2943-6.50075-14.3451-2.91332-4.1955 4.93349-6.3656 17.86268-6.3656 20.24438 0 1.9053 14.7566 5.784 21.9902 7.4852 7.2336-1.7012 21.9902-5.5799 21.9902-7.4852 0-2.3817-3.3275-15.31089-7.523-20.24438-3.0463-3.58212-8.8653.03873-13.1955 2.9006z"/></g><g filter="url(#e)" transform="matrix(.5 0 0 .5 0 .1236)"><path fill="#421913" d="M56.4531 17.9819h42.5617l2.5902 11.2845H54.1523Z"/></g><g filter="url(#f)" transform="matrix(.5 0 0 .5 0 .1236)"><path fill="#663c0d" fill-rule="evenodd" d="M51.2044 21.7432c-16.6972 0-13.3879 5.5266-6.2519 8.7939-.0399.0582-.0601.117-.0601.1761 0 1.598 14.768 2.8934 32.9853 2.8934 18.2172 0 32.9853-1.2954 32.9853-2.8934 0-.0479-.013-.0955-.04-.1429 7.075-3.2629 10.412-8.8271-6.083-8.8271-11.1883 0-20.1017 1.3613-26.5593 2.8804-6.559-1.5191-15.6124-2.8804-26.9763-2.8804z" clip-rule="evenodd"/></g><g filter="url(#g)" transform="matrix(.5 0 0 .5 0 .1236)"><path fill="#d2a460" d="M11.479 82.4367c2.6687 3.4228 6.7106 11.7506 9.859 13.5839 15.5288 9.0424 37.7225 24.2464 56.4741 24.5724 18.7516-.326 36.9159-14.482 52.4449-23.5245 3.148-1.8333 7.19-7.0175 9.859-10.4403 1.45-1.8596 2.77-3.1993 2.77-3.1993s6.091 36.1791-1.328 39.8061c-28.151 13.763-103.3705 13.763-131.5215 0-7.41893-3.627-2.11069-40.854-1.32762-45.0455.27567 0 2.30452 3.65 2.77012 4.2472z"/></g><g filter="url(#h)" transform="matrix(.5 0 0 .5 0 .1236)"><path fill="#d2a460" d="m142.911 68.731-65.1786 51.865v9.447c21.7262-9.397 66.3766-34.1088 71.1716-39.8496 4.795-9.6363 1.249-21.4624-5.993-21.4624z"/></g><g filter="url(#i)" transform="matrix(.5 0 0 .5 0 .1236)"><path fill="#d2a460" d="m8.67377 68.731 69.05863 51.865v9.447C54.7129 120.646 7.40376 95.9342 2.32368 90.1934-2.75639 80.5571 1.00072 68.731 8.67377 68.731Z"/></g><g filter="url(#j)" transform="matrix(.5 0 0 .5 0 .1236)"><path fill="#421913" d="m127.123 109.203 7.779 1.798-5.636 33.161c-.657 2.993-12.371.06-11.121-2.774z"/></g><g filter="url(#k)" transform="matrix(.5 0 0 .5 0 .1236)"><rect width="10.9039" height="5.2351" x="126.291" y="106.322" fill="url(#l)" rx=".52395" transform="rotate(13 126.291 106.322)"/></g><g filter="url(#m)" transform="matrix(.5 0 0 .5 0 .1236)"><circle cx="135.744" cy="88.0296" r="22.2183" fill="url(#n)" transform="rotate(43 135.744 88.0296)"/></g><ellipse cx="79.76932" cy="-14.01218" fill="url(#o)" rx="9.16844" ry="9.16799" transform="rotate(42.97994) skewX(-.04011)"/><defs><filter id="c" width="80.5643" height="34.955" x="35.9092" y="14.5728" color-interpolation-filters="sRGB" filterUnits="userSpaceOnUse"><feFlood flood-opacity="0" result="BackgroundImageFix"/><feBlend in="SourceGraphic" in2="BackgroundImageFix" result="shape"/><feGaussianBlur result="effect1_foregroundBlur_847_1831" stdDeviation="5.3906"/></filter><filter id="d" width="46.8739" height="31.8277" x="54.1516" y="-.24722" color-interpolation-filters="sRGB" filterUnits="userSpaceOnUse"><feFlood flood-opacity="0" result="BackgroundImageFix"/><feBlend in="SourceGraphic" in2="BackgroundImageFix" result="shape"/><feColorMatrix in="SourceAlpha" result="hardAlpha" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/><feOffset dx="-1.73607" dy="-1.15738"/><feGaussianBlur stdDeviation="1.15738"/><feComposite in2="hardAlpha" k2="-1" k3="1" operator="arithmetic"/><feColorMatrix values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.35 0"/><feBlend in2="shape" result="effect1_innerShadow_847_1831"/><feColorMatrix in="SourceAlpha" result="hardAlpha" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/><feOffset dx="1.15738" dy="1.44672"/><feGaussianBlur stdDeviation="1.15738"/><feComposite in2="hardAlpha" k2="-1" k3="1" operator="arithmetic"/><feColorMatrix values="0 0 0 0 0.579167 0 0 0 0 0.436815 0 0 0 0 0.277517 0 0 0 0.8 0"/><feBlend in2="effect1_innerShadow_847_1831" result="effect2_innerShadow_847_1831"/></filter><filter id="e" width="49.0455" height="13.9403" x="52.559" y="17.9819" color-interpolation-filters="sRGB" filterUnits="userSpaceOnUse"><feFlood flood-opacity="0" result="BackgroundImageFix"/><feBlend in="SourceGraphic" in2="BackgroundImageFix" result="shape"/><feColorMatrix in="SourceAlpha" result="hardAlpha" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/><feOffset dx="-1.59338" dy="2.65563"/><feGaussianBlur stdDeviation="1.59338"/><feComposite in2="hardAlpha" k2="-1" k3="1" operator="arithmetic"/><feColorMatrix values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/><feBlend in2="shape" result="effect1_innerShadow_847_1831"/></filter><filter id="f" width="80.4374" height="14.4674" x="37.3704" y="20.5858" color-interpolation-filters="sRGB" filterUnits="userSpaceOnUse"><feFlood flood-opacity="0" result="BackgroundImageFix"/><feBlend in="SourceGraphic" in2="BackgroundImageFix" result="shape"/><feColorMatrix in="SourceAlpha" result="hardAlpha" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/><feOffset dx="-1.73607" dy="-1.15738"/><feGaussianBlur stdDeviation="1.15738"/><feComposite in2="hardAlpha" k2="-1" k3="1" operator="arithmetic"/><feColorMatrix values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.35 0"/><feBlend in2="shape" result="effect1_innerShadow_847_1831"/><feColorMatrix in="SourceAlpha" result="hardAlpha" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/><feOffset dx="1.15738" dy="1.44672"/><feGaussianBlur stdDeviation="1.15738"/><feComposite in2="hardAlpha" k2="-1" k3="1" operator="arithmetic"/><feColorMatrix values="0 0 0 0 0.579167 0 0 0 0 0.436815 0 0 0 0 0.277517 0 0 0 0.8 0"/><feBlend in2="effect1_innerShadow_847_1831" result="effect2_innerShadow_847_1831"/></filter><filter id="g" width="139.101" height="61.6551" x="6.13867" y="76.0937" color-interpolation-filters="sRGB" filterUnits="userSpaceOnUse"><feFlood flood-opacity="0" result="BackgroundImageFix"/><feBlend in="SourceGraphic" in2="BackgroundImageFix" result="shape"/><feColorMatrix in="SourceAlpha" result="hardAlpha" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/><feOffset dy="-4.19159"/><feGaussianBlur stdDeviation="1.0479"/><feComposite in2="hardAlpha" k2="-1" k3="1" operator="arithmetic"/><feColorMatrix values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/><feBlend in2="shape" result="effect1_innerShadow_847_1831"/><feColorMatrix in="SourceAlpha" result="hardAlpha" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/><feOffset dy="-2.09579"/><feComposite in2="hardAlpha" k2="-1" k3="1" operator="arithmetic"/><feColorMatrix values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.08 0"/><feBlend in2="effect1_innerShadow_847_1831" result="effect2_innerShadow_847_1831"/><feColorMatrix in="SourceAlpha" result="hardAlpha" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/><feOffset dy="4.19159"/><feGaussianBlur stdDeviation="4.19159"/><feComposite in2="hardAlpha" k2="-1" k3="1" operator="arithmetic"/><feColorMatrix values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.15 0"/><feBlend in2="effect2_innerShadow_847_1831" result="effect3_innerShadow_847_1831"/><feColorMatrix in="SourceAlpha" result="hardAlpha" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/><feOffset dy="1.0479"/><feGaussianBlur stdDeviation="1.0479"/><feComposite in2="hardAlpha" k2="-1" k3="1" operator="arithmetic"/><feColorMatrix values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 1 0"/><feBlend in2="effect3_innerShadow_847_1831" mode="overlay" result="effect4_innerShadow_847_1831"/></filter><filter id="h" width="73.3652" height="67.5994" x="77.7324" y="66.6352" color-interpolation-filters="sRGB" filterUnits="userSpaceOnUse"><feFlood flood-opacity="0" result="BackgroundImageFix"/><feBlend in="SourceGraphic" in2="BackgroundImageFix" result="shape"/><feColorMatrix in="SourceAlpha" result="hardAlpha" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/><feOffset dy="-4.19159"/><feGaussianBlur stdDeviation="1.0479"/><feComposite in2="hardAlpha" k2="-1" k3="1" operator="arithmetic"/><feColorMatrix values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/><feBlend in2="shape" result="effect1_innerShadow_847_1831"/><feColorMatrix in="SourceAlpha" result="hardAlpha" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/><feOffset dy="-2.09579"/><feComposite in2="hardAlpha" k2="-1" k3="1" operator="arithmetic"/><feColorMatrix values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.08 0"/><feBlend in2="effect1_innerShadow_847_1831" result="effect2_innerShadow_847_1831"/><feColorMatrix in="SourceAlpha" result="hardAlpha" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/><feOffset dy="4.19159"/><feGaussianBlur stdDeviation="4.19159"/><feComposite in2="hardAlpha" k2="-1" k3="1" operator="arithmetic"/><feColorMatrix values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.15 0"/><feBlend in2="effect2_innerShadow_847_1831" result="effect3_innerShadow_847_1831"/><feColorMatrix in="SourceAlpha" result="hardAlpha" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/><feOffset dy="1.0479"/><feGaussianBlur stdDeviation="1.0479"/><feComposite in2="hardAlpha" k2="-1" k3="1" operator="arithmetic"/><feColorMatrix values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 1 0"/><feBlend in2="effect3_innerShadow_847_1831" mode="overlay" result="effect4_innerShadow_847_1831"/></filter><filter id="i" width="77.7324" height="67.5994" x="0" y="66.6352" color-interpolation-filters="sRGB" filterUnits="userSpaceOnUse"><feFlood flood-opacity="0" result="BackgroundImageFix"/><feBlend in="SourceGraphic" in2="BackgroundImageFix" result="shape"/><feColorMatrix in="SourceAlpha" result="hardAlpha" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/><feOffset dy="-4.19159"/><feGaussianBlur stdDeviation="1.0479"/><feComposite in2="hardAlpha" k2="-1" k3="1" operator="arithmetic"/><feColorMatrix values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/><feBlend in2="shape" result="effect1_innerShadow_847_1831"/><feColorMatrix in="SourceAlpha" result="hardAlpha" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/><feOffset dy="-2.09579"/><feComposite in2="hardAlpha" k2="-1" k3="1" operator="arithmetic"/><feColorMatrix values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.08 0"/><feBlend in2="effect1_innerShadow_847_1831" result="effect2_innerShadow_847_1831"/><feColorMatrix in="SourceAlpha" result="hardAlpha" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/><feOffset dy="4.19159"/><feGaussianBlur stdDeviation="4.19159"/><feComposite in2="hardAlpha" k2="-1" k3="1" operator="arithmetic"/><feColorMatrix values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.15 0"/><feBlend in2="effect2_innerShadow_847_1831" result="effect3_innerShadow_847_1831"/><feColorMatrix in="SourceAlpha" result="hardAlpha" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/><feOffset dy="1.0479"/><feGaussianBlur stdDeviation="1.0479"/><feComposite in2="hardAlpha" k2="-1" k3="1" operator="arithmetic"/><feColorMatrix values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 1 0"/><feBlend in2="effect3_innerShadow_847_1831" mode="overlay" result="effect4_innerShadow_847_1831"/></filter><filter id="j" width="18.4224" height="37.7877" x="116.48" y="109.203" color-interpolation-filters="sRGB" filterUnits="userSpaceOnUse"><feFlood flood-opacity="0" result="BackgroundImageFix"/><feBlend in="SourceGraphic" in2="BackgroundImageFix" result="shape"/><feColorMatrix in="SourceAlpha" result="hardAlpha" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/><feOffset dx="-2.6407" dy="3.69907"/><feGaussianBlur stdDeviation=".78592"/><feComposite in2="hardAlpha" k2="-1" k3="1" operator="arithmetic"/><feColorMatrix values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/><feBlend in2="shape" result="effect1_innerShadow_847_1831"/></filter><filter id="k" width="18.4043" height="14.1577" x="121.811" y="103.873" color-interpolation-filters="sRGB" filterUnits="userSpaceOnUse"><feFlood flood-opacity="0" result="BackgroundImageFix"/><feColorMatrix in="SourceAlpha" result="hardAlpha" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/><feOffset dy=".8512"/><feGaussianBlur stdDeviation="1.70239"/><feComposite in2="hardAlpha" operator="out"/><feColorMatrix values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/><feBlend in2="BackgroundImageFix" result="effect1_dropShadow_847_1831"/><feBlend in="SourceGraphic" in2="effect1_dropShadow_847_1831" result="shape"/><feColorMatrix in="SourceAlpha" result="hardAlpha" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/><feOffset dx=".8512" dy="-2.55359"/><feGaussianBlur stdDeviation="1.70239"/><feComposite in2="hardAlpha" k2="-1" k3="1" operator="arithmetic"/><feColorMatrix values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/><feBlend in2="shape" result="effect2_innerShadow_847_1831"/></filter><filter id="m" width="51.2461" height="51.2466" x="110.121" y="63.2574" color-interpolation-filters="sRGB" filterUnits="userSpaceOnUse"><feFlood flood-opacity="0" result="BackgroundImageFix"/><feColorMatrix in="SourceAlpha" result="hardAlpha" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/><feOffset dy=".8512"/><feGaussianBlur stdDeviation="1.70239"/><feComposite in2="hardAlpha" operator="out"/><feColorMatrix values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/><feBlend in2="BackgroundImageFix" result="effect1_dropShadow_847_1831"/><feBlend in="SourceGraphic" in2="effect1_dropShadow_847_1831" result="shape"/><feColorMatrix in="SourceAlpha" result="hardAlpha" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/><feOffset dx=".8512" dy="-2.55359"/><feGaussianBlur stdDeviation="1.70239"/><feComposite in2="hardAlpha" k2="-1" k3="1" operator="arithmetic"/><feColorMatrix values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/><feBlend in2="shape" result="effect2_innerShadow_847_1831"/></filter><radialGradient id="a" cx="0" cy="0" r="1" gradientTransform="matrix(0 -25.15 33.555 0 37.91706 38.00588)" gradientUnits="userSpaceOnUse"><stop stop-color="#3245FF"/><stop offset=".28646" stop-color="#BC52EE"/><stop offset=".41434" stop-color="#4AF2C8"/><stop offset=".55466" stop-color="#4AF2C8"/><stop offset="1" stop-color="#3245FF"/></radialGradient><radialGradient id="b" cx="0" cy="0" r="1" gradientTransform="matrix(0 36.673 -50.354 0 37.91691 32.37368)" gradientUnits="userSpaceOnUse"><stop offset=".36659" stop-color="#191C24"/><stop offset=".66145" stop-color="#111218"/><stop offset="1" stop-color="#040506"/></radialGradient><radialGradient id="l" cx="0" cy="0" r="1" gradientTransform="matrix(6.044 5.658 -9.328 9.963 130.134 106.85)" gradientUnits="userSpaceOnUse"><stop stop-color="#D5D7DB"/><stop offset=".17494" stop-color="#BABFCF"/><stop offset=".32731" stop-color="#A7AAB6"/><stop offset=".45345" stop-color="#9CA0AE"/><stop offset=".60938" stop-color="#7F818A"/><stop offset=".79688" stop-color="#636673"/><stop offset="1" stop-color="#47474A"/></radialGradient><radialGradient id="n" cx="0" cy="0" r="1" gradientTransform="matrix(24.63 48.03 -64.437 33.045 129.187 70.2917)" gradientUnits="userSpaceOnUse"><stop stop-color="#D5D7DB"/><stop offset=".17494" stop-color="#BABFCF"/><stop offset=".32731" stop-color="#A7AAB6"/><stop offset=".45345" stop-color="#9CA0AE"/><stop offset=".60938" stop-color="#7F818A"/><stop offset=".79688" stop-color="#636673"/><stop offset="1" stop-color="#47474A"/></radialGradient><linearGradient id="o" x1="154.075" x2="109.822" y1="69.6993" y2="88.276" gradientTransform="matrix(.5 0 0 .5 11.87299 -58.04057)" gradientUnits="userSpaceOnUse"><stop stop-color="#4AF2C8"/><stop offset="1" stop-color="#2F4CB3"/></linearGradient></defs></svg>'
};
export {
  getIconElement,
  isDefinedIcon
};
