import type { AstroConfig } from '../@types/astro.js';
import type { <PERSON>duleLoader } from '../core/module-loader/index.js';
import type { DevPipeline } from './pipeline.js';
export declare function recordServerError(loader: <PERSON><PERSON><PERSON><PERSON>oa<PERSON>, config: AstroConfig, { logger }: DevPipeline, _err: unknown): {
    error: Error;
    errorWithMetadata: import("../core/errors/errors.js").ErrorWithMetadata;
};
