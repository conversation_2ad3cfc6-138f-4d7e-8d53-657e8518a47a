{"name": "astro", "version": "4.16.18", "description": "Astro is a modern site builder with web best practices, performance, and DX front-of-mind.", "type": "module", "author": "<PERSON><PERSON><PERSON>", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/withastro/astro.git", "directory": "packages/astro"}, "bugs": "https://github.com/withastro/astro/issues", "homepage": "https://astro.build", "types": "./index.d.ts", "typesVersions": {"*": {"app": ["./dist/core/app/index"], "app/*": ["./dist/core/app/*"], "middleware": ["./dist/virtual-modules/middleware.d.ts"]}}, "exports": {".": {"types": "./index.d.ts", "default": "./dist/core/index.js"}, "./env": "./env.d.ts", "./env/runtime": "./dist/env/runtime.js", "./env/setup": "./dist/env/setup.js", "./types": "./types.d.ts", "./client": "./client.d.ts", "./astro-jsx": "./astro-jsx.d.ts", "./tsconfigs/*.json": "./tsconfigs/*", "./tsconfigs/*": "./tsconfigs/*.json", "./jsx/*": "./dist/jsx/*", "./jsx-runtime": {"types": "./jsx-runtime.d.ts", "default": "./dist/jsx-runtime/index.js"}, "./compiler-runtime": "./dist/runtime/compiler/index.js", "./runtime/*": "./dist/runtime/*", "./config": {"types": "./config.d.ts", "default": "./config.mjs"}, "./container": {"types": "./dist/container/index.d.ts", "default": "./dist/container/index.js"}, "./app": "./dist/core/app/index.js", "./app/node": "./dist/core/app/node.js", "./client/*": "./dist/runtime/client/*", "./components": "./components/index.ts", "./components/*": "./components/*", "./toolbar": "./dist/toolbar/index.js", "./actions/runtime/*": "./dist/actions/runtime/*", "./assets": "./dist/assets/index.js", "./assets/utils": "./dist/assets/utils/index.js", "./assets/utils/inferRemoteSize.js": "./dist/assets/utils/remoteProbe.js", "./assets/endpoint/*": "./dist/assets/endpoint/*.js", "./assets/services/sharp": "./dist/assets/services/sharp.js", "./assets/services/squoosh": "./dist/assets/services/squoosh.js", "./assets/services/noop": "./dist/assets/services/noop.js", "./loaders": "./dist/content/loaders/index.js", "./content/runtime": "./dist/content/runtime.js", "./content/runtime-assets": "./dist/content/runtime-assets.js", "./debug": "./components/Debug.astro", "./package.json": "./package.json", "./zod": {"types": "./zod.d.ts", "default": "./zod.mjs"}, "./errors": "./dist/core/errors/userError.js", "./middleware": {"types": "./dist/core/middleware/index.d.ts", "default": "./dist/core/middleware/index.js"}, "./virtual-modules/*": "./dist/virtual-modules/*"}, "bin": {"astro": "astro.js"}, "files": ["components", "tsconfigs", "dist", "types", "astro.js", "index.d.ts", "config.d.ts", "config.mjs", "zod.d.ts", "zod.mjs", "env.d.ts", "client.d.ts", "jsx-runtime.d.ts", "templates", "astro-jsx.d.ts", "types.d.ts", "README.md", "vendor"], "dependencies": {"@astrojs/compiler": "^2.10.3", "@babel/core": "^7.26.0", "@babel/plugin-transform-react-jsx": "^7.25.9", "@babel/types": "^7.26.0", "@oslojs/encoding": "^1.1.0", "@rollup/pluginutils": "^5.1.3", "@types/babel__core": "^7.20.5", "@types/cookie": "^0.6.0", "acorn": "^8.14.0", "aria-query": "^5.3.2", "axobject-query": "^4.1.0", "boxen": "8.0.1", "ci-info": "^4.1.0", "clsx": "^2.1.1", "common-ancestor-path": "^1.0.1", "cookie": "^0.7.2", "cssesc": "^3.0.0", "debug": "^4.3.7", "deterministic-object-hash": "^2.0.2", "devalue": "^5.1.1", "diff": "^5.2.0", "dlv": "^1.1.3", "dset": "^3.1.4", "es-module-lexer": "^1.5.4", "esbuild": "^0.21.5", "estree-walker": "^3.0.3", "fast-glob": "^3.3.2", "flattie": "^1.1.1", "github-slugger": "^2.0.0", "gray-matter": "^4.0.3", "html-escaper": "^3.0.3", "http-cache-semantics": "^4.1.1", "js-yaml": "^4.1.0", "kleur": "^4.1.5", "magic-string": "^0.30.14", "magicast": "^0.3.5", "micromatch": "^4.0.8", "mrmime": "^2.0.0", "neotraverse": "^0.6.18", "ora": "^8.1.1", "p-limit": "^6.1.0", "p-queue": "^8.0.1", "preferred-pm": "^4.0.0", "prompts": "^2.4.2", "rehype": "^13.0.2", "semver": "^7.6.3", "shiki": "^1.23.1", "tinyexec": "^0.3.1", "tsconfck": "^3.1.4", "unist-util-visit": "^5.0.0", "vfile": "^6.0.3", "vite": "^5.4.11", "vitefu": "^1.0.4", "which-pm": "^3.0.0", "xxhash-wasm": "^1.1.0", "yargs-parser": "^21.1.1", "zod": "^3.23.8", "zod-to-json-schema": "^3.23.5", "zod-to-ts": "^1.2.0", "@astrojs/internal-helpers": "0.4.1", "@astrojs/markdown-remark": "5.3.0", "@astrojs/telemetry": "3.1.0"}, "optionalDependencies": {"sharp": "^0.33.3"}, "devDependencies": {"@astrojs/check": "^0.9.4", "@playwright/test": "^1.49.0", "@types/aria-query": "^5.0.4", "@types/common-ancestor-path": "^1.0.2", "@types/cssesc": "^3.0.2", "@types/debug": "^4.1.12", "@types/diff": "^5.2.3", "@types/dlv": "^1.1.5", "@types/hast": "^3.0.4", "@types/html-escaper": "^3.0.2", "@types/http-cache-semantics": "^4.0.4", "@types/js-yaml": "^4.0.9", "@types/micromatch": "^4.0.9", "@types/prompts": "^2.4.9", "@types/semver": "^7.5.8", "@types/yargs-parser": "^21.0.3", "cheerio": "1.0.0", "eol": "^0.10.0", "execa": "^8.0.1", "expect-type": "^1.1.0", "fs-fixture": "^2.6.0", "mdast-util-mdx": "^3.0.0", "mdast-util-mdx-jsx": "^3.1.3", "node-mocks-http": "^1.16.1", "parse-srcset": "^1.0.2", "rehype-autolink-headings": "^7.1.0", "rehype-slug": "^6.0.0", "rehype-toc": "^3.0.2", "remark-code-titles": "^0.1.2", "rollup": "^4.27.4", "sass": "^1.81.0", "undici": "^6.21.0", "unified": "^11.0.5", "astro-scripts": "0.0.14"}, "engines": {"node": "^18.17.1 || ^20.3.0 || >=21.0.0", "npm": ">=9.6.5", "pnpm": ">=7.1.0"}, "publishConfig": {"provenance": true}, "scripts": {"prebuild": "astro-scripts prebuild --to-string \"src/runtime/server/astro-island.ts\" \"src/runtime/client/{idle,load,media,only,visible}.ts\"", "build": "pnpm run prebuild && astro-scripts build \"src/**/*.{ts,js}\" --copy-wasm && tsc", "build:ci": "pnpm run prebuild && astro-scripts build \"src/**/*.{ts,js}\" --copy-wasm", "dev": "astro-scripts dev --copy-wasm --prebuild \"src/runtime/server/astro-island.ts\" --prebuild \"src/runtime/client/{idle,load,media,only,visible}.ts\" \"src/**/*.{ts,js}\"", "test": "pnpm run test:unit && pnpm run test:integration && pnpm run test:types", "test:match": "astro-scripts test \"test/**/*.test.js\" --match", "test:e2e": "pnpm test:e2e:chrome && pnpm test:e2e:firefox", "test:e2e:match": "playwright test -g", "test:e2e:chrome": "playwright test", "test:e2e:firefox": "playwright test --config playwright.firefox.config.js", "test:types": "tsc --project tsconfig.tests.json", "test:unit": "astro-scripts test \"test/units/**/*.test.js\" --teardown ./test/units/teardown.js", "test:integration": "astro-scripts test \"test/*.test.js\""}}