/*!
 * Sitemap
 * Copyright(c) 2011 <PERSON>
 * MIT Licensed
 */
/**
 * URL in SitemapItem does not exist
 */
export declare class NoURLError extends Error {
    constructor(message?: string);
}
/**
 * Config was not passed to SitemapItem constructor
 */
export declare class NoConfigError extends Error {
    constructor(message?: string);
}
/**
 * changefreq property in sitemap is invalid
 */
export declare class ChangeFreqInvalidError extends Error {
    constructor(url: string, changefreq: any);
}
/**
 * priority property in sitemap is invalid
 */
export declare class PriorityInvalidError extends Error {
    constructor(url: string, priority: any);
}
/**
 * SitemapIndex target Folder does not exists
 */
export declare class UndefinedTargetFolder extends Error {
    constructor(message?: string);
}
export declare class InvalidVideoFormat extends Error {
    constructor(url: string);
}
export declare class InvalidVideoDuration extends Error {
    constructor(url: string, duration: any);
}
export declare class InvalidVideoDescription extends Error {
    constructor(url: string, length: number);
}
export declare class InvalidVideoRating extends Error {
    constructor(url: string, title: any, rating: any);
}
export declare class InvalidAttrValue extends Error {
    constructor(key: string, val: any, validator: RegExp);
}
export declare class InvalidAttr extends Error {
    constructor(key: string);
}
export declare class InvalidNewsFormat extends Error {
    constructor(url: string);
}
export declare class InvalidNewsAccessValue extends Error {
    constructor(url: string, access: any);
}
export declare class XMLLintUnavailable extends Error {
    constructor(message?: string);
}
export declare class InvalidVideoTitle extends Error {
    constructor(url: string, length: number);
}
export declare class InvalidVideoViewCount extends Error {
    constructor(url: string, count: number);
}
export declare class InvalidVideoTagCount extends Error {
    constructor(url: string, count: number);
}
export declare class InvalidVideoCategory extends Error {
    constructor(url: string, count: number);
}
export declare class InvalidVideoFamilyFriendly extends Error {
    constructor(url: string, fam: string);
}
export declare class InvalidVideoRestriction extends Error {
    constructor(url: string, code: string);
}
export declare class InvalidVideoRestrictionRelationship extends Error {
    constructor(url: string, val?: string);
}
export declare class InvalidVideoPriceType extends Error {
    constructor(url: string, priceType?: string, price?: string);
}
export declare class InvalidVideoResolution extends Error {
    constructor(url: string, resolution: string);
}
export declare class InvalidVideoPriceCurrency extends Error {
    constructor(url: string, currency: string);
}
export declare class EmptyStream extends Error {
    constructor();
}
export declare class EmptySitemap extends Error {
    constructor();
}
