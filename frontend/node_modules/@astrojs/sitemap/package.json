{"name": "@astrojs/sitemap", "description": "Generate a sitemap for your Astro site", "version": "3.4.1", "type": "module", "types": "./dist/index.d.ts", "author": "<PERSON><PERSON><PERSON>", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/withastro/astro.git", "directory": "packages/integrations/sitemap"}, "keywords": ["astro-integration", "astro-component", "seo", "sitemap"], "bugs": "https://github.com/withastro/astro/issues", "homepage": "https://docs.astro.build/en/guides/integrations-guide/sitemap/", "exports": {".": "./dist/index.js", "./package.json": "./package.json"}, "files": ["dist"], "dependencies": {"sitemap": "^8.0.0", "stream-replace-string": "^2.0.0", "zod": "^3.24.2"}, "devDependencies": {"xml2js": "0.6.2", "astro": "5.8.2", "astro-scripts": "0.0.14"}, "publishConfig": {"provenance": true}, "scripts": {"build": "astro-scripts build \"src/**/*.ts\" && tsc", "build:ci": "astro-scripts build \"src/**/*.ts\"", "dev": "astro-scripts dev \"src/**/*.ts\"", "test": "astro-scripts test \"test/**/*.test.js\""}}