{"name": "@astrojs/react", "description": "Use React components within Astro", "version": "4.3.0", "type": "module", "types": "./dist/index.d.ts", "author": "<PERSON><PERSON><PERSON>", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/withastro/astro.git", "directory": "packages/integrations/react"}, "keywords": ["astro-integration", "astro-component", "renderer", "react"], "bugs": "https://github.com/withastro/astro/issues", "homepage": "https://docs.astro.build/en/guides/integrations-guide/react/", "exports": {".": "./dist/index.js", "./actions": "./dist/actions.js", "./client.js": "./dist/client.js", "./client-v17.js": "./dist/client-v17.js", "./server.js": "./dist/server.js", "./server-v17.js": "./dist/server-v17.js", "./package.json": "./package.json", "./jsx-runtime": "./dist/jsx-runtime.js"}, "files": ["dist"], "dependencies": {"@vitejs/plugin-react": "^4.4.1", "ultrahtml": "^1.6.0", "vite": "^6.3.5"}, "devDependencies": {"@types/react": "^18.3.22", "@types/react-dom": "^18.3.7", "cheerio": "1.0.0", "react": "^18.3.1", "react-dom": "^18.3.1", "astro": "5.8.0", "astro-scripts": "0.0.14"}, "peerDependencies": {"@types/react": "^17.0.50 || ^18.0.21 || ^19.0.0", "@types/react-dom": "^17.0.17 || ^18.0.6 || ^19.0.0", "react": "^17.0.2 || ^18.0.0 || ^19.0.0", "react-dom": "^17.0.2 || ^18.0.0 || ^19.0.0"}, "engines": {"node": "18.20.8 || ^20.3.0 || >=22.0.0"}, "publishConfig": {"provenance": true}, "scripts": {"build": "astro-scripts build \"src/**/*.ts\" && tsc", "build:ci": "astro-scripts build \"src/**/*.ts\"", "dev": "astro-scripts dev \"src/**/*.ts\"", "test": "astro-scripts test \"test/**/*.test.js\""}}