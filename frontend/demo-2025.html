<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MaxCoupon - Premium Deals & Exclusive Coupons 2025</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700;800&display=swap" rel="stylesheet">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#fdf4ff',
                            100: '#fae8ff',
                            200: '#f5d0fe',
                            300: '#f0abfc',
                            400: '#e879f9',
                            500: '#d946ef',
                            600: '#c026d3',
                            700: '#a21caf',
                            800: '#86198f',
                            900: '#701a75',
                            950: '#4a044e',
                        },
                        secondary: {
                            50: '#fff7ed',
                            100: '#ffedd5',
                            200: '#fed7aa',
                            300: '#fdba74',
                            400: '#fb923c',
                            500: '#f97316',
                            600: '#ea580c',
                            700: '#c2410c',
                            800: '#9a3412',
                            900: '#7c2d12',
                            950: '#431407',
                        },
                        accent: {
                            50: '#f0f9ff',
                            100: '#e0f2fe',
                            200: '#bae6fd',
                            300: '#7dd3fc',
                            400: '#38bdf8',
                            500: '#0ea5e9',
                            600: '#0284c7',
                            700: '#0369a1',
                            800: '#075985',
                            900: '#0c4a6e',
                            950: '#082f49',
                        }
                    },
                    fontFamily: {
                        'display': ['Poppins', 'system-ui', 'sans-serif'],
                        'sans': ['Inter', 'system-ui', 'sans-serif'],
                    },
                    animation: {
                        'gradient': 'gradient 8s ease infinite',
                        'float': 'float 6s ease-in-out infinite',
                        'glow': 'glow 2s ease-in-out infinite alternate',
                    },
                    keyframes: {
                        gradient: {
                            '0%, 100%': { 'background-position': '0% 50%' },
                            '50%': { 'background-position': '100% 50%' },
                        },
                        float: {
                            '0%, 100%': { transform: 'translateY(0px)' },
                            '50%': { transform: 'translateY(-20px)' },
                        },
                        glow: {
                            '0%': { 'box-shadow': '0 0 20px rgba(217, 70, 239, 0.5)' },
                            '100%': { 'box-shadow': '0 0 40px rgba(217, 70, 239, 0.8)' },
                        }
                    }
                }
            }
        }
    </script>
    <style>
        .gradient-bg {
            background: linear-gradient(-45deg, #d946ef, #f97316, #0ea5e9, #c026d3);
            background-size: 400% 400%;
            animation: gradient 8s ease infinite;
        }
        .glass-effect {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .card-premium {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
        }
        .text-gradient {
            background: linear-gradient(135deg, #d946ef, #f97316);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        .btn-premium {
            background: linear-gradient(135deg, #d946ef, #c026d3);
            box-shadow: 0 10px 25px rgba(217, 70, 239, 0.3);
            transition: all 0.3s ease;
        }
        .btn-premium:hover {
            transform: translateY(-2px);
            box-shadow: 0 15px 35px rgba(217, 70, 239, 0.4);
        }
        .floating-element {
            animation: float 6s ease-in-out infinite;
        }
    </style>
</head>
<body class="min-h-screen bg-gradient-to-br from-neutral-900 via-neutral-800 to-neutral-900 text-white font-sans overflow-x-hidden">
    <!-- Background Elements -->
    <div class="fixed inset-0 overflow-hidden pointer-events-none">
        <div class="absolute top-1/4 left-1/4 w-96 h-96 bg-primary-500/20 rounded-full blur-3xl floating-element"></div>
        <div class="absolute top-3/4 right-1/4 w-80 h-80 bg-secondary-500/20 rounded-full blur-3xl floating-element" style="animation-delay: -2s;"></div>
        <div class="absolute top-1/2 left-1/2 w-64 h-64 bg-accent-500/20 rounded-full blur-3xl floating-element" style="animation-delay: -4s;"></div>
    </div>

    <!-- Header -->
    <header class="relative z-50 glass-effect border-b border-white/10">
        <!-- Top Bar -->
        <div class="bg-gradient-to-r from-primary-600 to-secondary-600 py-2">
            <div class="container mx-auto px-4">
                <div class="flex items-center justify-between text-sm">
                    <div class="flex items-center space-x-4">
                        <span class="animate-pulse">✨ Exclusive 2025 Premium Deals - Save up to 80%!</span>
                    </div>
                    <div class="flex items-center space-x-4">
                        <a href="#" class="hover:text-primary-200 transition-colors">Premium Support</a>
                        <span class="text-white/50">|</span>
                        <a href="#" class="hover:text-primary-200 transition-colors">VIP Access</a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Header -->
        <div class="container mx-auto px-4 py-6">
            <div class="flex items-center justify-between">
                <!-- Logo -->
                <div class="flex items-center">
                    <a href="#" class="flex items-center space-x-3 group">
                        <div class="relative">
                            <div class="w-12 h-12 bg-gradient-to-br from-primary-500 to-secondary-500 rounded-2xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300 animate-glow">
                                <svg class="w-7 h-7 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                            </div>
                            <div class="absolute -top-1 -right-1 w-4 h-4 bg-secondary-500 rounded-full animate-pulse"></div>
                        </div>
                        <div>
                            <h1 class="text-3xl font-display font-bold text-gradient">MaxCoupon</h1>
                            <p class="text-xs text-neutral-400 -mt-1">Premium Deals Platform</p>
                        </div>
                    </a>
                </div>

                <!-- Search Bar -->
                <div class="flex-1 max-w-2xl mx-8">
                    <div class="relative">
                        <input type="text" placeholder="Search premium deals, exclusive coupons..." 
                               class="w-full pl-14 pr-24 py-4 bg-white/10 border border-white/20 rounded-2xl text-white placeholder-neutral-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent backdrop-blur-sm transition-all duration-300">
                        <div class="absolute inset-y-0 left-0 pl-5 flex items-center pointer-events-none">
                            <svg class="h-6 w-6 text-neutral-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                            </svg>
                        </div>
                        <button class="absolute inset-y-0 right-0 pr-3 flex items-center">
                            <span class="btn-premium text-white px-6 py-3 rounded-xl font-medium">Search</span>
                        </button>
                    </div>
                </div>

                <!-- Navigation -->
                <nav class="flex items-center space-x-8">
                    <a href="#" class="text-neutral-300 hover:text-primary-400 font-medium transition-colors duration-300">Premium Coupons</a>
                    <a href="#" class="text-neutral-300 hover:text-secondary-400 font-medium transition-colors duration-300">Exclusive Deals</a>
                    <a href="#" class="text-neutral-300 hover:text-accent-400 font-medium transition-colors duration-300">Top Brands</a>
                    <a href="#" class="text-neutral-300 hover:text-primary-400 font-medium transition-colors duration-300">Categories</a>
                </nav>
            </div>
        </div>
    </header>

    <!-- Hero Section -->
    <section class="relative py-32 overflow-hidden">
        <div class="container mx-auto px-4 text-center relative z-10">
            <div class="max-w-5xl mx-auto">
                <h1 class="text-7xl md:text-8xl font-display font-bold mb-8 leading-tight">
                    Discover
                    <span class="block text-gradient animate-pulse">Premium Deals</span>
                    <span class="block text-4xl md:text-5xl text-neutral-400 font-normal mt-4">
                        That Actually Matter
                    </span>
                </h1>
                
                <p class="text-2xl text-neutral-300 mb-12 max-w-3xl mx-auto leading-relaxed">
                    Curated exclusive offers from the world's most prestigious brands. 
                    <span class="text-primary-400">Save more, spend smarter</span> with our AI-powered deal discovery.
                </p>

                <!-- Stats -->
                <div class="grid grid-cols-4 gap-8 mb-16 max-w-3xl mx-auto">
                    <div class="text-center">
                        <div class="text-4xl font-bold text-primary-400 mb-2">50K+</div>
                        <div class="text-neutral-400 text-sm">Premium Coupons</div>
                    </div>
                    <div class="text-center">
                        <div class="text-4xl font-bold text-secondary-400 mb-2">1K+</div>
                        <div class="text-neutral-400 text-sm">Elite Brands</div>
                    </div>
                    <div class="text-center">
                        <div class="text-4xl font-bold text-accent-400 mb-2">5M+</div>
                        <div class="text-neutral-400 text-sm">Smart Savers</div>
                    </div>
                    <div class="text-center">
                        <div class="text-4xl font-bold text-primary-400 mb-2">$500M+</div>
                        <div class="text-neutral-400 text-sm">Total Saved</div>
                    </div>
                </div>

                <!-- CTA Buttons -->
                <div class="flex items-center justify-center gap-6">
                    <button class="btn-premium text-white px-10 py-5 rounded-2xl font-semibold text-lg flex items-center space-x-3">
                        <span>Explore Premium Deals</span>
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6"></path>
                        </svg>
                    </button>
                    <button class="glass-effect text-white px-10 py-5 rounded-2xl font-semibold text-lg border border-white/20 hover:border-primary-400/50 transition-all duration-300">
                        Watch Demo
                    </button>
                </div>
            </div>
        </div>
    </section>

    <!-- Featured Coupons -->
    <section class="py-24 relative">
        <div class="container mx-auto px-4">
            <div class="text-center mb-16">
                <h2 class="text-5xl font-display font-bold text-white mb-6">
                    🔥 <span class="text-gradient">Exclusive Premium Coupons</span>
                </h2>
                <p class="text-xl text-neutral-400 max-w-2xl mx-auto">
                    Hand-curated deals from luxury brands and premium retailers
                </p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                <!-- Premium Coupon Card 1 -->
                <div class="card-premium rounded-3xl p-8 group hover:scale-105 transition-all duration-500">
                    <div class="flex items-center justify-between mb-6">
                        <div class="flex items-center space-x-4">
                            <div class="w-14 h-14 bg-gradient-to-br from-primary-500 to-primary-600 rounded-2xl flex items-center justify-center">
                                <span class="text-2xl font-bold text-white">A</span>
                            </div>
                            <div>
                                <h3 class="font-semibold text-white text-lg">Amazon</h3>
                                <p class="text-sm text-neutral-400">Premium Electronics</p>
                            </div>
                        </div>
                        <span class="bg-gradient-to-r from-secondary-500 to-secondary-600 text-white px-3 py-1 rounded-full text-xs font-semibold">
                            VIP Exclusive
                        </span>
                    </div>
                    
                    <h4 class="text-xl font-semibold text-white mb-3 group-hover:text-primary-400 transition-colors">
                        Premium Tech Sale - 35% Off
                    </h4>
                    <p class="text-neutral-400 text-sm mb-6">
                        Exclusive discount on premium electronics and smart devices
                    </p>
                    
                    <div class="bg-gradient-to-r from-primary-500 to-secondary-500 text-white px-6 py-4 rounded-2xl mb-6 text-center">
                        <div class="text-xs uppercase tracking-wide mb-1">Save Up To</div>
                        <div class="font-bold text-3xl">35%</div>
                    </div>
                    
                    <div class="glass-effect border border-white/20 rounded-2xl p-4 mb-6">
                        <div class="text-xs text-neutral-400 uppercase tracking-wide mb-1">Premium Code</div>
                        <div class="font-mono font-bold text-lg text-white">TECH35VIP</div>
                    </div>
                    
                    <button class="w-full btn-premium text-white py-4 rounded-2xl font-semibold group-hover:scale-105 transition-transform duration-300">
                        Claim Exclusive Deal
                    </button>
                </div>

                <!-- Premium Coupon Card 2 -->
                <div class="card-premium rounded-3xl p-8 group hover:scale-105 transition-all duration-500">
                    <div class="flex items-center justify-between mb-6">
                        <div class="flex items-center space-x-4">
                            <div class="w-14 h-14 bg-gradient-to-br from-accent-500 to-accent-600 rounded-2xl flex items-center justify-center">
                                <span class="text-2xl font-bold text-white">N</span>
                            </div>
                            <div>
                                <h3 class="font-semibold text-white text-lg">Nike</h3>
                                <p class="text-sm text-neutral-400">Athletic Premium</p>
                            </div>
                        </div>
                        <span class="bg-gradient-to-r from-primary-500 to-primary-600 text-white px-3 py-1 rounded-full text-xs font-semibold">
                            Limited Time
                        </span>
                    </div>
                    
                    <h4 class="text-xl font-semibold text-white mb-3 group-hover:text-accent-400 transition-colors">
                        Elite Athletic Collection - 40% Off
                    </h4>
                    <p class="text-neutral-400 text-sm mb-6">
                        Premium athletic wear and limited edition sneakers
                    </p>
                    
                    <div class="bg-gradient-to-r from-accent-500 to-primary-500 text-white px-6 py-4 rounded-2xl mb-6 text-center">
                        <div class="text-xs uppercase tracking-wide mb-1">Save Up To</div>
                        <div class="font-bold text-3xl">40%</div>
                    </div>
                    
                    <div class="glass-effect border border-white/20 rounded-2xl p-4 mb-6">
                        <div class="text-xs text-neutral-400 uppercase tracking-wide mb-1">Elite Code</div>
                        <div class="font-mono font-bold text-lg text-white">ELITE40</div>
                    </div>
                    
                    <button class="w-full btn-premium text-white py-4 rounded-2xl font-semibold group-hover:scale-105 transition-transform duration-300">
                        Unlock Premium Deal
                    </button>
                </div>

                <!-- Premium Coupon Card 3 -->
                <div class="card-premium rounded-3xl p-8 group hover:scale-105 transition-all duration-500">
                    <div class="flex items-center justify-between mb-6">
                        <div class="flex items-center space-x-4">
                            <div class="w-14 h-14 bg-gradient-to-br from-secondary-500 to-secondary-600 rounded-2xl flex items-center justify-center">
                                <span class="text-2xl font-bold text-white">A</span>
                            </div>
                            <div>
                                <h3 class="font-semibold text-white text-lg">Apple</h3>
                                <p class="text-sm text-neutral-400">Innovation Hub</p>
                            </div>
                        </div>
                        <span class="bg-gradient-to-r from-accent-500 to-accent-600 text-white px-3 py-1 rounded-full text-xs font-semibold">
                            Platinum
                        </span>
                    </div>
                    
                    <h4 class="text-xl font-semibold text-white mb-3 group-hover:text-secondary-400 transition-colors">
                        Apple Premium Bundle - $200 Off
                    </h4>
                    <p class="text-neutral-400 text-sm mb-6">
                        Latest iPhone, iPad, and MacBook exclusive bundles
                    </p>
                    
                    <div class="bg-gradient-to-r from-secondary-500 to-accent-500 text-white px-6 py-4 rounded-2xl mb-6 text-center">
                        <div class="text-xs uppercase tracking-wide mb-1">Save Up To</div>
                        <div class="font-bold text-3xl">$200</div>
                    </div>
                    
                    <div class="glass-effect border border-white/20 rounded-2xl p-4 mb-6">
                        <div class="text-xs text-neutral-400 uppercase tracking-wide mb-1">Platinum Code</div>
                        <div class="font-mono font-bold text-lg text-white">APPLE200</div>
                    </div>
                    
                    <button class="w-full btn-premium text-white py-4 rounded-2xl font-semibold group-hover:scale-105 transition-transform duration-300">
                        Access Premium Bundle
                    </button>
                </div>
            </div>

            <div class="text-center mt-16">
                <button class="glass-effect text-white px-12 py-5 rounded-2xl font-semibold text-lg border border-white/20 hover:border-primary-400/50 transition-all duration-300 flex items-center space-x-3 mx-auto">
                    <span>Explore All Premium Deals</span>
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                    </svg>
                </button>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="relative py-20 mt-32">
        <div class="absolute inset-0 bg-gradient-to-t from-neutral-900 to-transparent"></div>
        <div class="container mx-auto px-4 relative z-10">
            <div class="text-center">
                <div class="flex items-center justify-center space-x-3 mb-8">
                    <div class="w-12 h-12 bg-gradient-to-br from-primary-500 to-secondary-500 rounded-2xl flex items-center justify-center">
                        <svg class="w-7 h-7 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <div>
                        <h3 class="text-2xl font-display font-bold text-gradient">MaxCoupon</h3>
                        <p class="text-neutral-400 text-sm">Premium Deals Platform</p>
                    </div>
                </div>
                
                <p class="text-neutral-400 mb-8 max-w-2xl mx-auto">
                    Revolutionizing how you discover and save with premium deals from the world's most exclusive brands.
                </p>
                
                <div class="flex items-center justify-center space-x-8 mb-8">
                    <a href="#" class="text-neutral-400 hover:text-primary-400 transition-colors">Premium Support</a>
                    <a href="#" class="text-neutral-400 hover:text-secondary-400 transition-colors">Elite Brands</a>
                    <a href="#" class="text-neutral-400 hover:text-accent-400 transition-colors">VIP Access</a>
                    <a href="#" class="text-neutral-400 hover:text-primary-400 transition-colors">Privacy</a>
                </div>
                
                <div class="text-neutral-500 text-sm">
                    © 2025 MaxCoupon. Redefining premium savings.
                </div>
            </div>
        </div>
    </footer>

    <script>
        // Add some interactive effects
        document.addEventListener('DOMContentLoaded', function() {
            // Smooth scrolling for anchor links
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function (e) {
                    e.preventDefault();
                    document.querySelector(this.getAttribute('href')).scrollIntoView({
                        behavior: 'smooth'
                    });
                });
            });

            // Add parallax effect to background elements
            window.addEventListener('scroll', function() {
                const scrolled = window.pageYOffset;
                const parallax = document.querySelectorAll('.floating-element');
                const speed = 0.5;

                parallax.forEach(element => {
                    const yPos = -(scrolled * speed);
                    element.style.transform = `translateY(${yPos}px)`;
                });
            });

            // Add hover effects to cards
            const cards = document.querySelectorAll('.card-premium');
            cards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-10px) scale(1.02)';
                });
                
                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0) scale(1)';
                });
            });
        });
    </script>
</body>
</html>
