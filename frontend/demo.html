<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MaxCoupon - Best Deals & Coupons</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700;800&display=swap" rel="stylesheet">
    <style>
        .font-display { font-family: 'Poppins', system-ui, sans-serif; }
        .text-gradient { background: linear-gradient(to right, #3B82F6, #22C55E); -webkit-background-clip: text; -webkit-text-fill-color: transparent; }
        .card { background: white; border-radius: 1rem; box-shadow: 0 2px 15px -3px rgba(0, 0, 0, 0.07), 0 10px 20px -2px rgba(0, 0, 0, 0.04); border: 1px solid #f3f4f6; overflow: hidden; }
        .card-hover:hover { box-shadow: 0 4px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04); transform: translateY(-2px); }
        .btn-primary { background: #3B82F6; color: white; font-weight: 500; padding: 0.5rem 1rem; border-radius: 0.5rem; transition: all 0.2s; }
        .btn-primary:hover { background: #2563EB; }
        .badge { display: inline-flex; align-items: center; padding: 0.25rem 0.625rem; border-radius: 9999px; font-size: 0.75rem; font-weight: 500; }
        .badge-primary { background: #dbeafe; color: #1e40af; }
        .badge-accent { background: #fee2e2; color: #991b1b; }
    </style>
</head>
<body class="min-h-screen bg-gray-50 text-gray-900 font-sans">
    <!-- Header -->
    <header class="sticky top-0 z-50 bg-white border-b border-gray-200 shadow-sm">
        <!-- Top Bar -->
        <div class="bg-blue-600 text-white py-2">
            <div class="container mx-auto px-4">
                <div class="flex items-center justify-between text-sm">
                    <div class="flex items-center space-x-4">
                        <span>🎉 Save up to 70% with our exclusive deals!</span>
                    </div>
                    <div class="flex items-center space-x-4">
                        <a href="#" class="hover:text-blue-200 transition-colors">Help</a>
                        <span class="text-blue-300">|</span>
                        <a href="#" class="hover:text-blue-200 transition-colors">Contact</a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Header -->
        <div class="container mx-auto px-4 py-4">
            <div class="flex items-center justify-between">
                <!-- Logo -->
                <div class="flex items-center">
                    <a href="#" class="flex items-center space-x-2 group">
                        <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-green-500 rounded-xl flex items-center justify-center group-hover:scale-105 transition-transform duration-200">
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                        <div>
                            <h1 class="text-2xl font-display font-bold text-gradient">MaxCoupon</h1>
                            <p class="text-xs text-gray-500 -mt-1">Best Deals & Coupons</p>
                        </div>
                    </a>
                </div>

                <!-- Search Bar -->
                <div class="flex-1 max-w-2xl mx-8">
                    <div class="relative">
                        <input type="text" placeholder="Search for coupons, deals, brands..." class="w-full pl-12 pr-20 py-3 border border-gray-300 rounded-xl bg-white text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        <div class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                            <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                            </svg>
                        </div>
                        <button class="absolute inset-y-0 right-0 pr-3 flex items-center">
                            <span class="btn-primary">Search</span>
                        </button>
                    </div>
                </div>

                <!-- Navigation -->
                <nav class="flex items-center space-x-8">
                    <a href="#" class="text-gray-700 hover:text-blue-600 font-medium">Coupons</a>
                    <a href="#" class="text-gray-700 hover:text-blue-600 font-medium">Deals</a>
                    <a href="#" class="text-gray-700 hover:text-blue-600 font-medium">Brands</a>
                    <a href="#" class="text-gray-700 hover:text-blue-600 font-medium">Categories</a>
                </nav>
            </div>
        </div>
    </header>

    <!-- Hero Section -->
    <section class="bg-gradient-to-br from-blue-600 via-blue-700 to-green-600 text-white py-20">
        <div class="container mx-auto px-4 text-center">
            <h1 class="text-5xl font-display font-bold mb-6">
                Discover Amazing
                <span class="text-transparent bg-clip-text bg-gradient-to-r from-yellow-300 to-orange-300">
                    Deals & Coupons
                </span>
            </h1>
            <p class="text-xl text-blue-100 mb-8 max-w-3xl mx-auto">
                Save money on your favorite brands across Europe and America. 
                Get verified coupons, exclusive deals, and instant discounts.
            </p>

            <!-- Stats -->
            <div class="grid grid-cols-4 gap-8 mb-12 max-w-2xl mx-auto">
                <div class="text-center">
                    <div class="text-3xl font-bold mb-2">10K+</div>
                    <div class="text-blue-200 text-sm">Active Coupons</div>
                </div>
                <div class="text-center">
                    <div class="text-3xl font-bold mb-2">500+</div>
                    <div class="text-blue-200 text-sm">Top Brands</div>
                </div>
                <div class="text-center">
                    <div class="text-3xl font-bold mb-2">2M+</div>
                    <div class="text-blue-200 text-sm">Happy Users</div>
                </div>
                <div class="text-center">
                    <div class="text-3xl font-bold mb-2">$50M+</div>
                    <div class="text-blue-200 text-sm">Money Saved</div>
                </div>
            </div>

            <!-- CTA Buttons -->
            <div class="flex items-center justify-center gap-4">
                <a href="#" class="inline-flex items-center px-8 py-4 bg-white text-blue-600 font-semibold rounded-xl hover:bg-gray-50 transition-all duration-200 shadow-lg">
                    Browse Coupons
                </a>
                <a href="#" class="inline-flex items-center px-8 py-4 bg-transparent border-2 border-white text-white font-semibold rounded-xl hover:bg-white hover:text-blue-600 transition-all duration-200">
                    Hot Deals
                </a>
            </div>
        </div>
    </section>

    <!-- Featured Coupons -->
    <section class="py-16 bg-white">
        <div class="container mx-auto px-4">
            <div class="text-center mb-12">
                <h2 class="text-4xl font-display font-bold text-gray-900 mb-4">🔥 Featured Coupons</h2>
                <p class="text-xl text-gray-600 max-w-2xl mx-auto">Hand-picked exclusive deals and verified coupons from top brands</p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <!-- Coupon Card 1 -->
                <div class="card card-hover transition-all duration-300">
                    <div class="p-6">
                        <div class="flex items-center justify-between mb-4">
                            <div class="flex items-center space-x-3">
                                <div class="w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center">
                                    <span class="text-lg font-bold text-gray-600">A</span>
                                </div>
                                <div>
                                    <h3 class="font-semibold text-gray-900">Amazon</h3>
                                    <p class="text-sm text-gray-500">Electronics</p>
                                </div>
                            </div>
                            <span class="badge badge-accent">Exclusive</span>
                        </div>
                        
                        <h4 class="text-lg font-semibold text-gray-900 mb-2">20% Off Electronics</h4>
                        <p class="text-gray-600 text-sm mb-4">Get 20% off on all electronics during Prime Day sale</p>
                        
                        <div class="bg-gradient-to-r from-blue-500 to-green-500 text-white px-4 py-2 rounded-lg mb-4">
                            <div class="text-xs uppercase tracking-wide">Save</div>
                            <div class="font-bold text-xl">20%</div>
                        </div>
                        
                        <div class="bg-gray-50 border-2 border-dashed border-gray-300 rounded-lg p-3 mb-4">
                            <div class="text-xs text-gray-500 uppercase tracking-wide">Coupon Code</div>
                            <div class="font-mono font-bold text-lg text-gray-900">PRIME20</div>
                        </div>
                        
                        <button class="w-full btn-primary">Get Deal</button>
                    </div>
                </div>

                <!-- Coupon Card 2 -->
                <div class="card card-hover transition-all duration-300">
                    <div class="p-6">
                        <div class="flex items-center justify-between mb-4">
                            <div class="flex items-center space-x-3">
                                <div class="w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center">
                                    <span class="text-lg font-bold text-gray-600">N</span>
                                </div>
                                <div>
                                    <h3 class="font-semibold text-gray-900">Nike</h3>
                                    <p class="text-sm text-gray-500">Sports</p>
                                </div>
                            </div>
                            <span class="badge badge-primary">Featured</span>
                        </div>
                        
                        <h4 class="text-lg font-semibold text-gray-900 mb-2">25% Off Athletic Wear</h4>
                        <p class="text-gray-600 text-sm mb-4">Get 25% off on all athletic wear and footwear</p>
                        
                        <div class="bg-gradient-to-r from-blue-500 to-green-500 text-white px-4 py-2 rounded-lg mb-4">
                            <div class="text-xs uppercase tracking-wide">Save</div>
                            <div class="font-bold text-xl">25%</div>
                        </div>
                        
                        <div class="bg-gray-50 border-2 border-dashed border-gray-300 rounded-lg p-3 mb-4">
                            <div class="text-xs text-gray-500 uppercase tracking-wide">Coupon Code</div>
                            <div class="font-mono font-bold text-lg text-gray-900">SUMMER25</div>
                        </div>
                        
                        <button class="w-full btn-primary">Get Deal</button>
                    </div>
                </div>

                <!-- Coupon Card 3 -->
                <div class="card card-hover transition-all duration-300">
                    <div class="p-6">
                        <div class="flex items-center justify-between mb-4">
                            <div class="flex items-center space-x-3">
                                <div class="w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center">
                                    <span class="text-lg font-bold text-gray-600">H</span>
                                </div>
                                <div>
                                    <h3 class="font-semibold text-gray-900">H&M</h3>
                                    <p class="text-sm text-gray-500">Fashion</p>
                                </div>
                            </div>
                            <span class="badge badge-accent">Exclusive</span>
                        </div>
                        
                        <h4 class="text-lg font-semibold text-gray-900 mb-2">$10 Off $50</h4>
                        <p class="text-gray-600 text-sm mb-4">Get $10 off when you spend $50 or more on new collection</p>
                        
                        <div class="bg-gradient-to-r from-blue-500 to-green-500 text-white px-4 py-2 rounded-lg mb-4">
                            <div class="text-xs uppercase tracking-wide">Save</div>
                            <div class="font-bold text-xl">$10</div>
                        </div>
                        
                        <div class="bg-gray-50 border-2 border-dashed border-gray-300 rounded-lg p-3 mb-4">
                            <div class="text-xs text-gray-500 uppercase tracking-wide">Coupon Code</div>
                            <div class="font-mono font-bold text-lg text-gray-900">NEW10</div>
                        </div>
                        
                        <button class="w-full btn-primary">Get Deal</button>
                    </div>
                </div>

                <!-- Coupon Card 4 -->
                <div class="card card-hover transition-all duration-300">
                    <div class="p-6">
                        <div class="flex items-center justify-between mb-4">
                            <div class="flex items-center space-x-3">
                                <div class="w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center">
                                    <span class="text-lg font-bold text-gray-600">I</span>
                                </div>
                                <div>
                                    <h3 class="font-semibold text-gray-900">IKEA</h3>
                                    <p class="text-sm text-gray-500">Home</p>
                                </div>
                            </div>
                            <span class="badge badge-primary">Featured</span>
                        </div>
                        
                        <h4 class="text-lg font-semibold text-gray-900 mb-2">20% Off Kitchen Items</h4>
                        <p class="text-gray-600 text-sm mb-4">Save 20% on all kitchen furniture and accessories</p>
                        
                        <div class="bg-gradient-to-r from-blue-500 to-green-500 text-white px-4 py-2 rounded-lg mb-4">
                            <div class="text-xs uppercase tracking-wide">Save</div>
                            <div class="font-bold text-xl">20%</div>
                        </div>
                        
                        <div class="bg-gray-50 border-2 border-dashed border-gray-300 rounded-lg p-3 mb-4">
                            <div class="text-xs text-gray-500 uppercase tracking-wide">Coupon Code</div>
                            <div class="font-mono font-bold text-lg text-gray-900">KITCHEN20</div>
                        </div>
                        
                        <button class="w-full btn-primary">Get Deal</button>
                    </div>
                </div>
            </div>

            <div class="text-center mt-12">
                <a href="#" class="inline-flex items-center px-8 py-4 bg-gray-100 hover:bg-gray-200 text-gray-900 font-semibold rounded-xl transition-colors duration-200">
                    View All Coupons
                    <svg class="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                    </svg>
                </a>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-gray-900 text-white py-12">
        <div class="container mx-auto px-4">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <div>
                    <div class="flex items-center space-x-2 mb-4">
                        <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-green-500 rounded-xl flex items-center justify-center">
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                        <div>
                            <h3 class="text-xl font-display font-bold">MaxCoupon</h3>
                            <p class="text-gray-400 text-sm">Best Deals & Coupons</p>
                        </div>
                    </div>
                    <p class="text-gray-400 mb-6">Discover amazing deals and verified coupons from top brands across Europe and America.</p>
                </div>
                
                <div>
                    <h4 class="text-lg font-semibold mb-4">Quick Links</h4>
                    <ul class="space-y-3">
                        <li><a href="#" class="text-gray-400 hover:text-white transition-colors">All Coupons</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition-colors">Hot Deals</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition-colors">Top Brands</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition-colors">Categories</a></li>
                    </ul>
                </div>
                
                <div>
                    <h4 class="text-lg font-semibold mb-4">Categories</h4>
                    <ul class="space-y-3">
                        <li><a href="#" class="text-gray-400 hover:text-white transition-colors">Fashion & Clothing</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition-colors">Electronics & Tech</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition-colors">Home & Garden</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition-colors">Sports & Outdoors</a></li>
                    </ul>
                </div>
                
                <div>
                    <h4 class="text-lg font-semibold mb-4">Support</h4>
                    <ul class="space-y-3">
                        <li><a href="#" class="text-gray-400 hover:text-white transition-colors">Help Center</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition-colors">Contact Us</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition-colors">Privacy Policy</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition-colors">Terms of Service</a></li>
                    </ul>
                </div>
            </div>
            
            <div class="border-t border-gray-800 mt-8 pt-8 text-center">
                <p class="text-gray-400">© 2024 MaxCoupon. All rights reserved.</p>
            </div>
        </div>
    </footer>
</body>
</html>
