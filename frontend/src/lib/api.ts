// API Client for MaxCoupon Backend
const API_BASE_URL = import.meta.env.PUBLIC_API_URL || 'http://127.0.0.1:8080/api/v1';

// Types
export interface ApiResponse<T> {
  data?: T;
  error?: string;
  message?: string;
  pagination?: {
    page: number;
    limit: number;
    total: number;
  };
}

export interface Country {
  id: number;
  name: string;
  code: string;
  flag: string;
  locale: string;
  timezone: string;
  status: string;
  created_at: string;
  updated_at: string;
}

export interface Brand {
  id: number;
  name: string;
  unique_name: string;
  description: string;
  logo_url: string;
  website_url: string;
  category_id: number;
  country_id: number;
  popularity_score: number;
  total_coupons: number;
  total_deals: number;
  average_discount: number;
  status: string;
  created_at: string;
  updated_at: string;
}

export interface Coupon {
  id: number;
  title: string;
  description: string;
  code: string;
  discount_type: string;
  discount_value: number;
  discount_text: string;
  brand_id: number;
  category_id: number;
  country_id: number;
  is_featured: boolean;
  is_exclusive: boolean;
  click_count: number;
  success_rate: number;
  popularity_score: number;
  expiry_date: string;
  status: string;
  created_at: string;
  updated_at: string;
}

// API Client Class
class ApiClient {
  private baseURL: string;

  constructor(baseURL: string = API_BASE_URL) {
    this.baseURL = baseURL;
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<ApiResponse<T>> {
    try {
      const url = `${this.baseURL}${endpoint}`;
      const response = await fetch(url, {
        headers: {
          'Content-Type': 'application/json',
          ...options.headers,
        },
        ...options,
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      return data;
    } catch (error) {
      console.error('API request failed:', error);
      return {
        error: error instanceof Error ? error.message : 'Unknown error occurred',
      };
    }
  }

  // Countries
  async getCountries(): Promise<ApiResponse<Country[]>> {
    return this.request<Country[]>('/countries');
  }

  // Categories
  async getCategories(): Promise<ApiResponse<any[]>> {
    return this.request<any[]>('/categories');
  }

  // Brands
  async getBrands(params: {
    page?: number;
    limit?: number;
    category_id?: number;
    country_id?: number;
  } = {}): Promise<ApiResponse<Brand[]>> {
    const searchParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined) {
        searchParams.append(key, value.toString());
      }
    });
    
    const query = searchParams.toString();
    return this.request<Brand[]>(`/brands${query ? `?${query}` : ''}`);
  }

  async getBrandById(id: number): Promise<ApiResponse<Brand>> {
    return this.request<Brand>(`/brands/${id}`);
  }

  async getBrandByUniqueName(uniqueName: string): Promise<ApiResponse<Brand>> {
    return this.request<Brand>(`/brands/name/${uniqueName}`);
  }

  // Coupons
  async getCoupons(params: {
    page?: number;
    limit?: number;
    brand_id?: number;
    category_id?: number;
    country_id?: number;
    featured?: boolean;
    exclusive?: boolean;
  } = {}): Promise<ApiResponse<Coupon[]>> {
    const searchParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined) {
        searchParams.append(key, value.toString());
      }
    });
    
    const query = searchParams.toString();
    return this.request<Coupon[]>(`/coupons${query ? `?${query}` : ''}`);
  }

  async getCouponById(id: number): Promise<ApiResponse<Coupon>> {
    return this.request<Coupon>(`/coupons/${id}`);
  }

  async getFeaturedCoupons(limit: number = 10): Promise<ApiResponse<Coupon[]>> {
    return this.request<Coupon[]>(`/coupons/featured?limit=${limit}`);
  }

  async getCouponsByBrand(brandId: number, params: {
    page?: number;
    limit?: number;
  } = {}): Promise<ApiResponse<Coupon[]>> {
    const searchParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined) {
        searchParams.append(key, value.toString());
      }
    });
    
    const query = searchParams.toString();
    return this.request<Coupon[]>(`/coupons/brand/${brandId}${query ? `?${query}` : ''}`);
  }

  async trackCouponClick(couponId: number): Promise<ApiResponse<any>> {
    return this.request<any>(`/coupons/${couponId}/click`, {
      method: 'POST',
      body: JSON.stringify({
        user_agent: navigator.userAgent,
        referrer: document.referrer,
        timestamp: new Date().toISOString(),
      }),
    });
  }

  // Search
  async searchAll(query: string, params: {
    page?: number;
    limit?: number;
  } = {}): Promise<ApiResponse<any>> {
    const searchParams = new URLSearchParams({ q: query });
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined) {
        searchParams.append(key, value.toString());
      }
    });
    
    return this.request<any>(`/search?${searchParams.toString()}`);
  }

  // Analytics
  async trackPageView(page: string): Promise<void> {
    try {
      await this.request('/analytics/page-view', {
        method: 'POST',
        body: JSON.stringify({
          page,
          user_agent: navigator.userAgent,
          referrer: document.referrer,
          timestamp: new Date().toISOString(),
        }),
      });
    } catch (error) {
      console.error('Failed to track page view:', error);
    }
  }
}

// Export singleton instance
export const api = new ApiClient();
export default api;
