// AdSense Configuration
// This file manages all AdSense ad codes used throughout the site
// Each ad code has a unique key and can be rotated based on performance

export const AD_CODES = {
  // Modal ads - shown in coupon/deal choice modals
  modal_banner_1: {
    name: 'Modal Banner 1',
    code: `
      <script async src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-XXXXXXXXXXXXXXXX" crossorigin="anonymous"></script>
      <!-- Modal Banner 1 -->
      <ins class="adsbygoogle"
           style="display:block"
           data-ad-client="ca-pub-XXXXXXXXXXXXXXXX"
           data-ad-slot="1234567890"
           data-ad-format="auto"
           data-full-width-responsive="true"></ins>
      <script>
           (adsbygoogle = window.adsbygoogle || []).push({});
      </script>
    `,
    position: 'modal',
    maxClickRate: 30.0,
    priority: 1
  },
  
  modal_banner_2: {
    name: 'Modal Banner 2',
    code: `
      <script async src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-XXXXXXXXXXXXXXXX" crossorigin="anonymous"></script>
      <!-- Modal Banner 2 -->
      <ins class="adsbygoogle"
           style="display:block"
           data-ad-client="ca-pub-XXXXXXXXXXXXXXXX"
           data-ad-slot="0987654321"
           data-ad-format="auto"
           data-full-width-responsive="true"></ins>
      <script>
           (adsbygoogle = window.adsbygoogle || []).push({});
      </script>
    `,
    position: 'modal',
    maxClickRate: 30.0,
    priority: 2
  },
  
  modal_banner_3: {
    name: 'Modal Banner 3',
    code: `
      <script async src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-XXXXXXXXXXXXXXXX" crossorigin="anonymous"></script>
      <!-- Modal Banner 3 -->
      <ins class="adsbygoogle"
           style="display:block"
           data-ad-client="ca-pub-XXXXXXXXXXXXXXXX"
           data-ad-slot="1122334455"
           data-ad-format="auto"
           data-full-width-responsive="true"></ins>
      <script>
           (adsbygoogle = window.adsbygoogle || []).push({});
      </script>
    `,
    position: 'modal',
    maxClickRate: 30.0,
    priority: 3
  },

  // Sidebar ads - shown in sidebar sections
  sidebar_banner_1: {
    name: 'Sidebar Banner 1',
    code: `
      <script async src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-XXXXXXXXXXXXXXXX" crossorigin="anonymous"></script>
      <!-- Sidebar Banner 1 -->
      <ins class="adsbygoogle"
           style="display:block"
           data-ad-client="ca-pub-XXXXXXXXXXXXXXXX"
           data-ad-slot="5566778899"
           data-ad-format="auto"
           data-full-width-responsive="true"></ins>
      <script>
           (adsbygoogle = window.adsbygoogle || []).push({});
      </script>
    `,
    position: 'sidebar',
    maxClickRate: 30.0,
    priority: 1
  },

  // Header ads - shown in header sections
  header_banner_1: {
    name: 'Header Banner 1',
    code: `
      <script async src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-XXXXXXXXXXXXXXXX" crossorigin="anonymous"></script>
      <!-- Header Banner 1 -->
      <ins class="adsbygoogle"
           style="display:block"
           data-ad-client="ca-pub-XXXXXXXXXXXXXXXX"
           data-ad-slot="9988776655"
           data-ad-format="auto"
           data-full-width-responsive="true"></ins>
      <script>
           (adsbygoogle = window.adsbygoogle || []).push({});
      </script>
    `,
    position: 'header',
    maxClickRate: 30.0,
    priority: 1
  }
};

// Configuration settings
export const AD_CONFIG = {
  // Maximum click rate before rotating to next ad (percentage)
  MAX_CLICK_RATE: 30.0,
  
  // Minimum display count before calculating click rate
  MIN_DISPLAY_COUNT: 10,
  
  // API endpoints
  API_BASE_URL: 'http://127.0.0.1:8080/api/v1',
  
  // Ad positions
  POSITIONS: {
    MODAL: 'modal',
    SIDEBAR: 'sidebar', 
    HEADER: 'header',
    FOOTER: 'footer'
  }
};

// Helper functions
export const AdUtils = {
  // Get ads by position
  getAdsByPosition(position) {
    return Object.entries(AD_CODES)
      .filter(([key, ad]) => ad.position === position)
      .map(([key, ad]) => ({ key, ...ad }));
  },

  // Get ad code by key
  getAdCode(key) {
    return AD_CODES[key]?.code || '';
  },

  // Get ad name by key
  getAdName(key) {
    return AD_CODES[key]?.name || '';
  },

  // Check if ad exists
  hasAd(key) {
    return key in AD_CODES;
  },

  // Get all ad keys for a position
  getAdKeys(position) {
    return Object.keys(AD_CODES).filter(key => AD_CODES[key].position === position);
  }
};

// Export default configuration
export default {
  AD_CODES,
  AD_CONFIG,
  AdUtils
};
