---
import { api } from '../lib/api';

// Fetch popular brands from API
let popularBrands = [];
try {
	const response = await api.getBrands(1, 8); // Get first 8 brands
	if (response.data) {
		popularBrands = response.data.map(brand => ({
			id: brand.id,
			name: brand.name,
			description: brand.description,
			logo: brand.logo_url || "/images/brands/default.png",
			totalCoupons: brand.total_coupons || 0,
			totalDeals: brand.total_deals || 0,
			averageDiscount: brand.average_discount || 0,
			popularityScore: brand.popularity_score || 0,
			categories: brand.category ? [brand.category.name] : [],
			href: `/brands/${brand.unique_name || brand.id}`
		}));
	}
} catch (error) {
	console.error('Failed to fetch brands:', error);
	// Fallback data
	popularBrands = [
	{
		id: 1,
		name: "Amazon",
		description: "Global e-commerce giant with millions of products",
		logo: "/images/brands/amazon.png",
		totalCoupons: 156,
		totalDeals: 89,
		averageDiscount: 25,
		popularityScore: 98,
		categories: ["Electronics", "Books", "Home", "Fashion"],
		href: "/brands/amazon"
	},
	{
		id: 2,
		name: "Nike",
		description: "Leading athletic footwear and apparel brand",
		logo: "/images/brands/nike.png",
		totalCoupons: 78,
		totalDeals: 45,
		averageDiscount: 30,
		popularityScore: 95,
		categories: ["Sports", "Footwear", "Apparel"],
		href: "/brands/nike"
	},
	{
		id: 3,
		name: "H&M",
		description: "Swedish multinational clothing retailer",
		logo: "/images/brands/hm.png",
		totalCoupons: 92,
		totalDeals: 67,
		averageDiscount: 20,
		popularityScore: 88,
		categories: ["Fashion", "Clothing", "Accessories"],
		href: "/brands/hm"
	},
	{
		id: 4,
		name: "IKEA",
		description: "Swedish furniture and home goods retailer",
		logo: "/images/brands/ikea.png",
		totalCoupons: 34,
		totalDeals: 28,
		averageDiscount: 15,
		popularityScore: 85,
		categories: ["Furniture", "Home", "Kitchen"],
		href: "/brands/ikea"
	},
	{
		id: 5,
		name: "Adidas",
		description: "German multinational sportswear corporation",
		logo: "/images/brands/adidas.png",
		totalCoupons: 65,
		totalDeals: 41,
		averageDiscount: 28,
		popularityScore: 92,
		categories: ["Sports", "Footwear", "Apparel"],
		href: "/brands/adidas"
	},
	{
		id: 6,
		name: "Apple",
		description: "Technology company known for innovative products",
		logo: "/images/brands/apple.png",
		totalCoupons: 23,
		totalDeals: 15,
		averageDiscount: 10,
		popularityScore: 96,
		categories: ["Electronics", "Technology", "Accessories"],
		href: "/brands/apple"
	},
	{
		id: 7,
		name: "Zara",
		description: "Spanish fast fashion clothing retailer",
		logo: "/images/brands/zara.png",
		totalCoupons: 48,
		totalDeals: 32,
		averageDiscount: 22,
		popularityScore: 82,
		categories: ["Fashion", "Clothing", "Accessories"],
		href: "/brands/zara"
	},
	{
		id: 8,
		name: "Samsung",
		description: "South Korean electronics and technology conglomerate",
		logo: "/images/brands/samsung.png",
		totalCoupons: 41,
		totalDeals: 29,
		averageDiscount: 18,
		popularityScore: 89,
		categories: ["Electronics", "Technology", "Mobile"],
		href: "/brands/samsung"
	}
];
}
---

<section class="py-16 bg-white">
	<div class="container mx-auto px-4">
		<!-- Section Header -->
		<div class="text-center mb-12">
			<h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
				Popular Brands
			</h2>
			<p class="text-lg text-gray-600 max-w-2xl mx-auto">
				Shop from your favorite brands and save with exclusive coupons and deals
			</p>
		</div>

		<!-- Brands Grid -->
		<div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-8 gap-6 mb-12">
			{popularBrands.map((brand) => (
				<a 
					href={brand.href}
					class="group block"
				>
					<div class="card text-center p-6">
						<!-- Brand Logo Placeholder -->
						<div class="w-16 h-16 mx-auto mb-4 bg-purple-100 rounded-xl flex items-center justify-center group-hover:bg-purple-200 transition-all duration-200">
							<span class="text-2xl font-bold text-purple-600">
								{brand.name.charAt(0)}
							</span>
						</div>

						<!-- Brand Name -->
						<h3 class="font-semibold text-gray-900 mb-2 group-hover:text-purple-600 transition-colors">
							{brand.name}
						</h3>

						<!-- Stats -->
						<div class="space-y-1 text-sm text-gray-500">
							<div>{brand.totalCoupons + brand.totalDeals} offers</div>
							<div class="text-green-600 font-medium">
								{brand.averageDiscount}% avg. discount
							</div>
						</div>

						<!-- Popularity Indicator -->
						<div class="mt-3">
							<div class="w-full bg-gray-200 rounded-full h-1.5">
								<div
									class="bg-purple-600 h-1.5 rounded-full transition-all duration-300"
									style={`width: ${brand.popularityScore}%`}
								></div>
							</div>
							<div class="text-xs text-gray-400 mt-1">
								{brand.popularityScore}% popularity
							</div>
						</div>
					</div>
				</a>
			))}
		</div>

		<!-- Featured Brands Carousel -->
		<div class="mb-12">
			<h3 class="text-2xl font-display font-bold text-gray-900 mb-6 text-center">
				Featured Brand Spotlight
			</h3>
			
			<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
				{popularBrands.slice(0, 3).map((brand) => (
					<div class="card card-hover">
						<div class="p-6">
							<!-- Brand Header -->
							<div class="flex items-center space-x-4 mb-4">
								<div class="w-12 h-12 bg-gradient-to-br from-gray-100 to-gray-200 rounded-lg flex items-center justify-center">
									<span class="text-xl font-bold text-gray-600">
										{brand.name.charAt(0)}
									</span>
								</div>
								<div>
									<h4 class="text-xl font-semibold text-gray-900">{brand.name}</h4>
									<p class="text-gray-500 text-sm">{brand.description}</p>
								</div>
							</div>

							<!-- Categories -->
							<div class="mb-4">
								<div class="flex flex-wrap gap-2">
									{brand.categories.map((category) => (
										<span class="badge badge-primary text-xs">
											{category}
										</span>
									))}
								</div>
							</div>

							<!-- Stats Grid -->
							<div class="grid grid-cols-3 gap-4 mb-6">
								<div class="text-center">
									<div class="text-2xl font-bold text-primary-600">{brand.totalCoupons}</div>
									<div class="text-xs text-gray-500">Coupons</div>
								</div>
								<div class="text-center">
									<div class="text-2xl font-bold text-secondary-600">{brand.totalDeals}</div>
									<div class="text-xs text-gray-500">Deals</div>
								</div>
								<div class="text-center">
									<div class="text-2xl font-bold text-green-600">{brand.averageDiscount}%</div>
									<div class="text-xs text-gray-500">Avg. Discount</div>
								</div>
							</div>

							<!-- Action Buttons -->
							<div class="flex space-x-3">
								<a 
									href={`${brand.href}/coupons`}
									class="flex-1 btn-primary text-center text-sm py-2"
								>
									View Coupons
								</a>
								<a 
									href={`${brand.href}/deals`}
									class="flex-1 btn-outline text-center text-sm py-2"
								>
									View Deals
								</a>
							</div>
						</div>
					</div>
				))}
			</div>
		</div>

		<!-- Brand Categories -->
		<div class="bg-white rounded-2xl p-8 mb-12">
			<h3 class="text-2xl font-display font-bold text-gray-900 mb-6 text-center">
				Shop by Brand Category
			</h3>
			
			<div class="grid grid-cols-2 md:grid-cols-4 gap-6">
				<a href="/brands?category=fashion" class="group text-center">
					<div class="w-16 h-16 mx-auto mb-3 bg-gradient-to-br from-pink-100 to-purple-100 rounded-xl flex items-center justify-center group-hover:from-pink-200 group-hover:to-purple-200 transition-all duration-300">
						<span class="text-2xl">👗</span>
					</div>
					<h4 class="font-semibold text-gray-900 group-hover:text-primary-600 transition-colors">Fashion</h4>
					<p class="text-sm text-gray-500">25 brands</p>
				</a>

				<a href="/brands?category=electronics" class="group text-center">
					<div class="w-16 h-16 mx-auto mb-3 bg-gradient-to-br from-blue-100 to-indigo-100 rounded-xl flex items-center justify-center group-hover:from-blue-200 group-hover:to-indigo-200 transition-all duration-300">
						<span class="text-2xl">📱</span>
					</div>
					<h4 class="font-semibold text-gray-900 group-hover:text-primary-600 transition-colors">Electronics</h4>
					<p class="text-sm text-gray-500">18 brands</p>
				</a>

				<a href="/brands?category=sports" class="group text-center">
					<div class="w-16 h-16 mx-auto mb-3 bg-gradient-to-br from-green-100 to-emerald-100 rounded-xl flex items-center justify-center group-hover:from-green-200 group-hover:to-emerald-200 transition-all duration-300">
						<span class="text-2xl">⚽</span>
					</div>
					<h4 class="font-semibold text-gray-900 group-hover:text-primary-600 transition-colors">Sports</h4>
					<p class="text-sm text-gray-500">12 brands</p>
				</a>

				<a href="/brands?category=home" class="group text-center">
					<div class="w-16 h-16 mx-auto mb-3 bg-gradient-to-br from-orange-100 to-red-100 rounded-xl flex items-center justify-center group-hover:from-orange-200 group-hover:to-red-200 transition-all duration-300">
						<span class="text-2xl">🏠</span>
					</div>
					<h4 class="font-semibold text-gray-900 group-hover:text-primary-600 transition-colors">Home & Garden</h4>
					<p class="text-sm text-gray-500">15 brands</p>
				</a>
			</div>
		</div>

		<!-- View All Button -->
		<div class="text-center">
			<a 
				href="/brands" 
				class="inline-flex items-center px-8 py-4 bg-primary-600 hover:bg-primary-700 text-white font-semibold rounded-xl transition-colors duration-200"
			>
				View All Brands
				<svg class="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
					<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
				</svg>
			</a>
		</div>
	</div>
</section>

<script>
	document.addEventListener('DOMContentLoaded', () => {
		// Add hover effects and analytics tracking
		const brandCards = document.querySelectorAll('[href^="/brands/"]');
		
		brandCards.forEach(card => {
			card.addEventListener('click', (e) => {
				const brandName = card.querySelector('h3, h4')?.textContent?.trim();
				
				// Track brand click
				if (brandName) {
					try {
						fetch('/api/analytics/brand-click', {
							method: 'POST',
							headers: {
								'Content-Type': 'application/json',
							},
							body: JSON.stringify({
								brand_name: brandName,
								user_agent: navigator.userAgent,
								referrer: document.referrer,
								timestamp: new Date().toISOString()
							})
						});
					} catch (error) {
						console.error('Failed to track brand click:', error);
					}
				}
			});
		});
	});
</script>
