---
// Deal Modal Component
---

<!-- Deal Modal -->
<div id="dealModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50 flex items-center justify-center p-4">
	<div class="bg-white rounded-xl max-w-4xl w-full max-h-[90vh] overflow-hidden shadow-2xl">
		<!-- Modal Header -->
		<div class="flex justify-between items-center p-6 border-b border-gray-200">
			<h2 class="text-2xl font-bold text-gray-900">Get This Deal</h2>
			<button id="closeDealModal" class="text-gray-400 hover:text-gray-600 transition-colors">
				<svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
					<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
				</svg>
			</button>
		</div>

		<!-- Modal Content -->
		<div class="flex flex-col h-[calc(90vh-120px)]">
			<!-- Top Section - Advertisement -->
			<div id="dealAdSection" class="h-1/2 p-6 border-b border-gray-200 cursor-pointer hover:bg-gray-50 transition-colors">
				<div class="h-full flex flex-col">
					<h4 class="text-lg font-semibold text-gray-900 mb-4">Sponsored Content</h4>

					<!-- Ad Content Container -->
					<div id="dealAdContent" class="flex-1 flex items-center justify-center bg-gray-100 rounded-lg">
						<div class="text-center">
							<div class="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600 mx-auto mb-2"></div>
							<p class="text-gray-600">Loading advertisement...</p>
						</div>
					</div>

					<p class="text-xs text-gray-500 text-center mt-4">Click anywhere on this section to get the deal</p>
				</div>
			</div>

			<!-- Bottom Section - Deal Information -->
			<div id="dealInfoSection" class="h-1/2 p-6 cursor-pointer hover:bg-gray-50 transition-colors">
				<div class="h-full flex flex-col">
					<!-- Brand Logo -->
					<div class="flex items-center mb-4">
						<img id="dealBrandLogo" src="/images/placeholder-brand.svg" alt="Brand" class="w-12 h-12 object-contain rounded-lg border border-gray-200 mr-3">
						<div>
							<h3 id="dealBrandName" class="text-lg font-bold text-gray-900">Brand Name</h3>
							<p class="text-sm text-gray-600">Exclusive Deal</p>
						</div>
					</div>

					<!-- Deal Details -->
					<div class="flex-1 flex flex-col lg:flex-row gap-4">
						<div class="flex-1">
							<h4 id="dealTitle" class="text-base font-semibold text-gray-900 mb-2">Deal Title</h4>
							<p id="dealDescription" class="text-sm text-gray-600 mb-3">Deal description will appear here...</p>
						</div>

						<!-- Deal Value and Pricing -->
						<div class="lg:w-80 space-y-3">
							<!-- Deal Value -->
							<div class="bg-gradient-to-r from-purple-100 to-pink-100 rounded-lg p-3">
								<div class="flex items-center justify-between">
									<span class="text-xs text-gray-600">Deal Value</span>
									<span id="dealValue" class="text-lg font-bold text-purple-600">Save Big!</span>
								</div>
							</div>

							<!-- Deal Info -->
							<div class="bg-gray-50 rounded-lg p-3 space-y-2 text-sm text-gray-600">
								<div class="flex justify-between">
									<span>Original Price:</span>
									<span id="dealOriginalPrice" class="line-through">$99.99</span>
								</div>
								<div class="flex justify-between">
									<span>Deal Price:</span>
									<span id="dealPrice" class="text-green-600 font-semibold">$49.99</span>
								</div>
								<div class="flex justify-between">
									<span>You Save:</span>
									<span id="dealSavings" class="text-red-600 font-semibold">$50.00</span>
								</div>
							</div>
						</div>
					</div>

					<!-- Action Button -->
					<div class="mt-4">
						<button class="w-full bg-gradient-to-r from-purple-600 to-pink-600 text-white py-2 px-4 rounded-lg font-semibold hover:from-purple-700 hover:to-pink-700 transition-all duration-200 transform hover:scale-105">
							🛒 Get This Deal
						</button>
						<p class="text-xs text-gray-500 text-center mt-2">Click anywhere on this section to get the deal</p>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>

<script>
	// Deal Modal JavaScript
	let currentDealData = {};
	let currentDealAd = null;

	// Show deal modal function
	window.showDealModal = function(dealId, title, brand, storeUrl, brandLogo, description, originalPrice, dealPrice, discountPercentage) {
		console.log('showDealModal called with:', { dealId, title, brand, storeUrl, brandLogo, description, originalPrice, dealPrice, discountPercentage });
		
		// Store current deal data
		currentDealData = {
			dealId,
			title,
			brand,
			storeUrl,
			brandLogo,
			description,
			originalPrice,
			dealPrice,
			discountPercentage
		};

		// Update deal information
		document.getElementById('dealBrandName').textContent = brand;
		document.getElementById('dealBrandLogo').src = brandLogo;
		document.getElementById('dealBrandLogo').alt = brand;
		document.getElementById('dealTitle').textContent = title;
		document.getElementById('dealDescription').textContent = description;
		
		// Calculate and display deal value
		const savings = originalPrice - dealPrice;
		document.getElementById('dealValue').textContent = `${discountPercentage}% OFF`;
		document.getElementById('dealOriginalPrice').textContent = `$${originalPrice.toFixed(2)}`;
		document.getElementById('dealPrice').textContent = `$${dealPrice.toFixed(2)}`;
		document.getElementById('dealSavings').textContent = `$${savings.toFixed(2)}`;

		// Load advertisement
		loadDealAd(dealId);

		// Show modal
		document.getElementById('dealModal').classList.remove('hidden');
		document.body.style.overflow = 'hidden';
	};

	// Close deal modal function
	window.closeDealModal = function() {
		document.getElementById('dealModal').classList.add('hidden');
		document.body.style.overflow = 'auto';
		currentDealData = {};
		currentDealAd = null;
	};

	// Load advertisement for deal
	async function loadDealAd(dealId) {
		try {
			const response = await fetch(`/api/v1/ads/modal?entity_type=deal&entity_id=${dealId}`);
			const data = await response.json();
			
			if (data.success && data.data) {
				currentDealAd = data.data;
				document.getElementById('dealAdContent').innerHTML = data.data.ad_code;
			} else {
				document.getElementById('dealAdContent').innerHTML = '<p class="text-gray-500">No advertisement available</p>';
			}
		} catch (error) {
			console.error('Error loading deal ad:', error);
			document.getElementById('dealAdContent').innerHTML = '<p class="text-gray-500">Failed to load advertisement</p>';
		}
	}

	// Record ad click for deal
	async function recordDealAdClick() {
		if (!currentDealAd) return;
		
		try {
			await fetch('/api/v1/ads/click', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
				},
				body: JSON.stringify({
					ad_id: currentDealAd.id,
					entity_type: 'deal',
					entity_id: currentDealData.dealId
				})
			});
			console.log('Deal ad click recorded');
		} catch (error) {
			console.error('Error recording deal ad click:', error);
		}
	}

	// Deal info section click handler
	window.selectDealAffiliate = function(event) {
		console.log('selectDealAffiliate called');

		const storeUrl = currentDealData.storeUrl;
		console.log('Store URL:', storeUrl);

		// Jump directly to affiliate link (don't close modal)
		if (storeUrl && storeUrl !== '#') {
			console.log('Opening deal affiliate link:', storeUrl);
			window.open(storeUrl, '_blank');
		} else {
			console.log('No store URL available for deal');
		}

		// Track deal click
		trackDealClick(currentDealData.dealId);
		console.log('Deal affiliate link clicked:', currentDealData.dealId);
	};

	// Deal ad section click handler
	window.selectDealAd = function(event) {
		console.log('selectDealAd called');

		const storeUrl = currentDealData.storeUrl;
		console.log('Store URL:', storeUrl);

		// Record ad click since user interacted with the ad
		recordDealAdClick();

		// Open affiliate link in new tab (don't close modal)
		if (storeUrl && storeUrl !== '#') {
			console.log('Opening deal affiliate link:', storeUrl);
			window.open(storeUrl, '_blank');
		} else {
			console.log('No store URL available for deal');
		}

		// Track deal click
		trackDealClick(currentDealData.dealId);
		console.log('Deal ad option clicked:', currentDealData.dealId);
	};

	// Track deal click
	async function trackDealClick(dealId) {
		try {
			await fetch(`/api/v1/deals/${dealId}/click`, {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
				}
			});
			console.log('Deal click tracked');
		} catch (error) {
			console.error('Error tracking deal click:', error);
		}
	}

	// Event listeners
	document.addEventListener('DOMContentLoaded', function() {
		// Close modal button
		document.getElementById('closeDealModal').addEventListener('click', closeDealModal);

		// Deal info section click
		document.getElementById('dealInfoSection').addEventListener('click', selectDealAffiliate);

		// Deal ad section click
		document.getElementById('dealAdSection').addEventListener('click', selectDealAd);

		// Close modal when clicking outside
		document.getElementById('dealModal').addEventListener('click', function(e) {
			if (e.target === this) {
				// Don't auto-close, user must click close button
				// closeDealModal();
			}
		});

		// Prevent ESC key from closing modal
		document.addEventListener('keydown', function(e) {
			if (e.key === 'Escape') {
				// Don't auto-close, user must click close button
				// closeDealModal();
			}
		});
	});
</script>
