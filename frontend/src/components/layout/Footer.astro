---
// Footer component with comprehensive links and information
---

<footer class="bg-gray-50 border-t border-gray-200">
	<!-- Main Footer Content -->
	<div class="container mx-auto px-4 py-12">
		<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
			<!-- Company Info -->
			<div class="lg:col-span-1">
				<div class="flex items-center space-x-2 mb-4">
					<div class="w-10 h-10 bg-purple-600 rounded-lg flex items-center justify-center">
						<svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
						</svg>
					</div>
					<div>
						<h3 class="text-xl font-bold text-gray-900">MaxCoupon</h3>
						<p class="text-gray-600 text-sm">Best Deals & Coupons</p>
					</div>
				</div>
				<p class="text-gray-600 mb-6 leading-relaxed">
					Discover amazing deals and verified coupons from top brands across Europe and America.
					Save money on your favorite products with our exclusive offers.
				</p>
				<div class="flex space-x-3">
					<a href="#" class="w-10 h-10 bg-white border border-gray-200 hover:bg-purple-50 hover:border-purple-200 rounded-lg flex items-center justify-center transition-colors">
						<svg class="w-5 h-5 text-gray-600" fill="currentColor" viewBox="0 0 24 24">
							<path d="M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z"/>
						</svg>
					</a>
					<a href="#" class="w-10 h-10 bg-white border border-gray-200 hover:bg-purple-50 hover:border-purple-200 rounded-lg flex items-center justify-center transition-colors">
						<svg class="w-5 h-5 text-gray-600" fill="currentColor" viewBox="0 0 24 24">
							<path d="M22.46 6c-.77.35-1.6.58-2.46.69.88-.53 1.56-1.37 1.88-2.38-.83.5-1.75.85-2.72 1.05C18.37 4.5 17.26 4 16 4c-2.35 0-4.27 1.92-4.27 4.29 0 .34.04.67.11.98C8.28 9.09 5.11 7.38 3 4.79c-.37.63-.58 1.37-.58 2.15 0 1.49.75 2.81 1.91 3.56-.71 0-1.37-.2-1.95-.5v.03c0 2.08 1.48 3.82 3.44 4.21a4.22 4.22 0 0 1-1.93.07 4.28 4.28 0 0 0 4 2.98 8.521 8.521 0 0 1-5.33 1.84c-.34 0-.68-.02-1.02-.06C3.44 20.29 5.7 21 8.12 21 16 21 20.33 14.46 20.33 8.79c0-.19 0-.37-.01-.56.84-.6 1.56-1.36 2.14-2.23z"/>
						</svg>
					</a>
					<a href="#" class="w-10 h-10 bg-white border border-gray-200 hover:bg-purple-50 hover:border-purple-200 rounded-lg flex items-center justify-center transition-colors">
						<svg class="w-5 h-5 text-gray-600" fill="currentColor" viewBox="0 0 24 24">
							<path d="M12.017 0C5.396 0 .029 5.367.029 11.987c0 5.079 3.158 9.417 7.618 11.174-.105-.949-.199-2.403.041-3.439.219-.937 1.406-5.957 1.406-5.957s-.359-.72-.359-1.781c0-1.663.967-2.911 2.168-2.911 1.024 0 1.518.769 1.518 1.688 0 1.029-.653 2.567-.992 3.992-.285 1.193.6 2.165 1.775 2.165 2.128 0 3.768-2.245 3.768-5.487 0-2.861-2.063-4.869-5.008-4.869-3.41 0-5.409 2.562-5.409 5.199 0 1.033.394 2.143.889 2.741.099.12.112.225.085.345-.09.375-.293 1.199-.334 1.363-.053.225-.172.271-.402.165-1.495-.69-2.433-2.878-2.433-4.646 0-3.776 2.748-7.252 7.92-7.252 4.158 0 7.392 2.967 7.392 6.923 0 4.135-2.607 7.462-6.233 7.462-1.214 0-2.357-.629-2.746-1.378l-.748 2.853c-.271 1.043-1.002 2.35-1.492 3.146C9.57 23.812 10.763 24.009 12.017 24.009c6.624 0 11.99-5.367 11.99-11.988C24.007 5.367 18.641.001.012.001z"/>
						</svg>
					</a>
					<a href="#" class="w-10 h-10 bg-white border border-gray-200 hover:bg-purple-50 hover:border-purple-200 rounded-lg flex items-center justify-center transition-colors">
						<svg class="w-5 h-5 text-gray-600" fill="currentColor" viewBox="0 0 24 24">
							<path d="M12.007 0C5.373 0 0 5.372 0 12.007s5.373 12.006 12.007 12.006 12.006-5.372 12.006-12.006S18.641.001 12.007.001zM8.672 18.816c-.3 0-.593-.102-.826-.285-.42-.329-.713-.826-.713-1.224v-7.147c0-.398.293-.895.713-1.224.42-.329 1.049-.329 1.469 0l6.19 3.574c.42.329.713.826.713 1.224s-.293.895-.713 1.224l-6.19 3.574c-.21.164-.469.284-.643.284z"/>
						</svg>
					</a>
				</div>
			</div>

			<!-- Quick Links -->
			<div>
				<h4 class="text-lg font-semibold text-gray-900 mb-4">Quick Links</h4>
				<ul class="space-y-3">
					<li><a href="/coupons" class="text-gray-600 hover:text-purple-600 transition-colors">All Coupons</a></li>
					<li><a href="/deals" class="text-gray-600 hover:text-purple-600 transition-colors">Hot Deals</a></li>
					<li><a href="/brands" class="text-gray-600 hover:text-purple-600 transition-colors">Top Brands</a></li>
					<li><a href="/categories" class="text-gray-600 hover:text-purple-600 transition-colors">Categories</a></li>
					<li><a href="/search" class="text-gray-600 hover:text-purple-600 transition-colors">Search</a></li>
					<li><a href="/blog" class="text-gray-600 hover:text-purple-600 transition-colors">Blog</a></li>
				</ul>
			</div>

			<!-- Popular Categories -->
			<div>
				<h4 class="text-lg font-semibold text-gray-900 mb-4">Popular Categories</h4>
				<ul class="space-y-3">
					<li><a href="/categories/fashion-clothing" class="text-gray-600 hover:text-purple-600 transition-colors">Fashion & Clothing</a></li>
					<li><a href="/categories/electronics-tech" class="text-gray-600 hover:text-purple-600 transition-colors">Electronics & Tech</a></li>
					<li><a href="/categories/home-garden" class="text-gray-600 hover:text-purple-600 transition-colors">Home & Garden</a></li>
					<li><a href="/categories/health-beauty" class="text-gray-600 hover:text-purple-600 transition-colors">Health & Beauty</a></li>
					<li><a href="/categories/sports-outdoors" class="text-gray-600 hover:text-purple-600 transition-colors">Sports & Outdoors</a></li>
					<li><a href="/categories/travel-leisure" class="text-gray-600 hover:text-purple-600 transition-colors">Travel & Leisure</a></li>
				</ul>
			</div>

			<!-- Support & Legal -->
			<div>
				<h4 class="text-lg font-semibold text-gray-900 mb-4">Support & Legal</h4>
				<ul class="space-y-3">
					<li><a href="/help" class="text-gray-600 hover:text-purple-600 transition-colors">Help Center</a></li>
					<li><a href="/contact" class="text-gray-600 hover:text-purple-600 transition-colors">Contact Us</a></li>
					<li><a href="/about" class="text-gray-600 hover:text-purple-600 transition-colors">About Us</a></li>
					<li><a href="/privacy" class="text-gray-600 hover:text-purple-600 transition-colors">Privacy Policy</a></li>
					<li><a href="/terms" class="text-gray-600 hover:text-purple-600 transition-colors">Terms of Service</a></li>
					<li><a href="/sitemap" class="text-gray-600 hover:text-purple-600 transition-colors">Sitemap</a></li>
				</ul>
			</div>
		</div>
	</div>

	<!-- Newsletter Signup -->
	<div class="border-t border-gray-200 bg-purple-50">
		<div class="container mx-auto px-4 py-8">
			<div class="max-w-2xl mx-auto text-center">
				<h3 class="text-2xl font-bold text-gray-900 mb-2">Never Miss a Deal!</h3>
				<p class="text-gray-600 mb-6">Subscribe to our newsletter and get the latest coupons and deals delivered to your inbox.</p>
				<form class="flex flex-col sm:flex-row gap-4 max-w-md mx-auto">
					<input
						type="email"
						placeholder="Enter your email address"
						class="flex-1 px-4 py-3 bg-white border border-gray-300 rounded-lg text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
						required
					/>
					<button
						type="submit"
						class="btn-primary"
					>
						Subscribe
					</button>
				</form>
				<p class="text-xs text-gray-500 mt-3">
					By subscribing, you agree to our Privacy Policy and consent to receive updates from our company.
				</p>
			</div>
		</div>
	</div>

	<!-- Bottom Bar -->
	<div class="border-t border-gray-200">
		<div class="container mx-auto px-4 py-6">
			<div class="flex flex-col md:flex-row items-center justify-between">
				<div class="text-gray-600 text-sm mb-4 md:mb-0">
					© 2024 MaxCoupon. All rights reserved.
				</div>
				<div class="flex items-center space-x-6">
					<div class="flex items-center space-x-4 text-sm text-gray-600">
						<span>Available in:</span>
						<div class="flex space-x-2">
							<span class="text-lg">🇺🇸</span>
							<span class="text-lg">🇨🇦</span>
							<span class="text-lg">🇬🇧</span>
							<span class="text-lg">🇩🇪</span>
							<span class="text-lg">🇫🇷</span>
							<span class="text-lg">🇸🇪</span>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</footer>

<script>
	// Newsletter subscription
	document.addEventListener('DOMContentLoaded', () => {
		const newsletterForm = document.querySelector('footer form');
		
		newsletterForm?.addEventListener('submit', async (e) => {
			e.preventDefault();
			
			const formData = new FormData(e.target as HTMLFormElement);
			const email = formData.get('email') as string;
			
			if (!email) return;
			
			try {
				// Here you would typically send the email to your backend
				console.log('Newsletter subscription:', email);
				
				// Show success message
				const button = e.target.querySelector('button[type="submit"]') as HTMLButtonElement;
				const originalText = button.textContent;
				button.textContent = 'Subscribed!';
				button.disabled = true;
				button.classList.add('bg-green-600');
				
				setTimeout(() => {
					button.textContent = originalText;
					button.disabled = false;
					button.classList.remove('bg-green-600');
					(e.target as HTMLFormElement).reset();
				}, 3000);
				
			} catch (error) {
				console.error('Newsletter subscription failed:', error);
				// Show error message
				alert('Subscription failed. Please try again.');
			}
		});
	});
</script>
