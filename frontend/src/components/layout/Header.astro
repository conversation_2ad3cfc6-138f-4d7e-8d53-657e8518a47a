---
import SearchBar from '../search/SearchBar.astro';
import Navigation from './Navigation.astro';
import MobileMenu from './MobileMenu.astro';
---

<header class="sticky top-0 z-50 bg-white border-b border-gray-200 shadow-sm">
	<!-- Top Bar -->
	<div class="bg-primary-600 text-white py-2">
		<div class="container mx-auto px-4">
			<div class="flex items-center justify-between text-sm">
				<div class="flex items-center space-x-4">
					<span class="hidden sm:inline">🎉 Save up to 70% with our exclusive deals!</span>
					<span class="sm:hidden">🎉 Exclusive deals up to 70% off!</span>
				</div>
				<div class="flex items-center space-x-4">
					<a href="/help" class="hover:text-primary-200 transition-colors">Help</a>
					<span class="text-primary-300">|</span>
					<a href="/contact" class="hover:text-primary-200 transition-colors">Contact</a>
				</div>
			</div>
		</div>
	</div>

	<!-- Main Header -->
	<div class="container mx-auto px-4 py-4">
		<div class="flex items-center justify-between">
			<!-- Logo -->
			<div class="flex items-center">
				<a href="/" class="flex items-center space-x-2 group">
					<div class="w-10 h-10 bg-gradient-to-br from-primary-500 to-secondary-500 rounded-xl flex items-center justify-center group-hover:scale-105 transition-transform duration-200">
						<svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
						</svg>
					</div>
					<div class="hidden sm:block">
						<h1 class="text-2xl font-display font-bold text-gradient">MaxCoupon</h1>
						<p class="text-xs text-gray-500 -mt-1">Best Deals & Coupons</p>
					</div>
				</a>
			</div>

			<!-- Search Bar (Desktop) -->
			<div class="hidden lg:block flex-1 max-w-2xl mx-8">
				<SearchBar />
			</div>

			<!-- Desktop Navigation -->
			<div class="hidden lg:block">
				<Navigation />
			</div>

			<!-- Mobile Menu Button -->
			<div class="lg:hidden">
				<button 
					id="mobile-menu-button"
					class="p-2 rounded-lg text-gray-600 hover:text-gray-900 hover:bg-gray-100 transition-colors"
					aria-label="Open mobile menu"
				>
					<svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
					</svg>
				</button>
			</div>
		</div>

		<!-- Mobile Search Bar -->
		<div class="lg:hidden mt-4">
			<SearchBar />
		</div>
	</div>

	<!-- Mobile Menu -->
	<MobileMenu />
</header>

<script>
	// Mobile menu toggle
	document.addEventListener('DOMContentLoaded', () => {
		const mobileMenuButton = document.getElementById('mobile-menu-button');
		const mobileMenu = document.getElementById('mobile-menu');
		const mobileMenuClose = document.getElementById('mobile-menu-close');
		const mobileMenuOverlay = document.getElementById('mobile-menu-overlay');

		function openMobileMenu() {
			mobileMenu?.classList.remove('hidden');
			document.body.style.overflow = 'hidden';
		}

		function closeMobileMenu() {
			mobileMenu?.classList.add('hidden');
			document.body.style.overflow = '';
		}

		mobileMenuButton?.addEventListener('click', openMobileMenu);
		mobileMenuClose?.addEventListener('click', closeMobileMenu);
		mobileMenuOverlay?.addEventListener('click', closeMobileMenu);

		// Close menu on escape key
		document.addEventListener('keydown', (e) => {
			if (e.key === 'Escape') {
				closeMobileMenu();
			}
		});

		// Close menu when clicking on a link
		const mobileMenuLinks = mobileMenu?.querySelectorAll('a');
		mobileMenuLinks?.forEach(link => {
			link.addEventListener('click', closeMobileMenu);
		});
	});

	// Header scroll effect
	let lastScrollY = window.scrollY;
	const header = document.querySelector('header');

	window.addEventListener('scroll', () => {
		const currentScrollY = window.scrollY;
		
		if (currentScrollY > 100) {
			header?.classList.add('shadow-lg');
		} else {
			header?.classList.remove('shadow-lg');
		}

		// Hide header on scroll down, show on scroll up
		if (currentScrollY > lastScrollY && currentScrollY > 200) {
			header?.classList.add('-translate-y-full');
		} else {
			header?.classList.remove('-translate-y-full');
		}

		lastScrollY = currentScrollY;
	});
</script>

<style>
	header {
		transition: transform 0.3s ease-in-out, box-shadow 0.3s ease-in-out;
	}
</style>
