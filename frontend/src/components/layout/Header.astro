---
import SearchBar from '../search/SearchBar.astro';
import Navigation from './Navigation.astro';
import MobileMenu from './MobileMenu.astro';
---

<header class="sticky top-0 z-50 bg-white/95 backdrop-blur-sm border-b border-gray-200">
	<!-- Top Bar -->
	<div class="bg-purple-600 py-2">
		<div class="container mx-auto px-4">
			<div class="flex items-center justify-between text-sm text-white">
				<div class="flex items-center space-x-4">
					<span class="hidden sm:inline">✨ Exclusive 2025 Premium Deals - Save up to 80%!</span>
					<span class="sm:hidden">✨ Premium deals up to 80% off!</span>
				</div>
				<div class="flex items-center space-x-4">
					<a href="/help" class="hover:text-purple-200 transition-colors">Premium Support</a>
					<span class="text-purple-300">|</span>
					<a href="/contact" class="hover:text-purple-200 transition-colors">VIP Access</a>
				</div>
			</div>
		</div>
	</div>

	<!-- Main Header -->
	<div class="container mx-auto px-4 py-4">
		<div class="flex items-center justify-between">
			<!-- Logo -->
			<div class="flex items-center">
				<a href="/" class="flex items-center space-x-3 group">
					<div class="relative">
						<div class="w-12 h-12 bg-purple-600 rounded-xl flex items-center justify-center group-hover:scale-105 transition-transform duration-200">
							<svg class="w-7 h-7 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
							</svg>
						</div>
						<div class="absolute -top-1 -right-1 w-3 h-3 bg-purple-400 rounded-full"></div>
					</div>
					<div class="hidden sm:block">
						<h1 class="text-2xl font-bold text-gradient">MaxCoupon</h1>
						<p class="text-xs text-gray-500 -mt-1">Premium Deals Platform</p>
					</div>
				</a>
			</div>

			<!-- Search Bar (Desktop) -->
			<div class="hidden lg:block flex-1 max-w-2xl mx-8">
				<SearchBar />
			</div>

			<!-- Desktop Navigation -->
			<div class="hidden lg:block">
				<Navigation />
			</div>

			<!-- Mobile Menu Button -->
			<div class="lg:hidden">
				<button
					id="mobile-menu-button"
					class="p-2 rounded-lg text-gray-600 hover:text-purple-600 hover:bg-purple-50 transition-colors"
					aria-label="Open mobile menu"
				>
					<svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
					</svg>
				</button>
			</div>
		</div>

		<!-- Mobile Search Bar -->
		<div class="lg:hidden mt-4">
			<SearchBar />
		</div>
	</div>

	<!-- Mobile Menu -->
	<MobileMenu />
</header>

<script>
	// Mobile menu toggle
	document.addEventListener('DOMContentLoaded', () => {
		const mobileMenuButton = document.getElementById('mobile-menu-button');
		const mobileMenu = document.getElementById('mobile-menu');
		const mobileMenuClose = document.getElementById('mobile-menu-close');
		const mobileMenuOverlay = document.getElementById('mobile-menu-overlay');

		function openMobileMenu() {
			mobileMenu?.classList.remove('hidden');
			document.body.style.overflow = 'hidden';
		}

		function closeMobileMenu() {
			mobileMenu?.classList.add('hidden');
			document.body.style.overflow = '';
		}

		mobileMenuButton?.addEventListener('click', openMobileMenu);
		mobileMenuClose?.addEventListener('click', closeMobileMenu);
		mobileMenuOverlay?.addEventListener('click', closeMobileMenu);

		// Close menu on escape key
		document.addEventListener('keydown', (e) => {
			if (e.key === 'Escape') {
				closeMobileMenu();
			}
		});

		// Close menu when clicking on a link
		const mobileMenuLinks = mobileMenu?.querySelectorAll('a');
		mobileMenuLinks?.forEach(link => {
			link.addEventListener('click', closeMobileMenu);
		});
	});

	// Header scroll effect
	let lastScrollY = window.scrollY;
	const header = document.querySelector('header');

	window.addEventListener('scroll', () => {
		const currentScrollY = window.scrollY;
		
		if (currentScrollY > 100) {
			header?.classList.add('shadow-lg');
		} else {
			header?.classList.remove('shadow-lg');
		}

		// Hide header on scroll down, show on scroll up
		if (currentScrollY > lastScrollY && currentScrollY > 200) {
			header?.classList.add('-translate-y-full');
		} else {
			header?.classList.remove('-translate-y-full');
		}

		lastScrollY = currentScrollY;
	});
</script>

<style>
	header {
		transition: transform 0.3s ease-in-out, box-shadow 0.3s ease-in-out;
	}

	.animate-glow {
		animation: glow 2s ease-in-out infinite alternate;
	}

	@keyframes glow {
		0% { box-shadow: 0 0 20px rgba(217, 70, 239, 0.5); }
		100% { box-shadow: 0 0 40px rgba(217, 70, 239, 0.8); }
	}
</style>
