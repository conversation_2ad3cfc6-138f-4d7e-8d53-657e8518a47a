---
import SearchBar from '../search/SearchBar.astro';
import Navigation from './Navigation.astro';
import MobileMenu from './MobileMenu.astro';
---

<header class="sticky top-0 z-50 bg-white/80 backdrop-blur-xl border-b border-neutral-200/50 shadow-sm">
	<!-- Top Bar -->
	<div class="bg-gradient-to-r from-primary-600 via-secondary-500 to-accent-600 py-2">
		<div class="container mx-auto px-4">
			<div class="flex items-center justify-between text-sm text-white">
				<div class="flex items-center space-x-4">
					<span class="hidden sm:inline animate-pulse">✨ Exclusive Premium Deals - Save up to 80% on Luxury Brands!</span>
					<span class="sm:hidden animate-pulse">✨ Premium deals up to 80% off!</span>
				</div>
				<div class="flex items-center space-x-4">
					<a href="/vip" class="hover:text-primary-200 transition-colors font-medium">VIP Access</a>
					<span class="text-white/50">|</span>
					<a href="/support" class="hover:text-primary-200 transition-colors font-medium">Premium Support</a>
				</div>
			</div>
		</div>
	</div>

	<!-- Main Header -->
	<div class="container mx-auto px-4 py-4">
		<div class="flex items-center justify-between">
			<!-- Logo -->
			<div class="flex items-center">
				<a href="/" class="flex items-center space-x-3 group">
					<div class="relative">
						<div class="w-14 h-14 bg-gradient-to-br from-primary-500 via-secondary-500 to-accent-500 rounded-2xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300 shadow-lg">
							<svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
							</svg>
						</div>
						<div class="absolute -top-1 -right-1 w-5 h-5 bg-gradient-to-r from-secondary-500 to-accent-500 rounded-full animate-pulse flex items-center justify-center">
							<span class="text-white text-xs font-bold">✦</span>
						</div>
					</div>
					<div class="hidden sm:block">
						<h1 class="text-3xl font-display font-bold text-gradient">MaxCoupon</h1>
						<p class="text-xs text-neutral-500 -mt-1 font-medium">Premium Deals Platform</p>
					</div>
				</a>
			</div>

			<!-- Search Bar (Desktop) -->
			<div class="hidden lg:block flex-1 max-w-2xl mx-8">
				<SearchBar />
			</div>

			<!-- Desktop Navigation -->
			<div class="hidden lg:flex items-center space-x-6">
				<nav class="flex items-center space-x-6">
					<a href="/coupons" class="text-neutral-700 hover:text-primary-600 font-medium transition-colors">
						Coupons
					</a>
					<a href="/deals" class="text-neutral-700 hover:text-primary-600 font-medium transition-colors">
						Deals
					</a>
					<a href="/brands" class="text-neutral-700 hover:text-primary-600 font-medium transition-colors">
						Brands
					</a>
					<a href="/categories" class="text-neutral-700 hover:text-primary-600 font-medium transition-colors">
						Categories
					</a>
				</nav>
				
				<!-- CTA Button -->
				<a href="/vip" class="btn-luxury text-sm px-6 py-2">
					Join VIP
				</a>
			</div>

			<!-- Mobile Menu Button -->
			<div class="lg:hidden">
				<button 
					id="mobile-menu-button"
					class="p-3 rounded-xl text-neutral-700 hover:text-primary-600 hover:bg-primary-50 transition-colors"
					aria-label="Open mobile menu"
				>
					<svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
					</svg>
				</button>
			</div>
		</div>

		<!-- Mobile Search Bar -->
		<div class="lg:hidden mt-4">
			<SearchBar />
		</div>
	</div>

	<!-- Mobile Menu -->
	<MobileMenu />
</header>

<script>
	document.addEventListener('DOMContentLoaded', () => {
		// Mobile menu toggle
		const mobileMenuButton = document.getElementById('mobile-menu-button');
		const mobileMenu = document.getElementById('mobile-menu');
		const mobileMenuClose = document.getElementById('mobile-menu-close');
		const mobileMenuOverlay = document.getElementById('mobile-menu-overlay');

		function openMobileMenu() {
			mobileMenu?.classList.remove('hidden');
			document.body.style.overflow = 'hidden';
		}

		function closeMobileMenu() {
			mobileMenu?.classList.add('hidden');
			document.body.style.overflow = '';
		}

		mobileMenuButton?.addEventListener('click', openMobileMenu);
		mobileMenuClose?.addEventListener('click', closeMobileMenu);
		mobileMenuOverlay?.addEventListener('click', closeMobileMenu);

		// Close menu on escape key
		document.addEventListener('keydown', (e) => {
			if (e.key === 'Escape') {
				closeMobileMenu();
			}
		});

		// Close menu when clicking on a link
		const mobileMenuLinks = mobileMenu?.querySelectorAll('a');
		mobileMenuLinks?.forEach(link => {
			link.addEventListener('click', closeMobileMenu);
		});

		// Header scroll effect - keep it always visible
		let lastScrollY = window.scrollY;
		const header = document.querySelector('header');

		window.addEventListener('scroll', () => {
			const currentScrollY = window.scrollY;
			
			// Add shadow when scrolling
			if (currentScrollY > 10) {
				header?.classList.add('shadow-lg');
			} else {
				header?.classList.remove('shadow-lg');
			}

			// Keep header always visible - no hiding
			lastScrollY = currentScrollY;
		});

		// Add smooth hover effects to navigation links
		const navLinks = document.querySelectorAll('nav a');
		navLinks.forEach(link => {
			link.addEventListener('mouseenter', () => {
				link.style.transform = 'translateY(-1px)';
			});
			
			link.addEventListener('mouseleave', () => {
				link.style.transform = 'translateY(0)';
			});
		});

		// Logo animation on hover
		const logo = document.querySelector('a[href="/"]');
		logo?.addEventListener('mouseenter', () => {
			const logoIcon = logo.querySelector('div > div');
			logoIcon?.classList.add('animate-pulse');
		});
		
		logo?.addEventListener('mouseleave', () => {
			const logoIcon = logo.querySelector('div > div');
			logoIcon?.classList.remove('animate-pulse');
		});
	});
</script>

<style>
	/* Header specific styles */
	header {
		transition: all 0.3s ease-in-out;
	}
	
	/* Logo glow effect */
	.logo-glow {
		box-shadow: 0 0 20px rgba(168, 85, 247, 0.3);
	}
	
	/* Navigation link hover effects */
	nav a {
		position: relative;
		transition: all 0.3s ease;
	}
	
	nav a::after {
		content: '';
		position: absolute;
		bottom: -4px;
		left: 0;
		width: 0;
		height: 2px;
		background: linear-gradient(135deg, #a855f7, #f59e0b);
		transition: width 0.3s ease;
	}
	
	nav a:hover::after {
		width: 100%;
	}
	
	/* Search bar enhancement */
	.search-container {
		position: relative;
	}
	
	.search-container::before {
		content: '';
		position: absolute;
		inset: 0;
		padding: 1px;
		background: linear-gradient(135deg, #a855f7, #f59e0b);
		border-radius: 12px;
		mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
		mask-composite: exclude;
		opacity: 0;
		transition: opacity 0.3s ease;
	}
	
	.search-container:focus-within::before {
		opacity: 1;
	}
</style>
