---
// Navigation items configuration
const navigationItems = [
	{
		label: 'Coupons',
		href: '/coupons',
		icon: 'ticket',
		description: 'Browse all available coupons'
	},
	{
		label: 'Deals',
		href: '/deals',
		icon: 'tag',
		description: 'Discover hot deals and offers'
	},
	{
		label: 'Brands',
		href: '/brands',
		icon: 'star',
		description: 'Shop by your favorite brands',
		dropdown: [
			{ label: 'Amazon', href: '/brands/amazon' },
			{ label: 'Nike', href: '/brands/nike' },
			{ label: 'H&M', href: '/brands/hm' },
			{ label: 'IKEA', href: '/brands/ikea' },
			{ label: 'Adidas', href: '/brands/adidas' },
			{ label: 'View All Brands', href: '/brands', featured: true }
		]
	},
	{
		label: 'Categories',
		href: '/categories',
		icon: 'grid',
		description: 'Browse by category',
		dropdown: [
			{ label: 'Fashion & Clothing', href: '/categories/fashion-clothing' },
			{ label: 'Electronics & Tech', href: '/categories/electronics-tech' },
			{ label: 'Home & Garden', href: '/categories/home-garden' },
			{ label: 'Health & Beauty', href: '/categories/health-beauty' },
			{ label: 'Sports & Outdoors', href: '/categories/sports-outdoors' },
			{ label: 'View All Categories', href: '/categories', featured: true }
		]
	}
];

// Get current path for active state
const currentPath = Astro.url.pathname;
---

<nav class="flex items-center space-x-8">
	{navigationItems.map((item) => (
		<div class="relative group">
			<a 
				href={item.href}
				class={`flex items-center space-x-1 px-3 py-2 rounded-lg text-sm font-medium transition-colors duration-200 ${
					currentPath.startsWith(item.href) 
						? 'text-primary-600 bg-primary-50' 
						: 'text-gray-700 hover:text-primary-600 hover:bg-gray-50'
				}`}
			>
				<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
					{item.icon === 'ticket' && (
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 5v2m0 4v2m0 4v2M5 5a2 2 0 00-2 2v3a1 1 0 001 1h1a1 1 0 001-1V7a2 2 0 012-2h8a2 2 0 012 2v1a1 1 0 001 1h1a1 1 0 001-1V7a2 2 0 00-2-2H5z"></path>
					)}
					{item.icon === 'tag' && (
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"></path>
					)}
					{item.icon === 'star' && (
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"></path>
					)}
					{item.icon === 'grid' && (
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z"></path>
					)}
				</svg>
				<span>{item.label}</span>
				{item.dropdown && (
					<svg class="w-4 h-4 transition-transform duration-200 group-hover:rotate-180" fill="none" stroke="currentColor" viewBox="0 0 24 24">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
					</svg>
				)}
			</a>

			{/* Dropdown Menu */}
			{item.dropdown && (
				<div class="absolute top-full left-0 mt-2 w-64 bg-white rounded-xl shadow-large border border-gray-100 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-50">
					<div class="p-4">
						<div class="text-xs font-semibold text-gray-500 uppercase tracking-wide mb-3">
							{item.description}
						</div>
						<div class="space-y-1">
							{item.dropdown.map((dropdownItem) => (
								<a 
									href={dropdownItem.href}
									class={`block px-3 py-2 rounded-lg text-sm transition-colors duration-200 ${
										dropdownItem.featured 
											? 'text-primary-600 font-medium hover:bg-primary-50' 
											: 'text-gray-700 hover:text-gray-900 hover:bg-gray-50'
									}`}
								>
									{dropdownItem.label}
									{dropdownItem.featured && (
										<svg class="inline w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
											<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
										</svg>
									)}
								</a>
							))}
						</div>
					</div>
				</div>
			)}
		</div>
	))}

	<!-- Country/Language Selector -->
	<div class="relative group">
		<button class="flex items-center space-x-1 px-3 py-2 rounded-lg text-sm font-medium text-gray-700 hover:text-primary-600 hover:bg-gray-50 transition-colors duration-200">
			<span class="text-lg">🇺🇸</span>
			<span>US</span>
			<svg class="w-4 h-4 transition-transform duration-200 group-hover:rotate-180" fill="none" stroke="currentColor" viewBox="0 0 24 24">
				<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
			</svg>
		</button>

		<div class="absolute top-full right-0 mt-2 w-48 bg-white rounded-xl shadow-large border border-gray-100 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-50">
			<div class="p-2">
				<div class="text-xs font-semibold text-gray-500 uppercase tracking-wide mb-2 px-3">
					Select Country
				</div>
				<a href="?country=us" class="flex items-center space-x-2 px-3 py-2 rounded-lg text-sm text-gray-700 hover:bg-gray-50 transition-colors">
					<span class="text-lg">🇺🇸</span>
					<span>United States</span>
				</a>
				<a href="?country=ca" class="flex items-center space-x-2 px-3 py-2 rounded-lg text-sm text-gray-700 hover:bg-gray-50 transition-colors">
					<span class="text-lg">🇨🇦</span>
					<span>Canada</span>
				</a>
				<a href="?country=gb" class="flex items-center space-x-2 px-3 py-2 rounded-lg text-sm text-gray-700 hover:bg-gray-50 transition-colors">
					<span class="text-lg">🇬🇧</span>
					<span>United Kingdom</span>
				</a>
				<a href="?country=de" class="flex items-center space-x-2 px-3 py-2 rounded-lg text-sm text-gray-700 hover:bg-gray-50 transition-colors">
					<span class="text-lg">🇩🇪</span>
					<span>Germany</span>
				</a>
				<a href="?country=fr" class="flex items-center space-x-2 px-3 py-2 rounded-lg text-sm text-gray-700 hover:bg-gray-50 transition-colors">
					<span class="text-lg">🇫🇷</span>
					<span>France</span>
				</a>
				<a href="?country=se" class="flex items-center space-x-2 px-3 py-2 rounded-lg text-sm text-gray-700 hover:bg-gray-50 transition-colors">
					<span class="text-lg">🇸🇪</span>
					<span>Sweden</span>
				</a>
			</div>
		</div>
	</div>
</nav>

<style>
	/* Ensure dropdown appears above other content */
	.group:hover .absolute {
		z-index: 50;
	}
</style>
