---
// Hot Deals component for displaying trending and popular coupons
const API_BASE_URL = import.meta.env.PUBLIC_API_URL || 'http://127.0.0.1:8080';

// Fetch hot deals from API
let hotDeals = [];
try {
	const response = await fetch(`${API_BASE_URL}/api/v1/coupons/popular?limit=6`);
	if (response.ok) {
		const data = await response.json();
		hotDeals = data.data || [];
	}
} catch (error) {
	console.error('Failed to fetch hot deals:', error);
}

// Helper function to format discount
function formatDiscount(coupon) {
	if (coupon.discount_type === 'percentage') {
		return `${coupon.discount_value}% OFF`;
	} else if (coupon.discount_type === 'fixed') {
		return `$${coupon.discount_value} OFF`;
	} else {
		return 'DEAL';
	}
}

// Helper function to get time remaining
function getTimeRemaining(expiryDate) {
	const now = new Date();
	const expiry = new Date(expiryDate);
	const diff = expiry - now;
	
	if (diff <= 0) return 'Expired';
	
	const days = Math.floor(diff / (1000 * 60 * 60 * 24));
	const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
	
	if (days > 0) {
		return `${days}d ${hours}h left`;
	} else if (hours > 0) {
		return `${hours}h left`;
	} else {
		return 'Ending soon';
	}
}
---

<section class="py-16 bg-gradient-to-br from-purple-50 to-white">
	<div class="container mx-auto px-4">
		<!-- Section Header -->
		<div class="text-center mb-12">
			<div class="inline-flex items-center justify-center w-16 h-16 bg-purple-600 rounded-full mb-4">
				<svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
					<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
				</svg>
			</div>
			<h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
				🔥 Hot Deals
			</h2>
			<p class="text-lg text-gray-600 max-w-2xl mx-auto">
				Trending coupons that everyone's talking about. Don't miss these popular deals!
			</p>
		</div>

		<!-- Hot Deals Grid -->
		<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
			{hotDeals.map((deal, index) => (
				<div class="card group relative overflow-hidden">
					<!-- Hot Badge -->
					{index < 3 && (
						<div class="absolute top-4 left-4 z-10">
							<span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-bold bg-red-500 text-white">
								<svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
									<path fill-rule="evenodd" d="M12.395 2.553a1 1 0 00-1.45-.385c-.345.23-.614.558-.822.88-.214.33-.403.713-.57 1.116-.334.804-.614 1.768-.84 2.734a31.365 31.365 0 00-.613 3.58 2.64 2.64 0 01-.945-1.067c-.328-.68-.398-1.534-.398-2.654A1 1 0 005.05 6.05 6.981 6.981 0 003 11a7 7 0 1011.95-4.95c-.592-.591-.98-.985-1.348-1.467-.363-.476-.724-1.063-1.207-2.03zM12.12 15.12A3 3 0 017 13s.879.5 2.5.5c0-1 .5-4 1.25-4.5.5 1 .786 1.293 1.371 1.879A2.99 2.99 0 0113 13a2.99 2.99 0 01-.879 2.121z" clip-rule="evenodd"></path>
								</svg>
								HOT
							</span>
						</div>
					)}

					<!-- Exclusive Badge -->
					{deal.is_exclusive && (
						<div class="absolute top-4 right-4 z-10">
							<span class="badge badge-primary text-xs">
								Exclusive
							</span>
						</div>
					)}

					<!-- Deal Content -->
					<div class="p-6">
						<!-- Brand Header -->
						<div class="flex items-center space-x-3 mb-4">
							<div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
								<span class="text-xl font-bold text-purple-600">
									{deal.brand?.name?.charAt(0) || 'D'}
								</span>
							</div>
							<div>
								<h3 class="font-semibold text-gray-900">{deal.brand?.name || 'Brand'}</h3>
								<p class="text-sm text-gray-500">{deal.category?.name || 'General'}</p>
							</div>
						</div>

						<!-- Deal Title -->
						<h4 class="text-lg font-semibold text-gray-900 mb-3 line-clamp-2 group-hover:text-purple-600 transition-colors">
							{deal.title}
						</h4>

						<!-- Discount Badge -->
						<div class="flex items-center justify-between mb-4">
							<div class="bg-purple-600 text-white px-4 py-2 rounded-lg font-bold text-xl">
								{formatDiscount(deal)}
							</div>
							<div class="text-right">
								<div class="text-sm text-gray-500">Success Rate</div>
								<div class="text-lg font-semibold text-green-600">{deal.success_rate || 0}%</div>
							</div>
						</div>

						<!-- Stats Row -->
						<div class="flex items-center justify-between text-sm text-gray-500 mb-4">
							<div class="flex items-center space-x-4">
								<span class="flex items-center">
									<svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
										<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
										<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
									</svg>
									{deal.click_count || 0}
								</span>
								<span class="flex items-center">
									<svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
										<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
									</svg>
									{getTimeRemaining(deal.expiry_date)}
								</span>
							</div>
							{deal.is_verified && (
								<span class="flex items-center text-green-600">
									<svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
										<path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
									</svg>
									Verified
								</span>
							)}
						</div>

						<!-- Action Buttons -->
						<div class="flex space-x-2">
							<button 
								class="btn-primary flex-1 text-sm py-2 copy-code-btn"
								data-code={deal.code}
								data-title={deal.title}
							>
								Get Code
							</button>
							<button class="btn-secondary text-sm py-2 px-4">
								<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path>
								</svg>
							</button>
						</div>
					</div>
				</div>
			))}
		</div>

		<!-- View All Button -->
		<div class="text-center">
			<a href="/deals" class="btn-outline inline-flex items-center px-8 py-3">
				<svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
					<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
				</svg>
				View All Hot Deals
			</a>
		</div>
	</div>
</section>

<!-- Success Modal -->
<div id="success-modal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
	<div class="bg-white rounded-xl p-6 max-w-md mx-4 animate-slide-up">
		<div class="text-center">
			<div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
				<svg class="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
					<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
				</svg>
			</div>
			<h3 class="text-lg font-semibold text-gray-900 mb-2">Code Copied!</h3>
			<p class="text-gray-600 mb-4" id="modal-message">Coupon code has been copied to your clipboard</p>
			<button id="modal-close" class="btn-primary px-6 py-2">
				Got it
			</button>
		</div>
	</div>
</div>

<script>
	document.addEventListener('DOMContentLoaded', () => {
		const copyButtons = document.querySelectorAll('.copy-code-btn');
		const modal = document.getElementById('success-modal');
		const modalMessage = document.getElementById('modal-message');
		const modalClose = document.getElementById('modal-close');

		// Handle copy code buttons
		copyButtons.forEach(button => {
			button.addEventListener('click', async () => {
				const code = button.getAttribute('data-code');
				const title = button.getAttribute('data-title');

				try {
					await navigator.clipboard.writeText(code);
					modalMessage.textContent = `Code "${code}" copied! Use it for: ${title}`;
					modal.classList.remove('hidden');
				} catch (err) {
					console.error('Failed to copy code:', err);
					// Fallback for older browsers
					const textArea = document.createElement('textarea');
					textArea.value = code;
					document.body.appendChild(textArea);
					textArea.select();
					document.execCommand('copy');
					document.body.removeChild(textArea);
					
					modalMessage.textContent = `Code "${code}" copied! Use it for: ${title}`;
					modal.classList.remove('hidden');
				}
			});
		});

		// Handle modal close
		modalClose.addEventListener('click', () => {
			modal.classList.add('hidden');
		});

		// Close modal on backdrop click
		modal.addEventListener('click', (e) => {
			if (e.target === modal) {
				modal.classList.add('hidden');
			}
		});
	});
</script>

<style>
	.line-clamp-2 {
		display: -webkit-box;
		-webkit-line-clamp: 2;
		-webkit-box-orient: vertical;
		overflow: hidden;
	}
</style>
