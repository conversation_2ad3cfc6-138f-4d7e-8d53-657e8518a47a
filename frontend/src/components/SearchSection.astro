---
// Search section component for finding coupons and deals
---

<section class="py-16 bg-gradient-to-br from-purple-50 to-white">
	<div class="container mx-auto px-4">
		<div class="max-w-4xl mx-auto text-center">
			<!-- Section Header -->
			<div class="mb-12">
				<h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
					Find Your Perfect <span class="text-gradient">Coupon</span>
				</h2>
				<p class="text-lg text-gray-600 max-w-2xl mx-auto">
					Search through thousands of verified coupons from your favorite brands and save more
				</p>
			</div>

			<!-- Search Form -->
			<div class="bg-white rounded-2xl p-8 shadow-sm border border-purple-100 mb-12">
				<form id="search-form" class="space-y-6">
					<!-- Main Search Input -->
					<div class="relative">
						<div class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
							<svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
							</svg>
						</div>
						<input
							type="text"
							id="search-query"
							placeholder="Search for coupons, brands, or stores..."
							class="w-full pl-12 pr-4 py-4 text-lg border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
						/>
					</div>

					<!-- Simplified Filter Options -->
					<div class="grid grid-cols-1 md:grid-cols-2 gap-4">
						<div>
							<label for="category-filter" class="block text-sm font-medium text-gray-700 mb-2">Category</label>
							<select id="category-filter" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent">
								<option value="">All Categories</option>
								<option value="electronics">Electronics</option>
								<option value="fashion">Fashion</option>
								<option value="home-garden">Home & Garden</option>
								<option value="sports-outdoors">Sports & Outdoors</option>
								<option value="beauty-health">Beauty & Health</option>
								<option value="travel">Travel</option>
							</select>
						</div>

						<div>
							<label for="sort-filter" class="block text-sm font-medium text-gray-700 mb-2">Sort By</label>
							<select id="sort-filter" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent">
								<option value="popularity">Most Popular</option>
								<option value="newest">Newest First</option>
								<option value="discount-high">Highest Discount</option>
								<option value="expiry">Expiring Soon</option>
							</select>
						</div>
					</div>

					<!-- Search Button -->
					<div class="flex justify-center">
						<button type="submit" class="btn-primary px-8 py-4 text-lg font-semibold">
							<svg class="w-5 h-5 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
							</svg>
							Search Coupons
						</button>
					</div>
				</form>
			</div>

			<!-- Quick Search Tags -->
			<div class="text-center">
				<p class="text-sm text-gray-600 mb-4">Popular searches:</p>
				<div class="flex flex-wrap justify-center gap-2">
					<button class="search-tag px-4 py-2 bg-purple-100 text-purple-700 rounded-full text-sm hover:bg-purple-200 transition-colors" data-query="Amazon">
						Amazon
					</button>
					<button class="search-tag px-4 py-2 bg-purple-100 text-purple-700 rounded-full text-sm hover:bg-purple-200 transition-colors" data-query="Nike">
						Nike
					</button>
					<button class="search-tag px-4 py-2 bg-purple-100 text-purple-700 rounded-full text-sm hover:bg-purple-200 transition-colors" data-query="Electronics">
						Electronics
					</button>
					<button class="search-tag px-4 py-2 bg-purple-100 text-purple-700 rounded-full text-sm hover:bg-purple-200 transition-colors" data-query="Fashion">
						Fashion
					</button>
					<button class="search-tag px-4 py-2 bg-purple-100 text-purple-700 rounded-full text-sm hover:bg-purple-200 transition-colors" data-query="Free Shipping">
						Free Shipping
					</button>
					<button class="search-tag px-4 py-2 bg-purple-100 text-purple-700 rounded-full text-sm hover:bg-purple-200 transition-colors" data-query="50% off">
						50% off
					</button>
				</div>
			</div>
		</div>
	</div>

	<!-- Search Results Container -->
	<div id="search-results" class="container mx-auto px-4 mt-12 hidden">
		<div class="max-w-6xl mx-auto">
			<div class="flex items-center justify-between mb-6">
				<h3 class="text-2xl font-bold text-gray-900">Search Results</h3>
				<div id="results-count" class="text-gray-600"></div>
			</div>
			<div id="results-grid" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
				<!-- Results will be populated here -->
			</div>
			<div id="loading" class="text-center py-8 hidden">
				<div class="inline-flex items-center px-4 py-2 font-semibold leading-6 text-sm shadow rounded-md text-purple-500 bg-white transition ease-in-out duration-150">
					<svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-purple-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
						<circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
						<path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
					</svg>
					Searching...
				</div>
			</div>
		</div>
	</div>
</section>

<script>
	document.addEventListener('DOMContentLoaded', () => {
		const searchForm = document.getElementById('search-form');
		const searchQuery = document.getElementById('search-query');
		const categoryFilter = document.getElementById('category-filter');
		const sortFilter = document.getElementById('sort-filter');
		const searchResults = document.getElementById('search-results');
		const resultsGrid = document.getElementById('results-grid');
		const resultsCount = document.getElementById('results-count');
		const loading = document.getElementById('loading');
		const searchTags = document.querySelectorAll('.search-tag');

		// Handle search form submission
		searchForm?.addEventListener('submit', async (e) => {
			e.preventDefault();
			await performSearch();
		});

		// Handle quick search tags
		searchTags.forEach(tag => {
			tag.addEventListener('click', () => {
				const query = tag.getAttribute('data-query');
				if (searchQuery) {
					searchQuery.value = query;
					performSearch();
				}
			});
		});

		// Perform search function
		async function performSearch() {
			const query = searchQuery?.value?.trim();
			if (!query) return;

			// Show loading
			loading?.classList.remove('hidden');
			searchResults?.classList.remove('hidden');
			resultsGrid.innerHTML = '';

			try {
				// Build search URL
				const params = new URLSearchParams({
					q: query,
					page: '1',
					limit: '12'
				});

				if (categoryFilter?.value) {
					params.append('category', categoryFilter.value);
				}
				if (sortFilter?.value) {
					params.append('sort', sortFilter.value);
				}

				// Make API call
				const response = await fetch(`/api/v1/coupons/search?${params}`);
				const data = await response.json();

				// Hide loading
				loading?.classList.add('hidden');

				if (data.data && data.data.length > 0) {
					displayResults(data.data);
					resultsCount.textContent = `Found ${data.pagination?.total || data.data.length} results`;
				} else {
					displayNoResults();
					resultsCount.textContent = 'No results found';
				}
			} catch (error) {
				console.error('Search error:', error);
				loading?.classList.add('hidden');
				displayError();
				resultsCount.textContent = 'Search error occurred';
			}
		}

		// Display search results
		function displayResults(coupons) {
			resultsGrid.innerHTML = coupons.map(coupon => `
				<div class="card p-6">
					<div class="flex items-center justify-between mb-4">
						<div class="flex items-center space-x-3">
							<div class="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
								<span class="text-lg font-bold text-purple-600">
									${coupon.brand?.name?.charAt(0) || 'C'}
								</span>
							</div>
							<div>
								<h3 class="font-semibold text-gray-900">${coupon.brand?.name || 'Brand'}</h3>
								<p class="text-sm text-gray-500">${coupon.category?.name || 'General'}</p>
							</div>
						</div>
						${coupon.is_exclusive ? '<span class="badge badge-primary text-xs">Exclusive</span>' : ''}
					</div>
					
					<h4 class="text-lg font-semibold text-gray-900 mb-2 line-clamp-2">
						${coupon.title}
					</h4>
					
					<p class="text-gray-600 text-sm mb-4 line-clamp-2">
						${coupon.description}
					</p>
					
					<div class="flex items-center justify-between mb-4">
						<div class="bg-purple-600 text-white px-4 py-2 rounded-lg font-bold text-lg">
							${formatDiscount(coupon)}
						</div>
						<div class="text-right">
							<div class="text-sm text-gray-500">Success Rate</div>
							<div class="text-lg font-semibold text-green-600">${coupon.success_rate || 0}%</div>
						</div>
					</div>
					
					<div class="flex space-x-2">
						<button class="btn-primary flex-1 text-sm py-2">
							Get Code
						</button>
						<button class="btn-secondary text-sm py-2 px-4">
							View Deal
						</button>
					</div>
				</div>
			`).join('');
		}

		// Display no results message
		function displayNoResults() {
			resultsGrid.innerHTML = `
				<div class="col-span-full text-center py-12">
					<div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
						<svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
						</svg>
					</div>
					<h3 class="text-lg font-semibold text-gray-900 mb-2">No results found</h3>
					<p class="text-gray-600">Try adjusting your search terms or filters</p>
				</div>
			`;
		}

		// Display error message
		function displayError() {
			resultsGrid.innerHTML = `
				<div class="col-span-full text-center py-12">
					<div class="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
						<svg class="w-8 h-8 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
						</svg>
					</div>
					<h3 class="text-lg font-semibold text-gray-900 mb-2">Search Error</h3>
					<p class="text-gray-600">Something went wrong. Please try again.</p>
				</div>
			`;
		}

		// Format discount helper function
		function formatDiscount(coupon) {
			if (coupon.discount_type === 'percentage') {
				return `${coupon.discount_value}% OFF`;
			} else if (coupon.discount_type === 'fixed') {
				return `$${coupon.discount_value} OFF`;
			} else {
				return 'DEAL';
			}
		}
	});
</script>

<style>
	.line-clamp-2 {
		display: -webkit-box;
		-webkit-line-clamp: 2;
		-webkit-box-orient: vertical;
		overflow: hidden;
	}
</style>
