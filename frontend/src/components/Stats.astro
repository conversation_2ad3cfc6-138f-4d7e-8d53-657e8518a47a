---
import { api } from '../lib/api';

// Fetch stats from API
let stats = {
	totalCoupons: 0,
	totalBrands: 0,
	totalSavings: 0,
	activeUsers: 0
};

try {
	// In a real implementation, you would have a stats endpoint
	// For now, we'll use placeholder data
	stats = {
		totalCoupons: 2500,
		totalBrands: 150,
		totalSavings: 1250000,
		activeUsers: 50000
	};
} catch (error) {
	console.error('Failed to fetch stats:', error);
}

// Helper function to format numbers
function formatNumber(num: number): string {
	if (num >= 1000000) {
		return (num / 1000000).toFixed(1) + 'M';
	} else if (num >= 1000) {
		return (num / 1000).toFixed(0) + 'K';
	}
	return num.toString();
}

function formatCurrency(num: number): string {
	if (num >= 1000000) {
		return '$' + (num / 1000000).toFixed(1) + 'M';
	} else if (num >= 1000) {
		return '$' + (num / 1000).toFixed(0) + 'K';
	}
	return '$' + num.toString();
}
---

<section class="py-16 relative overflow-hidden">
	<!-- Background Elements -->
	<div class="absolute inset-0 bg-gradient-to-br from-primary-500/5 to-secondary-500/5"></div>
	<div class="absolute top-0 left-1/4 w-96 h-96 bg-primary-500/10 rounded-full blur-3xl"></div>
	<div class="absolute bottom-0 right-1/4 w-96 h-96 bg-secondary-500/10 rounded-full blur-3xl"></div>

	<div class="container mx-auto px-4 relative">
		<div class="text-center mb-16">
			<h2 class="text-3xl md:text-4xl font-display font-bold text-white mb-4">
				Trusted by <span class="text-gradient">Smart Savers</span> Worldwide
			</h2>
			<p class="text-xl text-neutral-400 max-w-2xl mx-auto">
				Join millions who save money every day with our verified deals and exclusive offers
			</p>
		</div>

		<div class="grid grid-cols-2 lg:grid-cols-4 gap-8 max-w-6xl mx-auto">
			<!-- Total Coupons -->
			<div class="text-center group">
				<div class="card p-8 rounded-2xl border border-white/10 hover:border-primary-500/30 transition-all duration-300 group-hover:scale-105">
					<div class="w-16 h-16 bg-gradient-to-br from-primary-500 to-primary-600 rounded-2xl flex items-center justify-center mx-auto mb-6">
						<svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 14l6-6m-5.5.5h.01m4.99 5h.01M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16l3.5-2 3.5 2 3.5-2 3.5 2zM10 8.5a.5.5 0 11-1 0 .5.5 0 011 0zm5 5a.5.5 0 11-1 0 .5.5 0 011 0z"></path>
						</svg>
					</div>
					<div class="text-4xl font-bold text-white mb-2 counter" data-target={stats.totalCoupons}>
						{formatNumber(stats.totalCoupons)}
					</div>
					<div class="text-neutral-400 font-medium">Active Coupons</div>
				</div>
			</div>

			<!-- Total Brands -->
			<div class="text-center group">
				<div class="card p-8 rounded-2xl border border-white/10 hover:border-secondary-500/30 transition-all duration-300 group-hover:scale-105">
					<div class="w-16 h-16 bg-gradient-to-br from-secondary-500 to-secondary-600 rounded-2xl flex items-center justify-center mx-auto mb-6">
						<svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
						</svg>
					</div>
					<div class="text-4xl font-bold text-white mb-2 counter" data-target={stats.totalBrands}>
						{formatNumber(stats.totalBrands)}+
					</div>
					<div class="text-neutral-400 font-medium">Partner Brands</div>
				</div>
			</div>

			<!-- Total Savings -->
			<div class="text-center group">
				<div class="card p-8 rounded-2xl border border-white/10 hover:border-accent-500/30 transition-all duration-300 group-hover:scale-105">
					<div class="w-16 h-16 bg-gradient-to-br from-accent-500 to-accent-600 rounded-2xl flex items-center justify-center mx-auto mb-6">
						<svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
						</svg>
					</div>
					<div class="text-4xl font-bold text-white mb-2 counter" data-target={stats.totalSavings}>
						{formatCurrency(stats.totalSavings)}
					</div>
					<div class="text-neutral-400 font-medium">Total Saved</div>
				</div>
			</div>

			<!-- Active Users -->
			<div class="text-center group">
				<div class="card p-8 rounded-2xl border border-white/10 hover:border-primary-500/30 transition-all duration-300 group-hover:scale-105">
					<div class="w-16 h-16 bg-gradient-to-br from-primary-500 to-secondary-500 rounded-2xl flex items-center justify-center mx-auto mb-6">
						<svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
						</svg>
					</div>
					<div class="text-4xl font-bold text-white mb-2 counter" data-target={stats.activeUsers}>
						{formatNumber(stats.activeUsers)}+
					</div>
					<div class="text-neutral-400 font-medium">Happy Users</div>
				</div>
			</div>
		</div>

		<!-- Additional Trust Indicators -->
		<div class="mt-16 text-center">
			<div class="flex flex-wrap justify-center items-center gap-8 opacity-60">
				<div class="flex items-center space-x-2">
					<svg class="w-5 h-5 text-green-400" fill="currentColor" viewBox="0 0 20 20">
						<path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
					</svg>
					<span class="text-neutral-400 text-sm">SSL Secured</span>
				</div>
				<div class="flex items-center space-x-2">
					<svg class="w-5 h-5 text-green-400" fill="currentColor" viewBox="0 0 20 20">
						<path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
					</svg>
					<span class="text-neutral-400 text-sm">Verified Deals</span>
				</div>
				<div class="flex items-center space-x-2">
					<svg class="w-5 h-5 text-green-400" fill="currentColor" viewBox="0 0 20 20">
						<path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
					</svg>
					<span class="text-neutral-400 text-sm">24/7 Support</span>
				</div>
				<div class="flex items-center space-x-2">
					<svg class="w-5 h-5 text-green-400" fill="currentColor" viewBox="0 0 20 20">
						<path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
					</svg>
					<span class="text-neutral-400 text-sm">Free to Use</span>
				</div>
			</div>
		</div>
	</div>
</section>

<script>
	// Counter animation
	function animateCounters() {
		const counters = document.querySelectorAll('.counter');
		
		counters.forEach(counter => {
			const target = parseInt(counter.getAttribute('data-target') || '0');
			const increment = target / 100;
			let current = 0;
			
			const updateCounter = () => {
				if (current < target) {
					current += increment;
					counter.textContent = Math.ceil(current).toLocaleString();
					requestAnimationFrame(updateCounter);
				} else {
					counter.textContent = target.toLocaleString();
				}
			};
			
			// Start animation when element is visible
			const observer = new IntersectionObserver((entries) => {
				entries.forEach(entry => {
					if (entry.isIntersecting) {
						updateCounter();
						observer.unobserve(entry.target);
					}
				});
			});
			
			observer.observe(counter);
		});
	}

	// Initialize when DOM is loaded
	document.addEventListener('DOMContentLoaded', animateCounters);
</script>
