---
import SearchBar from './search/SearchBar.astro';
---

<!-- Hero Section -->
<section class="relative py-32 overflow-hidden">
	<div class="container mx-auto px-4 text-center relative z-10">
		<div class="max-w-5xl mx-auto">
			<h1 class="text-7xl md:text-8xl font-display font-bold mb-8 leading-tight">
				Discover
				<span class="block text-gradient animate-pulse">Premium Deals</span>
				<span class="block text-4xl md:text-5xl text-neutral-400 font-normal mt-4">
					That Actually Matter
				</span>
			</h1>
			
			<p class="text-2xl text-neutral-300 mb-12 max-w-3xl mx-auto leading-relaxed">
				Curated exclusive offers from the world's most prestigious brands. 
				<span class="text-primary-400">Save more, spend smarter</span> with our AI-powered deal discovery.
			</p>

			<!-- Search Bar -->
			<div class="mb-16 max-w-2xl mx-auto">
				<SearchBar />
			</div>

			<!-- Stats -->
			<div class="grid grid-cols-4 gap-8 mb-16 max-w-3xl mx-auto">
				<div class="text-center">
					<div class="text-4xl font-bold text-primary-400 mb-2">50K+</div>
					<div class="text-neutral-400 text-sm">Premium Coupons</div>
				</div>
				<div class="text-center">
					<div class="text-4xl font-bold text-secondary-400 mb-2">1K+</div>
					<div class="text-neutral-400 text-sm">Elite Brands</div>
				</div>
				<div class="text-center">
					<div class="text-4xl font-bold text-accent-400 mb-2">5M+</div>
					<div class="text-neutral-400 text-sm">Smart Savers</div>
				</div>
				<div class="text-center">
					<div class="text-4xl font-bold text-primary-400 mb-2">$500M+</div>
					<div class="text-neutral-400 text-sm">Total Saved</div>
				</div>
			</div>

			<!-- CTA Buttons -->
			<div class="flex items-center justify-center gap-6">
				<a href="/coupons" class="btn-primary text-white px-10 py-5 rounded-2xl font-semibold text-lg flex items-center space-x-3">
					<span>Explore Premium Deals</span>
					<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6"></path>
					</svg>
				</a>
				<a href="/deals" class="btn-outline text-white px-10 py-5 rounded-2xl font-semibold text-lg">
					Watch Demo
				</a>
			</div>
		</div>
	</div>

	<!-- Floating Elements -->
	<div class="absolute top-20 left-10 hidden lg:block floating-element">
		<div class="w-20 h-20 glass-effect rounded-2xl flex items-center justify-center">
			<span class="text-3xl">💎</span>
		</div>
	</div>
	<div class="absolute top-32 right-20 hidden lg:block floating-element" style="animation-delay: -2s;">
		<div class="w-16 h-16 glass-effect rounded-2xl flex items-center justify-center">
			<span class="text-2xl">🚀</span>
		</div>
	</div>
	<div class="absolute bottom-20 left-20 hidden lg:block floating-element" style="animation-delay: -4s;">
		<div class="w-18 h-18 glass-effect rounded-2xl flex items-center justify-center">
			<span class="text-2xl">⭐</span>
		</div>
	</div>
	<div class="absolute bottom-32 right-10 hidden lg:block floating-element" style="animation-delay: -6s;">
		<div class="w-14 h-14 glass-effect rounded-2xl flex items-center justify-center">
			<span class="text-xl">✨</span>
		</div>
	</div>
</section>

<style>
	.floating-element {
		animation: float 6s ease-in-out infinite;
	}
	
	@keyframes float {
		0%, 100% { transform: translateY(0px); }
		50% { transform: translateY(-20px); }
	}
</style>
