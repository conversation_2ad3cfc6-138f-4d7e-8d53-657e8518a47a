---
import SearchBar from './search/SearchBar.astro';
---

<!-- Hero Section -->
<section class="relative py-32 overflow-hidden">
	<!-- Animated Background Elements -->
	<div class="absolute inset-0 overflow-hidden">
		<div class="absolute top-1/4 left-1/4 w-64 h-64 bg-purple-500/10 rounded-full blur-3xl animate-pulse"></div>
		<div class="absolute bottom-1/4 right-1/4 w-96 h-96 bg-blue-500/10 rounded-full blur-3xl animate-pulse delay-1000"></div>
		<div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-80 h-80 bg-indigo-500/10 rounded-full blur-3xl animate-pulse delay-500"></div>
	</div>

	<div class="container mx-auto px-4 text-center relative z-10">
		<div class="max-w-6xl mx-auto">
			<!-- Badge -->
			<div class="mb-8">
				<span class="inline-block px-6 py-3 bg-primary-500/20 border border-primary-500/30 rounded-full text-primary-300 text-sm font-semibold">
					🎉 New: AI-Powered Deal Discovery Engine
				</span>
			</div>

			<h1 class="text-6xl md:text-7xl lg:text-8xl font-display font-bold mb-8 leading-tight">
				Discover
				<span class="block text-gradient bg-gradient-to-r from-purple-400 via-pink-400 to-blue-400 bg-clip-text text-transparent animate-pulse">
					Premium Deals
				</span>
				<span class="block text-3xl md:text-4xl lg:text-5xl text-neutral-400 font-normal mt-4">
					That Actually Matter
				</span>
			</h1>

			<p class="text-xl md:text-2xl text-neutral-300 mb-12 max-w-4xl mx-auto leading-relaxed">
				Curated exclusive offers from the world's most prestigious brands.
				<span class="text-primary-400 font-semibold">Save more, spend smarter</span> with our AI-powered deal discovery platform.
			</p>

			<!-- Enhanced Search Bar -->
			<div class="mb-16 max-w-4xl mx-auto">
				<div class="relative group">
					<div class="absolute inset-0 bg-gradient-to-r from-purple-500/20 to-blue-500/20 rounded-3xl blur-xl group-hover:blur-2xl transition-all duration-300"></div>
					<div class="relative bg-white/10 backdrop-blur-xl border border-white/20 rounded-3xl p-3">
						<div class="flex items-center">
							<div class="flex-1 relative">
								<input
									type="text"
									placeholder="Search for premium deals, exclusive coupons, or luxury brands..."
									class="w-full px-8 py-5 bg-transparent text-white placeholder-neutral-400 focus:outline-none text-lg"
								/>
								<svg class="absolute left-3 top-1/2 transform -translate-y-1/2 w-6 h-6 text-neutral-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
								</svg>
							</div>
							<button class="btn-primary px-10 py-5 rounded-2xl font-semibold text-lg hover:scale-105 transition-transform">
								Discover Deals
							</button>
						</div>
					</div>
				</div>

				<!-- Popular Searches -->
				<div class="mt-6 flex flex-wrap justify-center gap-3">
					<span class="text-neutral-400 text-sm">Trending:</span>
					<button class="px-4 py-2 bg-white/5 hover:bg-white/10 border border-white/10 rounded-full text-neutral-300 text-sm transition-colors hover:scale-105">
						Amazon Prime Day
					</button>
					<button class="px-4 py-2 bg-white/5 hover:bg-white/10 border border-white/10 rounded-full text-neutral-300 text-sm transition-colors hover:scale-105">
						Nike Flash Sale
					</button>
					<button class="px-4 py-2 bg-white/5 hover:bg-white/10 border border-white/10 rounded-full text-neutral-300 text-sm transition-colors hover:scale-105">
						Apple Deals
					</button>
					<button class="px-4 py-2 bg-white/5 hover:bg-white/10 border border-white/10 rounded-full text-neutral-300 text-sm transition-colors hover:scale-105">
						Fashion Week
					</button>
				</div>
			</div>

			<!-- Enhanced Stats -->
			<div class="grid grid-cols-2 md:grid-cols-4 gap-8 mb-16 max-w-5xl mx-auto">
				<div class="text-center group">
					<div class="relative">
						<div class="absolute inset-0 bg-gradient-to-r from-purple-500/20 to-pink-500/20 rounded-2xl blur-xl group-hover:blur-2xl transition-all duration-300"></div>
						<div class="relative bg-white/5 backdrop-blur-sm border border-white/10 rounded-2xl p-6 hover:bg-white/10 transition-colors">
							<div class="text-4xl font-bold text-primary-400 mb-2 font-display">50K+</div>
							<div class="text-neutral-400 font-medium">Premium Coupons</div>
							<div class="text-xs text-neutral-500 mt-1">Verified daily</div>
						</div>
					</div>
				</div>
				<div class="text-center group">
					<div class="relative">
						<div class="absolute inset-0 bg-gradient-to-r from-blue-500/20 to-cyan-500/20 rounded-2xl blur-xl group-hover:blur-2xl transition-all duration-300"></div>
						<div class="relative bg-white/5 backdrop-blur-sm border border-white/10 rounded-2xl p-6 hover:bg-white/10 transition-colors">
							<div class="text-4xl font-bold text-secondary-400 mb-2 font-display">1K+</div>
							<div class="text-neutral-400 font-medium">Elite Brands</div>
							<div class="text-xs text-neutral-500 mt-1">Premium partners</div>
						</div>
					</div>
				</div>
				<div class="text-center group">
					<div class="relative">
						<div class="absolute inset-0 bg-gradient-to-r from-green-500/20 to-emerald-500/20 rounded-2xl blur-xl group-hover:blur-2xl transition-all duration-300"></div>
						<div class="relative bg-white/5 backdrop-blur-sm border border-white/10 rounded-2xl p-6 hover:bg-white/10 transition-colors">
							<div class="text-4xl font-bold text-accent-400 mb-2 font-display">5M+</div>
							<div class="text-neutral-400 font-medium">Smart Savers</div>
							<div class="text-xs text-neutral-500 mt-1">Worldwide</div>
						</div>
					</div>
				</div>
				<div class="text-center group">
					<div class="relative">
						<div class="absolute inset-0 bg-gradient-to-r from-orange-500/20 to-red-500/20 rounded-2xl blur-xl group-hover:blur-2xl transition-all duration-300"></div>
						<div class="relative bg-white/5 backdrop-blur-sm border border-white/10 rounded-2xl p-6 hover:bg-white/10 transition-colors">
							<div class="text-4xl font-bold text-primary-400 mb-2 font-display">$500M+</div>
							<div class="text-neutral-400 font-medium">Total Saved</div>
							<div class="text-xs text-neutral-500 mt-1">This year</div>
						</div>
					</div>
				</div>
			</div>

			<!-- Enhanced CTA Buttons -->
			<div class="flex flex-col sm:flex-row items-center justify-center gap-6">
				<a href="/deals-premium" class="group relative overflow-hidden">
					<div class="absolute inset-0 bg-gradient-to-r from-purple-600 to-blue-600 rounded-2xl blur-xl group-hover:blur-2xl transition-all duration-300"></div>
					<div class="relative btn-primary text-white px-12 py-6 rounded-2xl font-semibold text-lg flex items-center space-x-3 hover:scale-105 transition-transform">
						<span>Explore Premium Deals</span>
						<svg class="w-5 h-5 group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6"></path>
						</svg>
					</div>
				</a>
				<a href="/brands-premium" class="group relative">
					<div class="btn-outline text-white px-12 py-6 rounded-2xl font-semibold text-lg border-2 border-white/20 hover:border-white/40 hover:bg-white/10 transition-all duration-300 flex items-center space-x-3">
						<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
						</svg>
						<span>Browse Brands</span>
					</div>
				</a>
			</div>

			<!-- Trust Badges -->
			<div class="mt-12 flex items-center justify-center space-x-8 opacity-60">
				<div class="flex items-center space-x-2">
					<svg class="w-5 h-5 text-green-400" fill="currentColor" viewBox="0 0 20 20">
						<path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
					</svg>
					<span class="text-sm text-neutral-400">100% Verified</span>
				</div>
				<div class="flex items-center space-x-2">
					<svg class="w-5 h-5 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
						<path fill-rule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clip-rule="evenodd"></path>
					</svg>
					<span class="text-sm text-neutral-400">Secure & Safe</span>
				</div>
				<div class="flex items-center space-x-2">
					<svg class="w-5 h-5 text-purple-400" fill="currentColor" viewBox="0 0 20 20">
						<path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
					</svg>
					<span class="text-sm text-neutral-400">Premium Quality</span>
				</div>
			</div>
		</div>
	</div>

	<!-- Floating Elements -->
	<div class="absolute top-20 left-10 hidden lg:block floating-element">
		<div class="w-20 h-20 glass-effect rounded-2xl flex items-center justify-center">
			<span class="text-3xl">💎</span>
		</div>
	</div>
	<div class="absolute top-32 right-20 hidden lg:block floating-element" style="animation-delay: -2s;">
		<div class="w-16 h-16 glass-effect rounded-2xl flex items-center justify-center">
			<span class="text-2xl">🚀</span>
		</div>
	</div>
	<div class="absolute bottom-20 left-20 hidden lg:block floating-element" style="animation-delay: -4s;">
		<div class="w-18 h-18 glass-effect rounded-2xl flex items-center justify-center">
			<span class="text-2xl">⭐</span>
		</div>
	</div>
	<div class="absolute bottom-32 right-10 hidden lg:block floating-element" style="animation-delay: -6s;">
		<div class="w-14 h-14 glass-effect rounded-2xl flex items-center justify-center">
			<span class="text-xl">✨</span>
		</div>
	</div>
</section>

<style>
	.floating-element {
		animation: float 6s ease-in-out infinite;
	}
	
	@keyframes float {
		0%, 100% { transform: translateY(0px); }
		50% { transform: translateY(-20px); }
	}
</style>
