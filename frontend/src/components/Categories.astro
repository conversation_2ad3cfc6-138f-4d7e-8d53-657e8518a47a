---
import { api } from '../lib/api';

// Fetch categories from API
let categories = [];
try {
	const response = await api.getCategories();
	if (response.data) {
		categories = response.data.map(category => ({
			id: category.id,
			name: category.name,
			description: category.description,
			slug: category.slug,
			icon: category.icon || "📦",
			color: category.color || "#6B7280",
			totalOffers: Math.floor(Math.random() * 1000) + 200, // Mock data for now
			popularBrands: ["Brand 1", "Brand 2", "Brand 3", "Brand 4"],
			subcategories: ["Sub 1", "Sub 2", "Sub 3", "Sub 4"]
		}));
	}
} catch (error) {
	console.error('Failed to fetch categories:', error);
	// Fallback data
	categories = [
	{
		id: 1,
		name: "Fashion & Clothing",
		description: "Clothing, shoes, accessories and fashion items",
		slug: "fashion-clothing",
		icon: "👗",
		color: "#E91E63",
		totalOffers: 1250,
		popularBrands: ["H&M", "Zara", "Nike", "Adidas"],
		subcategories: ["Women's Clothing", "Men's Clothing", "Shoes", "Accessories"]
	},
	{
		id: 2,
		name: "Electronics & Tech",
		description: "Computers, phones, gadgets and electronic devices",
		slug: "electronics-tech",
		icon: "📱",
		color: "#2196F3",
		totalOffers: 890,
		popularBrands: ["Apple", "Samsung", "Sony", "Microsoft"],
		subcategories: ["Smartphones", "Computers", "Gaming", "Audio"]
	},
	{
		id: 3,
		name: "Home & Garden",
		description: "Home improvement, furniture, garden and household items",
		slug: "home-garden",
		icon: "🏠",
		color: "#4CAF50",
		totalOffers: 675,
		popularBrands: ["IKEA", "Home Depot", "Wayfair", "Target"],
		subcategories: ["Furniture", "Kitchen", "Garden", "Decor"]
	},
	{
		id: 4,
		name: "Health & Beauty",
		description: "Cosmetics, skincare, health products and wellness",
		slug: "health-beauty",
		icon: "💄",
		color: "#FF5722",
		totalOffers: 543,
		popularBrands: ["Sephora", "Ulta", "CVS", "Walgreens"],
		subcategories: ["Skincare", "Makeup", "Health", "Hair Care"]
	},
	{
		id: 5,
		name: "Sports & Outdoors",
		description: "Sports equipment, outdoor gear and fitness products",
		slug: "sports-outdoors",
		icon: "⚽",
		color: "#FF9800",
		totalOffers: 432,
		popularBrands: ["Nike", "Adidas", "Under Armour", "REI"],
		subcategories: ["Athletic Wear", "Equipment", "Outdoor", "Fitness"]
	},
	{
		id: 6,
		name: "Travel & Leisure",
		description: "Hotels, flights, vacation packages and entertainment",
		slug: "travel-leisure",
		icon: "✈️",
		color: "#9C27B0",
		totalOffers: 321,
		popularBrands: ["Booking.com", "Expedia", "Airbnb", "Hotels.com"],
		subcategories: ["Hotels", "Flights", "Car Rental", "Activities"]
	},
	{
		id: 7,
		name: "Food & Dining",
		description: "Restaurants, food delivery, groceries and beverages",
		slug: "food-dining",
		icon: "🍕",
		color: "#795548",
		totalOffers: 298,
		popularBrands: ["DoorDash", "Uber Eats", "Grubhub", "Starbucks"],
		subcategories: ["Delivery", "Restaurants", "Groceries", "Coffee"]
	},
	{
		id: 8,
		name: "Books & Education",
		description: "Books, courses, educational materials and learning",
		slug: "books-education",
		icon: "📚",
		color: "#3F51B5",
		totalOffers: 187,
		popularBrands: ["Amazon", "Barnes & Noble", "Coursera", "Udemy"],
		subcategories: ["Books", "Courses", "Software", "Supplies"]
	}
];
}
---

<section class="py-16 bg-white">
	<div class="container mx-auto px-4">
		<!-- Section Header -->
		<div class="text-center mb-12">
			<h2 class="text-3xl md:text-4xl font-display font-bold text-gray-900 mb-4">
				🛍️ Shop by Category
			</h2>
			<p class="text-xl text-gray-600 max-w-2xl mx-auto">
				Find the best deals organized by your favorite shopping categories
			</p>
		</div>

		<!-- Categories Grid -->
		<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
			{categories.map((category) => (
				<a 
					href={`/categories/${category.slug}`}
					class="group block"
				>
					<div class="card card-hover overflow-hidden">
						<!-- Category Header -->
						<div 
							class="p-6 text-white relative overflow-hidden"
							style={`background: linear-gradient(135deg, ${category.color}, ${category.color}dd)`}
						>
							<!-- Background Pattern -->
							<div class="absolute inset-0 opacity-10">
								<div class="absolute inset-0 bg-[url('data:image/svg+xml,%3Csvg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="none" fill-rule="evenodd"%3E%3Cg fill="%23ffffff" fill-opacity="0.1"%3E%3Ccircle cx="30" cy="30" r="2"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E')]"></div>
							</div>
							
							<div class="relative">
								<div class="text-4xl mb-3">{category.icon}</div>
								<h3 class="text-xl font-semibold mb-2 group-hover:scale-105 transition-transform duration-200">
									{category.name}
								</h3>
								<p class="text-white/90 text-sm">
									{category.totalOffers} offers available
								</p>
							</div>
						</div>

						<!-- Category Content -->
						<div class="p-6">
							<p class="text-gray-600 text-sm mb-4">
								{category.description}
							</p>

							<!-- Popular Brands -->
							<div class="mb-4">
								<div class="text-xs font-semibold text-gray-500 uppercase tracking-wide mb-2">
									Popular Brands
								</div>
								<div class="flex flex-wrap gap-1">
									{category.popularBrands.slice(0, 3).map((brand) => (
										<span class="text-xs bg-gray-100 text-gray-700 px-2 py-1 rounded">
											{brand}
										</span>
									))}
									{category.popularBrands.length > 3 && (
										<span class="text-xs text-gray-500">
											+{category.popularBrands.length - 3} more
										</span>
									)}
								</div>
							</div>

							<!-- Subcategories -->
							<div>
								<div class="text-xs font-semibold text-gray-500 uppercase tracking-wide mb-2">
									Subcategories
								</div>
								<div class="space-y-1">
									{category.subcategories.slice(0, 3).map((subcategory) => (
										<div class="text-sm text-gray-600 hover:text-primary-600 transition-colors">
											{subcategory}
										</div>
									))}
									{category.subcategories.length > 3 && (
										<div class="text-sm text-gray-500">
											+{category.subcategories.length - 3} more
										</div>
									)}
								</div>
							</div>
						</div>
					</div>
				</a>
			))}
		</div>

		<!-- Trending Categories -->
		<div class="bg-gradient-to-r from-primary-50 to-secondary-50 rounded-2xl p-8 mb-12">
			<div class="text-center mb-8">
				<h3 class="text-2xl font-display font-bold text-gray-900 mb-2">
					🔥 Trending This Week
				</h3>
				<p class="text-gray-600">
					Categories with the most activity and best deals
				</p>
			</div>

			<div class="grid grid-cols-1 md:grid-cols-3 gap-6">
				{categories.slice(0, 3).map((category, index) => (
					<div class="bg-white rounded-xl p-6 text-center">
						<div class="text-3xl mb-3">{category.icon}</div>
						<h4 class="font-semibold text-gray-900 mb-2">{category.name}</h4>
						<div class="text-2xl font-bold text-primary-600 mb-1">
							{category.totalOffers}
						</div>
						<div class="text-sm text-gray-500 mb-4">active offers</div>
						<a 
							href={`/categories/${category.slug}`}
							class="inline-flex items-center text-primary-600 hover:text-primary-700 font-medium text-sm"
						>
							Explore Category
							<svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
							</svg>
						</a>
					</div>
				))}
			</div>
		</div>

		<!-- Quick Category Navigation -->
		<div class="bg-gray-50 rounded-2xl p-8">
			<h3 class="text-xl font-display font-bold text-gray-900 mb-6 text-center">
				Quick Category Access
			</h3>
			
			<div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-8 gap-4">
				{categories.map((category) => (
					<a 
						href={`/categories/${category.slug}`}
						class="group flex flex-col items-center p-4 rounded-xl hover:bg-white transition-all duration-200"
					>
						<div 
							class="w-12 h-12 rounded-xl flex items-center justify-center mb-2 group-hover:scale-110 transition-transform duration-200"
							style={`background-color: ${category.color}20`}
						>
							<span class="text-xl">{category.icon}</span>
						</div>
						<span class="text-sm font-medium text-gray-700 group-hover:text-primary-600 transition-colors text-center">
							{category.name.split(' ')[0]}
						</span>
					</a>
				))}
			</div>
		</div>

		<!-- View All Button -->
		<div class="text-center mt-12">
			<a 
				href="/categories" 
				class="inline-flex items-center px-8 py-4 bg-gray-100 hover:bg-gray-200 text-gray-900 font-semibold rounded-xl transition-colors duration-200"
			>
				View All Categories
				<svg class="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
					<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
				</svg>
			</a>
		</div>
	</div>
</section>

<script>
	document.addEventListener('DOMContentLoaded', () => {
		// Track category clicks
		const categoryLinks = document.querySelectorAll('[href^="/categories/"]');
		
		categoryLinks.forEach(link => {
			link.addEventListener('click', (e) => {
				const categoryName = link.querySelector('h3, h4')?.textContent?.trim();
				
				if (categoryName) {
					try {
						fetch('/api/analytics/category-click', {
							method: 'POST',
							headers: {
								'Content-Type': 'application/json',
							},
							body: JSON.stringify({
								category_name: categoryName,
								user_agent: navigator.userAgent,
								referrer: document.referrer,
								timestamp: new Date().toISOString()
							})
						});
					} catch (error) {
						console.error('Failed to track category click:', error);
					}
				}
			});
		});

		// Add intersection observer for animation
		const categoryCards = document.querySelectorAll('.card');
		
		const observer = new IntersectionObserver((entries) => {
			entries.forEach((entry) => {
				if (entry.isIntersecting) {
					entry.target.classList.add('animate-slide-up');
				}
			});
		}, {
			threshold: 0.1,
			rootMargin: '0px 0px -50px 0px'
		});

		categoryCards.forEach((card) => {
			observer.observe(card);
		});
	});
</script>
