---
import SearchBar from './search/SearchBar.astro';
---

<section class="relative bg-gradient-to-br from-primary-600 via-primary-700 to-secondary-600 overflow-hidden">
	<!-- Background Pattern -->
	<div class="absolute inset-0 bg-black/10"></div>
	<div class="absolute inset-0 bg-[url('data:image/svg+xml,%3Csvg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="none" fill-rule="evenodd"%3E%3Cg fill="%23ffffff" fill-opacity="0.05"%3E%3Ccircle cx="30" cy="30" r="2"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E')] opacity-30"></div>

	<div class="relative container mx-auto px-4 py-20 lg:py-32">
		<div class="max-w-4xl mx-auto text-center">
			<!-- Main Heading -->
			<h1 class="text-4xl md:text-5xl lg:text-6xl font-display font-bold text-white mb-6 leading-tight">
				Discover Amazing
				<span class="text-transparent bg-clip-text bg-gradient-to-r from-yellow-300 to-orange-300">
					Deals & Coupons
				</span>
			</h1>

			<!-- Subtitle -->
			<p class="text-xl md:text-2xl text-blue-100 mb-8 max-w-3xl mx-auto leading-relaxed">
				Save money on your favorite brands across Europe and America. 
				Get verified coupons, exclusive deals, and instant discounts.
			</p>

			<!-- Search Bar -->
			<div class="mb-12 max-w-2xl mx-auto">
				<SearchBar />
			</div>

			<!-- Stats -->
			<div class="grid grid-cols-2 md:grid-cols-4 gap-8 mb-12">
				<div class="text-center">
					<div class="text-3xl md:text-4xl font-bold text-white mb-2">10K+</div>
					<div class="text-blue-200 text-sm md:text-base">Active Coupons</div>
				</div>
				<div class="text-center">
					<div class="text-3xl md:text-4xl font-bold text-white mb-2">500+</div>
					<div class="text-blue-200 text-sm md:text-base">Top Brands</div>
				</div>
				<div class="text-center">
					<div class="text-3xl md:text-4xl font-bold text-white mb-2">2M+</div>
					<div class="text-blue-200 text-sm md:text-base">Happy Users</div>
				</div>
				<div class="text-center">
					<div class="text-3xl md:text-4xl font-bold text-white mb-2">$50M+</div>
					<div class="text-blue-200 text-sm md:text-base">Money Saved</div>
				</div>
			</div>

			<!-- Quick Actions -->
			<div class="flex flex-col sm:flex-row items-center justify-center gap-4">
				<a 
					href="/coupons" 
					class="inline-flex items-center px-8 py-4 bg-white text-primary-600 font-semibold rounded-xl hover:bg-gray-50 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-1"
				>
					<svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 5v2m0 4v2m0 4v2M5 5a2 2 0 00-2 2v3a1 1 0 001 1h1a1 1 0 001-1V7a2 2 0 012-2h8a2 2 0 012 2v1a1 1 0 001 1h1a1 1 0 001-1V7a2 2 0 00-2-2H5z"></path>
					</svg>
					Browse Coupons
				</a>
				<a 
					href="/deals" 
					class="inline-flex items-center px-8 py-4 bg-transparent border-2 border-white text-white font-semibold rounded-xl hover:bg-white hover:text-primary-600 transition-all duration-200 transform hover:-translate-y-1"
				>
					<svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"></path>
					</svg>
					Hot Deals
				</a>
			</div>
		</div>
	</div>

	<!-- Floating Elements -->
	<div class="absolute top-20 left-10 hidden lg:block animate-bounce-soft">
		<div class="w-16 h-16 bg-white/10 rounded-full flex items-center justify-center backdrop-blur-sm">
			<span class="text-2xl">💰</span>
		</div>
	</div>
	<div class="absolute top-32 right-20 hidden lg:block animate-bounce-soft" style="animation-delay: 0.5s;">
		<div class="w-12 h-12 bg-white/10 rounded-full flex items-center justify-center backdrop-blur-sm">
			<span class="text-xl">🎯</span>
		</div>
	</div>
	<div class="absolute bottom-20 left-20 hidden lg:block animate-bounce-soft" style="animation-delay: 1s;">
		<div class="w-14 h-14 bg-white/10 rounded-full flex items-center justify-center backdrop-blur-sm">
			<span class="text-xl">🛍️</span>
		</div>
	</div>
	<div class="absolute bottom-32 right-10 hidden lg:block animate-bounce-soft" style="animation-delay: 1.5s;">
		<div class="w-10 h-10 bg-white/10 rounded-full flex items-center justify-center backdrop-blur-sm">
			<span class="text-lg">✨</span>
		</div>
	</div>

	<!-- Wave Bottom -->
	<div class="absolute bottom-0 left-0 right-0">
		<svg class="w-full h-16 text-gray-50" fill="currentColor" viewBox="0 0 1200 120" preserveAspectRatio="none">
			<path d="M0,0V46.29c47.79,22.2,103.59,32.17,158,28,70.36-5.37,136.33-33.31,206.8-37.5C438.64,32.43,512.34,53.67,583,72.05c69.27,18,138.3,24.88,209.4,13.08,36.15-6,69.85-17.84,104.45-29.34C989.49,25,1113-14.29,1200,52.47V0Z" opacity=".25"></path>
			<path d="M0,0V15.81C13,36.92,27.64,56.86,47.69,72.05,99.41,111.27,165,111,224.58,91.58c31.15-10.15,60.09-26.07,89.67-39.8,40.92-19,84.73-46,130.83-49.67,36.26-2.85,70.9,9.42,98.6,31.56,31.77,25.39,62.32,62,103.63,73,40.44,10.79,81.35-6.69,119.13-24.28s75.16-39,116.92-43.05c59.73-5.85,113.28,22.88,168.9,38.84,30.2,8.66,59,6.17,87.09-7.5,22.43-10.89,48-26.93,60.65-49.24V0Z" opacity=".5"></path>
			<path d="M0,0V5.63C149.93,59,314.09,71.32,475.83,42.57c43-7.64,84.23-20.12,127.61-26.46,59-8.63,112.48,12.24,165.56,35.4C827.93,77.22,886,95.24,951.2,90c86.53-7,172.46-45.71,248.8-84.81V0Z"></path>
		</svg>
	</div>
</section>

<style>
	/* Custom animations for floating elements */
	@keyframes bounceSoft {
		0%, 20%, 53%, 80%, 100% {
			transform: translate3d(0,0,0);
		}
		40%, 43% {
			transform: translate3d(0, -8px, 0);
		}
		70% {
			transform: translate3d(0, -4px, 0);
		}
		90% {
			transform: translate3d(0, -2px, 0);
		}
	}

	.animate-bounce-soft {
		animation: bounceSoft 3s ease-in-out infinite;
	}
</style>
