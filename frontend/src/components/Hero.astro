---
import SearchBar from './search/SearchBar.astro';
---

<section class="relative bg-white overflow-hidden">
	<!-- Subtle Background Pattern -->
	<div class="absolute inset-0 bg-gradient-to-br from-purple-50 to-white"></div>
	<div class="absolute inset-0 bg-[url('data:image/svg+xml,%3Csvg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="none" fill-rule="evenodd"%3E%3Cg fill="%23a855f7" fill-opacity="0.03"%3E%3Ccircle cx="30" cy="30" r="2"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E')]"></div>

	<div class="relative container mx-auto px-4 py-16 lg:py-24">
		<div class="max-w-4xl mx-auto text-center">
			<!-- Main Heading -->
			<h1 class="text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 mb-6 leading-tight">
				Discover Amazing
				<span class="text-gradient">
					Deals & Coupons
				</span>
			</h1>

			<!-- Subtitle -->
			<p class="text-lg md:text-xl text-gray-600 mb-8 max-w-3xl mx-auto leading-relaxed">
				Save money on your favorite brands across Europe and America.
				Get verified coupons, exclusive deals, and instant discounts.
			</p>

			<!-- Search Bar -->
			<div class="mb-12 max-w-2xl mx-auto">
				<SearchBar />
			</div>

			<!-- Stats -->
			<div class="grid grid-cols-2 md:grid-cols-4 gap-8 mb-12">
				<div class="text-center">
					<div class="text-3xl md:text-4xl font-bold text-gray-900 mb-2">10K+</div>
					<div class="text-gray-600 text-sm md:text-base">Active Coupons</div>
				</div>
				<div class="text-center">
					<div class="text-3xl md:text-4xl font-bold text-gray-900 mb-2">500+</div>
					<div class="text-gray-600 text-sm md:text-base">Top Brands</div>
				</div>
				<div class="text-center">
					<div class="text-3xl md:text-4xl font-bold text-gray-900 mb-2">2M+</div>
					<div class="text-gray-600 text-sm md:text-base">Happy Users</div>
				</div>
				<div class="text-center">
					<div class="text-3xl md:text-4xl font-bold text-gray-900 mb-2">$50M+</div>
					<div class="text-gray-600 text-sm md:text-base">Money Saved</div>
				</div>
			</div>

			<!-- Quick Actions -->
			<div class="flex flex-col sm:flex-row items-center justify-center gap-4">
				<a
					href="/coupons"
					class="btn-primary inline-flex items-center"
				>
					<svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 5v2m0 4v2m0 4v2M5 5a2 2 0 00-2 2v3a1 1 0 001 1h1a1 1 0 001-1V7a2 2 0 012-2h8a2 2 0 012 2v1a1 1 0 001 1h1a1 1 0 001-1V7a2 2 0 00-2-2H5z"></path>
					</svg>
					Browse Coupons
				</a>
				<a
					href="/deals"
					class="btn-outline inline-flex items-center"
				>
					<svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"></path>
					</svg>
					Hot Deals
				</a>
			</div>
		</div>
	</div>

	<!-- Subtle Decorative Elements -->
	<div class="absolute top-20 left-10 hidden lg:block opacity-20">
		<div class="w-12 h-12 bg-purple-200 rounded-full flex items-center justify-center">
			<span class="text-lg">💰</span>
		</div>
	</div>
	<div class="absolute top-32 right-20 hidden lg:block opacity-20">
		<div class="w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center">
			<span class="text-sm">🎯</span>
		</div>
	</div>
	<div class="absolute bottom-20 left-20 hidden lg:block opacity-20">
		<div class="w-14 h-14 bg-purple-200 rounded-full flex items-center justify-center">
			<span class="text-lg">🛍️</span>
		</div>
	</div>
</section>

<style>
	/* Custom animations for floating elements */
	@keyframes bounceSoft {
		0%, 20%, 53%, 80%, 100% {
			transform: translate3d(0,0,0);
		}
		40%, 43% {
			transform: translate3d(0, -8px, 0);
		}
		70% {
			transform: translate3d(0, -4px, 0);
		}
		90% {
			transform: translate3d(0, -2px, 0);
		}
	}

	.animate-bounce-soft {
		animation: bounceSoft 3s ease-in-out infinite;
	}
</style>
