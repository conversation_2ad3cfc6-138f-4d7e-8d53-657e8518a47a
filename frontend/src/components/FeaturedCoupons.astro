---
import { api } from '../lib/api';

// Fetch featured coupons from API
let featuredCoupons = [];
try {
	const response = await api.getFeaturedCoupons(8);
	if (response.data) {
		featuredCoupons = response.data;
	}
} catch (error) {
	console.error('Failed to fetch featured coupons:', error);
	// Fallback data
	featuredCoupons = [
		{
			id: 1,
			title: "Amazon Prime Day Special - 20% Off Electronics",
			description: "Get 20% off on all electronics during Prime Day sale",
			code: "PRIME20",
			discount_value: 20,
			discount_type: "percentage",
			brand: {
				name: "Amazon",
				logo: "/images/brands/amazon.png"
			},
			category: "Electronics",
			expiry_date: "2024-12-31",
			is_exclusive: true,
			is_featured: true,
			click_count: 1250,
			success_rate: 95
		},
		{
			id: 2,
			title: "Nike Summer Sale - 25% Off Athletic Wear",
			description: "Get 25% off on all athletic wear and footwear",
			code: "SUMMER25",
			discount_value: 25,
			discount_type: "percentage",
			brand: {
				name: "<PERSON>",
				logo: "/images/brands/nike.png"
			},
			category: "Sports",
			expiry_date: "2024-12-25",
			is_exclusive: false,
			is_featured: true,
			click_count: 890,
			success_rate: 92
		}
	];
}

// Helper function to format expiry date
function formatExpiryDate(dateString: string) {
	const date = new Date(dateString);
	const now = new Date();
	const diffTime = date.getTime() - now.getTime();
	const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

	if (diffDays < 0) return "Expired";
	if (diffDays === 0) return "Expires today";
	if (diffDays === 1) return "Expires tomorrow";
	if (diffDays < 7) return `Expires in ${diffDays} days`;

	return date.toLocaleDateString('en-US', {
		month: 'short',
		day: 'numeric',
		year: date.getFullYear() !== now.getFullYear() ? 'numeric' : undefined
	});
}

// Helper function to format discount
function formatDiscount(coupon: any) {
	if (coupon.discount_type === 'percentage') {
		return `${coupon.discount_value}%`;
	} else if (coupon.discount_type === 'fixed') {
		return `$${coupon.discount_value}`;
	}
	return coupon.discount_text || `${coupon.discount_value}%`;
}
---

<section class="py-16 bg-white">
	<div class="container mx-auto px-4">
		<!-- Section Header -->
		<div class="text-center mb-12">
			<h2 class="text-3xl md:text-4xl font-display font-bold text-gray-900 mb-4">
				🔥 Featured Coupons
			</h2>
			<p class="text-xl text-gray-600 max-w-2xl mx-auto">
				Hand-picked exclusive deals and verified coupons from top brands
			</p>
		</div>

		<!-- Coupons Grid -->
		<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
			{featuredCoupons.map((coupon) => (
				<div class="card card-hover group">
					<!-- Coupon Header -->
					<div class="p-6">
						<!-- Brand and Exclusive Badge -->
						<div class="flex items-center justify-between mb-4">
							<div class="flex items-center space-x-3">
								<div class="w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center">
									<span class="text-lg font-bold text-gray-600">
										{coupon.brand?.name?.charAt(0) || 'C'}
									</span>
								</div>
								<div>
									<h3 class="font-semibold text-gray-900">{coupon.brand?.name || 'Brand'}</h3>
									<p class="text-sm text-gray-500">{coupon.category || 'General'}</p>
								</div>
							</div>
							{coupon.is_exclusive && (
								<span class="badge badge-accent text-xs">
									Exclusive
								</span>
							)}
						</div>

						<!-- Coupon Title -->
						<h4 class="text-lg font-semibold text-gray-900 mb-2 line-clamp-2 group-hover:text-primary-600 transition-colors">
							{coupon.title}
						</h4>

						<!-- Description -->
						<p class="text-gray-600 text-sm mb-4 line-clamp-2">
							{coupon.description}
						</p>

						<!-- Discount Badge -->
						<div class="flex items-center justify-between mb-4">
							<div class="bg-gradient-to-r from-primary-500 to-secondary-500 text-white px-4 py-2 rounded-lg font-bold text-lg">
								{formatDiscount(coupon)}
							</div>
							<div class="text-right">
								<div class="text-sm text-gray-500">Success Rate</div>
								<div class="text-lg font-semibold text-green-600">{coupon.success_rate || 0}%</div>
							</div>
						</div>

						<!-- Coupon Code -->
						<div class="bg-gray-50 border-2 border-dashed border-gray-300 rounded-lg p-3 mb-4">
							<div class="flex items-center justify-between">
								<div>
									<div class="text-xs text-gray-500 uppercase tracking-wide">Coupon Code</div>
									<div class="font-mono font-bold text-lg text-gray-900">{coupon.code}</div>
								</div>
								<button 
									class="copy-code-btn text-primary-600 hover:text-primary-700 transition-colors"
									data-code={coupon.code}
									title="Copy code"
								>
									<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
										<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
									</svg>
								</button>
							</div>
						</div>

						<!-- Expiry and Stats -->
						<div class="flex items-center justify-between text-sm text-gray-500 mb-4">
							<span>{formatExpiryDate(coupon.expiry_date)}</span>
							<span>{coupon.click_count || 0} used</span>
						</div>

						<!-- Action Button -->
						<button 
							class="w-full btn-primary coupon-btn"
							data-coupon-id={coupon.id}
							data-code={coupon.code}
						>
							Get Deal
						</button>
					</div>
				</div>
			))}
		</div>

		<!-- View All Button -->
		<div class="text-center">
			<a 
				href="/coupons" 
				class="inline-flex items-center px-8 py-4 bg-gray-100 hover:bg-gray-200 text-gray-900 font-semibold rounded-xl transition-colors duration-200"
			>
				View All Coupons
				<svg class="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
					<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
				</svg>
			</a>
		</div>
	</div>
</section>

<script>
	document.addEventListener('DOMContentLoaded', () => {
		// Copy code functionality
		const copyButtons = document.querySelectorAll('.copy-code-btn');
		
		copyButtons.forEach(button => {
			button.addEventListener('click', async (e) => {
				e.preventDefault();
				const code = button.getAttribute('data-code');
				
				if (!code) return;
				
				try {
					await navigator.clipboard.writeText(code);
					
					// Visual feedback
					const originalHTML = button.innerHTML;
					button.innerHTML = `
						<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
						</svg>
					`;
					button.classList.add('text-green-600');
					
					setTimeout(() => {
						button.innerHTML = originalHTML;
						button.classList.remove('text-green-600');
					}, 2000);
					
				} catch (err) {
					console.error('Failed to copy code:', err);
					// Fallback for older browsers
					const textArea = document.createElement('textarea');
					textArea.value = code;
					document.body.appendChild(textArea);
					textArea.select();
					document.execCommand('copy');
					document.body.removeChild(textArea);
				}
			});
		});

		// Coupon click tracking
		const couponButtons = document.querySelectorAll('.coupon-btn');
		
		couponButtons.forEach(button => {
			button.addEventListener('click', async (e) => {
				const couponId = button.getAttribute('data-coupon-id');
				const code = button.getAttribute('data-code');
				
				if (!couponId) return;
				
				// Track click
				try {
					await fetch(`http://localhost:8080/api/v1/coupons/${couponId}/click`, {
						method: 'POST',
						headers: {
							'Content-Type': 'application/json',
						},
						body: JSON.stringify({
							user_agent: navigator.userAgent,
							referrer: document.referrer,
							timestamp: new Date().toISOString()
						})
					});
				} catch (error) {
					console.error('Failed to track coupon click:', error);
				}
				
				// Copy code to clipboard
				if (code) {
					try {
						await navigator.clipboard.writeText(code);
					} catch (err) {
						console.error('Failed to copy code:', err);
					}
				}
				
				// Visual feedback
				const originalText = button.textContent;
				button.textContent = 'Code Copied!';
				button.classList.add('bg-green-600', 'hover:bg-green-700');
				
				setTimeout(() => {
					button.textContent = originalText;
					button.classList.remove('bg-green-600', 'hover:bg-green-700');
				}, 2000);
			});
		});
	});
</script>

<style>
	.line-clamp-2 {
		display: -webkit-box;
		-webkit-line-clamp: 2;
		-webkit-box-orient: vertical;
		overflow: hidden;
	}
</style>
