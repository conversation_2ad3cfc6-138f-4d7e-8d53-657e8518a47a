---
import { api } from '../lib/api';

// Fetch featured coupons from API
let featuredCoupons = [];
try {
	const response = await api.getFeaturedCoupons(8);
	if (response.data) {
		featuredCoupons = response.data;
	}
} catch (error) {
	console.error('Failed to fetch featured coupons:', error);
	// Fallback data
	featuredCoupons = [
		{
			id: 1,
			title: "Amazon Prime Day Special - 20% Off Electronics",
			description: "Get 20% off on all electronics during Prime Day sale",
			code: "PRIME20",
			discount_value: 20,
			discount_type: "percentage",
			brand: {
				name: "Amazon",
				logo: "/images/brands/amazon.png"
			},
			category: "Electronics",
			expiry_date: "2024-12-31",
			is_exclusive: true,
			is_featured: true,
			click_count: 1250,
			success_rate: 95
		},
		{
			id: 2,
			title: "Nike Summer Sale - 25% Off Athletic Wear",
			description: "Get 25% off on all athletic wear and footwear",
			code: "SUMMER25",
			discount_value: 25,
			discount_type: "percentage",
			brand: {
				name: "<PERSON>",
				logo: "/images/brands/nike.png"
			},
			category: "Sports",
			expiry_date: "2024-12-25",
			is_exclusive: false,
			is_featured: true,
			click_count: 890,
			success_rate: 92
		}
	];
}

// Helper function to format expiry date
function formatExpiryDate(dateString: string) {
	const date = new Date(dateString);
	const now = new Date();
	const diffTime = date.getTime() - now.getTime();
	const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

	if (diffDays < 0) return "Expired";
	if (diffDays === 0) return "Expires today";
	if (diffDays === 1) return "Expires tomorrow";
	if (diffDays < 7) return `Expires in ${diffDays} days`;

	return date.toLocaleDateString('en-US', {
		month: 'short',
		day: 'numeric',
		year: date.getFullYear() !== now.getFullYear() ? 'numeric' : undefined
	});
}

// Helper function to format discount
function formatDiscount(coupon: any) {
	if (coupon.discount_type === 'percentage') {
		return `${coupon.discount_value}%`;
	} else if (coupon.discount_type === 'fixed') {
		return `$${coupon.discount_value}`;
	}
	return coupon.discount_text || `${coupon.discount_value}%`;
}
---

<section class="py-16 bg-white">
	<div class="container mx-auto px-4">
		<!-- Section Header -->
		<div class="text-center mb-12">
			<h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
				Featured Coupons
			</h2>
			<p class="text-lg text-gray-600 max-w-2xl mx-auto">
				Hand-picked exclusive deals and verified coupons from top brands
			</p>
		</div>

		<!-- Coupons Grid -->
		<div class="space-y-4 mb-12">
			{featuredCoupons.map((coupon) => (
				<div class="bg-white rounded-xl border border-gray-200 hover:border-primary-300 hover:shadow-lg transition-all duration-300 overflow-hidden">
					<div class="flex">
						<!-- Left side - Discount Badge -->
						<div class="flex-shrink-0 w-32 bg-gradient-to-br from-primary-500 to-secondary-500 flex flex-col items-center justify-center text-white relative">
							<div class="text-center">
								<div class="text-2xl font-bold leading-tight">
									{coupon.discount_type === 'percentage' ? `${coupon.discount_value}%` : `$${coupon.discount_value}`}
								</div>
								<div class="text-sm font-medium">
									OFF
								</div>
							</div>
							{coupon.is_exclusive && (
								<div class="absolute top-2 right-2">
									<span class="bg-orange-500 text-white px-2 py-1 rounded text-xs font-semibold">
										VIP
									</span>
								</div>
							)}
							<!-- Decorative notch -->
							<div class="absolute right-0 top-1/2 transform translate-x-1/2 -translate-y-1/2 w-4 h-4 bg-gray-50 rounded-full border-2 border-gray-200"></div>
						</div>

						<!-- Right side - Content -->
						<div class="flex-1 p-6">
							<div class="flex items-start justify-between">
								<div class="flex-1">
									<div class="flex items-center space-x-2 mb-2">
										<div class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
											<span class="text-base font-bold text-purple-600">
												{coupon.brand?.name?.charAt(0) || 'C'}
											</span>
										</div>
										<span class="text-sm font-medium text-gray-600">{coupon.brand?.name || 'Brand'}</span>
										{coupon.is_verified && (
											<span class="text-xs bg-green-100 text-green-700 px-2 py-1 rounded-full font-medium">
												VERIFIED
											</span>
										)}
									</div>

									<h4 class="text-xl font-semibold text-gray-900 mb-2 line-clamp-2">
										{coupon.title}
									</h4>

									<p class="text-gray-600 mb-4 line-clamp-2">
										{coupon.description}
									</p>

									<div class="flex items-center space-x-4 text-sm text-gray-500 mb-4">
										<div class="flex items-center space-x-1">
											<svg class="w-4 h-4 text-green-500" fill="currentColor" viewBox="0 0 20 20">
												<path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
											</svg>
											<span>{coupon.success_rate || 0}% success</span>
										</div>
										<div class="flex items-center space-x-1">
											<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
												<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
												<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
											</svg>
											<span>{coupon.click_count || 0} used today</span>
										</div>
										{coupon.expiry_date && (
											<div class="flex items-center space-x-1">
												<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
													<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
												</svg>
												<span>Expires {formatExpiryDate(coupon.expiry_date)}</span>
											</div>
										)}
									</div>
								</div>

								<!-- Code and Button -->
								<div class="flex flex-col items-end space-y-3">
									<div class="bg-gray-100 border-2 border-dashed border-gray-300 rounded-lg px-4 py-2 text-center min-w-[120px]">
										<div class="text-xs text-gray-500 mb-1">CODE</div>
										<div class="text-sm font-mono font-bold text-gray-900 tracking-wider">
											{coupon.code ? coupon.code.substring(0, 4) + '***' : 'GET***'}
										</div>
									</div>
									<button
										class="bg-primary-600 hover:bg-primary-700 text-white px-6 py-2 rounded-lg font-semibold text-sm transition-colors duration-200 coupon-btn"
										data-coupon-id={coupon.id}
										data-code={coupon.code}
										data-title={coupon.title}
										data-brand={coupon.brand?.name || 'Brand'}
									>
										Get Code
									</button>
								</div>
							</div>
						</div>
					</div>
				</div>
			))}
		</div>

		<!-- View All Button -->
		<div class="text-center">
			<a 
				href="/coupons" 
				class="inline-flex items-center px-8 py-4 bg-gray-100 hover:bg-gray-200 text-gray-900 font-semibold rounded-xl transition-colors duration-200"
			>
				View All Coupons
				<svg class="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
					<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
				</svg>
			</a>
		</div>
	</div>
</section>

<script>
	document.addEventListener('DOMContentLoaded', () => {
		// Coupon modal functionality
		const couponButtons = document.querySelectorAll('.coupon-btn');

		couponButtons.forEach(button => {
			button.addEventListener('click', async (e) => {
				e.preventDefault();

				const couponId = button.getAttribute('data-coupon-id');
				const code = button.getAttribute('data-code');
				const title = button.getAttribute('data-title');
				const brand = button.getAttribute('data-brand');

				// Show modal
				if (typeof showCouponModal === 'function') {
					showCouponModal(couponId, code, title, brand, '#', '/images/placeholder-brand.svg');
				}

				// Track click
				try {
					await fetch(`http://localhost:8080/api/v1/coupons/${couponId}/click`, {
						method: 'POST',
						headers: {
							'Content-Type': 'application/json',
						},
						body: JSON.stringify({
							user_agent: navigator.userAgent,
							referrer: document.referrer,
							timestamp: new Date().toISOString()
						})
					});
				} catch (error) {
					console.error('Failed to track coupon click:', error);
				}
			});
		});
	});
</script>

<style>
	.line-clamp-2 {
		display: -webkit-box;
		-webkit-line-clamp: 2;
		-webkit-box-orient: vertical;
		overflow: hidden;
	}
</style>
