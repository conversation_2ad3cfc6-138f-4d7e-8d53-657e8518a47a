<!-- Coupon Modal -->
<div id="coupon-modal" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden items-center justify-center p-4">
	<div class="bg-white rounded-2xl max-w-md w-full mx-auto transform transition-all duration-300 scale-95 opacity-0" id="modal-content">
		<div class="p-6">
			<!-- Header -->
			<div class="flex items-center justify-between mb-6">
				<div class="flex items-center space-x-3">
					<div class="w-12 h-12 bg-gradient-to-br from-primary-500 to-secondary-500 rounded-xl flex items-center justify-center">
						<svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 5v2m0 4v2m0 4v2M5 5a2 2 0 00-2 2v3a1 1 0 001 1h1a1 1 0 001-1V7a2 2 0 012-2h8a2 2 0 012 2v1a1 1 0 001 1h1a1 1 0 001-1V7a2 2 0 00-2-2H5z"></path>
						</svg>
					</div>
					<div>
						<h3 class="text-lg font-semibold text-gray-900" id="modal-title">Coupon Code</h3>
						<p class="text-sm text-gray-500" id="modal-brand">Brand Name</p>
					</div>
				</div>
				<button onclick="closeCouponModal()" class="text-gray-400 hover:text-gray-600 transition-colors">
					<svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
					</svg>
				</button>
			</div>

			<!-- Coupon Code Display -->
			<div class="bg-gradient-to-r from-primary-50 to-secondary-50 border-2 border-dashed border-primary-300 rounded-xl p-6 text-center mb-6">
				<div class="text-sm text-gray-600 mb-2">YOUR COUPON CODE</div>
				<div class="text-3xl font-mono font-bold text-primary-600 tracking-wider mb-4" id="modal-code">
					LOADING...
				</div>
				<button 
					onclick="copyCouponCode()" 
					class="bg-primary-600 hover:bg-primary-700 text-white px-6 py-2 rounded-lg font-semibold transition-colors duration-200 flex items-center space-x-2 mx-auto"
				>
					<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
					</svg>
					<span>Copy Code</span>
				</button>
			</div>

			<!-- Instructions -->
			<div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
				<div class="flex items-start space-x-3">
					<svg class="w-5 h-5 text-blue-500 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
					</svg>
					<div>
						<h4 class="text-sm font-semibold text-blue-900 mb-1">How to use this coupon:</h4>
						<ol class="text-sm text-blue-800 space-y-1">
							<li>1. Copy the coupon code above</li>
							<li>2. Click "Visit Store" to go to the website</li>
							<li>3. Add items to your cart</li>
							<li>4. Paste the code at checkout</li>
						</ol>
					</div>
				</div>
			</div>

			<!-- Action Buttons -->
			<div class="flex space-x-3">
				<button 
					onclick="visitStore()" 
					class="flex-1 bg-green-600 hover:bg-green-700 text-white py-3 rounded-lg font-semibold transition-colors duration-200 flex items-center justify-center space-x-2"
				>
					<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path>
					</svg>
					<span>Visit Store</span>
				</button>
				<button 
					onclick="closeCouponModal()" 
					class="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg font-semibold hover:bg-gray-50 transition-colors duration-200"
				>
					Close
				</button>
			</div>

			<!-- Success Message -->
			<div id="copy-success" class="hidden mt-4 p-3 bg-green-100 border border-green-300 rounded-lg">
				<div class="flex items-center space-x-2">
					<svg class="w-4 h-4 text-green-600" fill="currentColor" viewBox="0 0 20 20">
						<path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
					</svg>
					<span class="text-sm font-medium text-green-800">Coupon code copied to clipboard!</span>
				</div>
			</div>
		</div>
	</div>
</div>

<script>
	// Global variables for modal
	let currentCouponData = {};

	// Show coupon modal
	window.showCouponModal = function(couponId, code, title, brand, storeUrl) {
		currentCouponData = { couponId, code, title, brand, storeUrl };
		
		const modal = document.getElementById('coupon-modal');
		const modalContent = document.getElementById('modal-content');
		const modalTitle = document.getElementById('modal-title');
		const modalBrand = document.getElementById('modal-brand');
		const modalCode = document.getElementById('modal-code');
		
		// Set content
		modalTitle.textContent = title;
		modalBrand.textContent = brand;
		modalCode.textContent = code || 'NO CODE NEEDED';
		
		// Show modal with animation
		modal.classList.remove('hidden');
		modal.classList.add('flex');
		
		// Trigger animation
		setTimeout(() => {
			modalContent.classList.remove('scale-95', 'opacity-0');
			modalContent.classList.add('scale-100', 'opacity-100');
		}, 10);
		
		// Track coupon view (you can add analytics here)
		console.log('Coupon viewed:', couponId);
	};

	// Close coupon modal
	window.closeCouponModal = function() {
		const modal = document.getElementById('coupon-modal');
		const modalContent = document.getElementById('modal-content');
		const copySuccess = document.getElementById('copy-success');
		
		// Hide success message
		copySuccess.classList.add('hidden');
		
		// Animate out
		modalContent.classList.remove('scale-100', 'opacity-100');
		modalContent.classList.add('scale-95', 'opacity-0');
		
		setTimeout(() => {
			modal.classList.add('hidden');
			modal.classList.remove('flex');
		}, 300);
	};

	// Copy coupon code
	window.copyCouponCode = function() {
		const code = currentCouponData.code;
		const copySuccess = document.getElementById('copy-success');
		
		if (code && code !== 'NO CODE NEEDED') {
			navigator.clipboard.writeText(code).then(() => {
				// Show success message
				copySuccess.classList.remove('hidden');
				setTimeout(() => {
					copySuccess.classList.add('hidden');
				}, 3000);
			}).catch(() => {
				// Fallback for older browsers
				const textArea = document.createElement('textarea');
				textArea.value = code;
				document.body.appendChild(textArea);
				textArea.select();
				document.execCommand('copy');
				document.body.removeChild(textArea);
				
				copySuccess.classList.remove('hidden');
				setTimeout(() => {
					copySuccess.classList.add('hidden');
				}, 3000);
			});
		}
	};

	// Visit store
	window.visitStore = function() {
		const storeUrl = currentCouponData.storeUrl;
		if (storeUrl && storeUrl !== '#') {
			window.open(storeUrl, '_blank');
		} else {
			// Fallback - you could redirect to a search or brand page
			console.log('No store URL available');
		}
		
		// Track coupon click (you can add analytics here)
		console.log('Coupon clicked:', currentCouponData.couponId);
	};

	// Close modal when clicking outside
	document.addEventListener('click', function(event) {
		const modal = document.getElementById('coupon-modal');
		if (event.target === modal) {
			closeCouponModal();
		}
	});

	// Close modal with Escape key
	document.addEventListener('keydown', function(event) {
		if (event.key === 'Escape') {
			closeCouponModal();
		}
	});
</script>
