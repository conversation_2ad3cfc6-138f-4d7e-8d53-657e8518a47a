<!-- Choice Modal for Coupon/Deal -->
<div id="choice-modal" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden items-center justify-center p-4">
	<div class="bg-white rounded-2xl max-w-lg w-full mx-auto transform transition-all duration-300 scale-95 opacity-0" id="modal-content">
		<div class="p-6">
			<!-- Header -->
			<div class="flex items-center justify-between mb-6">
				<div class="flex items-center space-x-3">
					<div class="w-12 h-12 bg-gradient-to-br from-primary-500 to-secondary-500 rounded-xl flex items-center justify-center">
						<svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 5v2m0 4v2m0 4v2M5 5a2 2 0 00-2 2v3a1 1 0 001 1h1a1 1 0 001-1V7a2 2 0 012-2h8a2 2 0 012 2v1a1 1 0 001 1h1a1 1 0 001-1V7a2 2 0 00-2-2H5z"></path>
						</svg>
					</div>
					<div>
						<h3 class="text-lg font-semibold text-gray-900" id="modal-title">Get Your Coupon</h3>
						<p class="text-sm text-gray-500" id="modal-brand">Choose an option to continue</p>
					</div>
				</div>
				<button onclick="closeChoiceModal()" class="text-gray-400 hover:text-gray-600 transition-colors">
					<svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
					</svg>
				</button>
			</div>

			<!-- Choice Instructions -->
			<div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
				<div class="flex items-start space-x-3">
					<svg class="w-5 h-5 text-blue-500 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
					</svg>
					<div>
						<h4 class="text-sm font-semibold text-blue-900 mb-1">Please choose one option to continue:</h4>
						<p class="text-sm text-blue-800">You must select either the affiliate link or view our sponsor ad to access your coupon code.</p>
					</div>
				</div>
			</div>

			<!-- Choice Options -->
			<div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
				<!-- Option 1: Affiliate Link (Brand Logo) -->
				<div class="border-2 border-gray-200 rounded-xl p-6 hover:border-primary-300 transition-colors cursor-pointer text-center" onclick="selectAffiliateLink()">
					<div class="mb-4">
						<img
							id="brand-logo"
							src="/images/placeholder-brand.svg"
							alt="Brand Logo"
							class="w-24 h-24 mx-auto rounded-lg object-contain bg-gray-50 p-3"
						/>
					</div>
					<h4 class="text-lg font-semibold text-gray-900 mb-2">Visit Store</h4>
					<p class="text-sm text-gray-600 mb-3">Go directly to the store and get your coupon code instantly.</p>
					<span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
						Instant Access
					</span>
				</div>

				<!-- Option 2: Live Advertisement -->
				<div class="border-2 border-gray-200 rounded-xl p-4 hover:border-primary-300 transition-colors cursor-pointer" onclick="selectViewAd()">
					<div class="text-center mb-3">
						<h4 class="text-lg font-semibold text-gray-900 mb-1">View Sponsor</h4>
						<p class="text-xs text-gray-500">Support our site by viewing our sponsor</p>
					</div>
					<!-- Live Ad Content -->
					<div id="live-ad-content" class="min-h-[120px] flex items-center justify-center bg-gray-50 rounded-lg">
						<div class="text-center">
							<div class="text-sm text-gray-500 mb-1">Advertisement</div>
							<div class="text-xs text-gray-400">Loading...</div>
						</div>
					</div>
					<div class="text-center mt-3">
						<span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
							Support Us
						</span>
					</div>
				</div>
			</div>

			<!-- Loading State -->
			<div id="loading-state" class="hidden text-center py-8">
				<div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
				<p class="mt-2 text-sm text-gray-600">Loading...</p>
			</div>
		</div>
	</div>
</div>



<!-- Final Coupon Modal -->
<div id="final-coupon-modal" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden items-center justify-center p-4">
	<div class="bg-white rounded-2xl max-w-md w-full mx-auto transform transition-all duration-300 scale-95 opacity-0" id="final-modal-content">
		<div class="p-6">
			<!-- Header -->
			<div class="flex items-center justify-between mb-6">
				<div class="flex items-center space-x-3">
					<div class="w-12 h-12 bg-gradient-to-br from-primary-500 to-secondary-500 rounded-xl flex items-center justify-center">
						<svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
						</svg>
					</div>
					<div>
						<h3 class="text-lg font-semibold text-gray-900" id="final-modal-title">Your Coupon Code</h3>
						<p class="text-sm text-gray-500" id="final-modal-brand">Brand Name</p>
					</div>
				</div>
				<button onclick="closeFinalModal()" class="text-gray-400 hover:text-gray-600 transition-colors">
					<svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
					</svg>
				</button>
			</div>

			<!-- Coupon Code Display -->
			<div class="bg-gradient-to-r from-primary-50 to-secondary-50 border-2 border-dashed border-primary-300 rounded-xl p-6 text-center mb-6">
				<div class="text-sm text-gray-600 mb-2">YOUR COUPON CODE</div>
				<div class="text-3xl font-mono font-bold text-primary-600 tracking-wider mb-4" id="final-modal-code">
					LOADING...
				</div>
				<button
					onclick="copyCouponCode()"
					class="bg-primary-600 hover:bg-primary-700 text-white px-6 py-2 rounded-lg font-semibold transition-colors duration-200 flex items-center space-x-2 mx-auto"
				>
					<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
					</svg>
					<span>Copy Code</span>
				</button>
			</div>

			<!-- Instructions -->
			<div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
				<div class="flex items-start space-x-3">
					<svg class="w-5 h-5 text-blue-500 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
					</svg>
					<div>
						<h4 class="text-sm font-semibold text-blue-900 mb-1">How to use this coupon:</h4>
						<ol class="text-sm text-blue-800 space-y-1">
							<li>1. Copy the coupon code above</li>
							<li>2. Click "Visit Store" to go to the website</li>
							<li>3. Add items to your cart</li>
							<li>4. Paste the code at checkout</li>
						</ol>
					</div>
				</div>
			</div>

			<!-- Action Buttons -->
			<div class="flex space-x-3">
				<button
					onclick="visitStore()"
					class="flex-1 bg-green-600 hover:bg-green-700 text-white py-3 rounded-lg font-semibold transition-colors duration-200 flex items-center justify-center space-x-2"
				>
					<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path>
					</svg>
					<span>Visit Store</span>
				</button>
				<button
					onclick="closeFinalModal()"
					class="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg font-semibold hover:bg-gray-50 transition-colors duration-200"
				>
					Close
				</button>
			</div>

			<!-- Success Message -->
			<div id="copy-success" class="hidden mt-4 p-3 bg-green-100 border border-green-300 rounded-lg">
				<div class="flex items-center space-x-2">
					<svg class="w-4 h-4 text-green-600" fill="currentColor" viewBox="0 0 20 20">
						<path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
					</svg>
					<span class="text-sm font-medium text-green-800">Coupon code copied to clipboard!</span>
				</div>
			</div>
		</div>
	</div>
</div>

<script>
	// Global variables for modal
	let currentCouponData = {};
	let currentAdData = {};

	// Show choice modal (entry point)
	window.showCouponModal = function(couponId, code, title, brand, storeUrl, brandLogo) {
		currentCouponData = { couponId, code, title, brand, storeUrl, brandLogo };

		const modal = document.getElementById('choice-modal');
		const modalContent = document.getElementById('modal-content');
		const modalTitle = document.getElementById('modal-title');
		const modalBrand = document.getElementById('modal-brand');
		const brandLogoImg = document.getElementById('brand-logo');

		// Set content
		modalTitle.textContent = `Get "${title}"`;
		modalBrand.textContent = `From ${brand}`;

		// Set brand logo
		if (brandLogo && brandLogo !== '#') {
			brandLogoImg.src = brandLogo;
			brandLogoImg.alt = `${brand} Logo`;
		} else {
			brandLogoImg.src = '/images/placeholder-brand.svg';
			brandLogoImg.alt = 'Brand Logo';
		}

		// Load and display live ad
		loadLiveAd();

		// Show modal with animation
		modal.classList.remove('hidden');
		modal.classList.add('flex');

		// Trigger animation
		setTimeout(() => {
			modalContent.classList.remove('scale-95', 'opacity-0');
			modalContent.classList.add('scale-100', 'opacity-100');
		}, 10);
	};

	// Close choice modal
	window.closeChoiceModal = function() {
		const modal = document.getElementById('choice-modal');
		const modalContent = document.getElementById('modal-content');

		// Animate out
		modalContent.classList.remove('scale-100', 'opacity-100');
		modalContent.classList.add('scale-95', 'opacity-0');

		setTimeout(() => {
			modal.classList.add('hidden');
			modal.classList.remove('flex');
		}, 300);
	};

	// Option 1: Select affiliate link (direct to store)
	window.selectAffiliateLink = function() {
		closeChoiceModal();

		// Show final coupon modal directly
		setTimeout(() => {
			showFinalCouponModal();
		}, 300);
	};

	// Option 2: Select view ad (ad already displayed, just record click and show coupon)
	window.selectViewAd = function() {
		// Record ad click since user interacted with the ad option
		recordAdClick();

		closeChoiceModal();

		// Show final coupon modal directly
		setTimeout(() => {
			showFinalCouponModal();
		}, 300);
	};

	// Load and display live ad in choice modal
	async function loadLiveAd() {
		const liveAdContainer = document.getElementById('live-ad-content');

		try {
			const response = await fetch(`http://127.0.0.1:8080/api/v1/ads/modal?entity_type=coupon&entity_id=${currentCouponData.couponId}`);
			const result = await response.json();

			if (result.success && result.data) {
				currentAdData = result.data;

				// Display the actual ad code directly
				liveAdContainer.innerHTML = result.data.ad_code;

				// Record ad show
				recordAdShow();

				console.log('Live ad loaded and displayed:', currentAdData.name);
			} else {
				console.error('Failed to load ad:', result.message);
				liveAdContainer.innerHTML = `
					<div class="text-center py-8">
						<div class="text-sm text-gray-500 mb-1">Advertisement</div>
						<div class="text-xs text-gray-400">No ads available</div>
					</div>
				`;
			}
		} catch (error) {
			console.error('Error loading live ad:', error);
			liveAdContainer.innerHTML = `
				<div class="text-center py-8">
					<div class="text-sm text-gray-500 mb-1">Advertisement</div>
					<div class="text-xs text-gray-400">Failed to load ad</div>
				</div>
			`;
		}
	}



	// Show final coupon modal
	function showFinalCouponModal() {
		const modal = document.getElementById('final-coupon-modal');
		const modalContent = document.getElementById('final-modal-content');
		const modalTitle = document.getElementById('final-modal-title');
		const modalBrand = document.getElementById('final-modal-brand');
		const modalCode = document.getElementById('final-modal-code');

		// Set content
		modalTitle.textContent = currentCouponData.title;
		modalBrand.textContent = currentCouponData.brand;
		modalCode.textContent = currentCouponData.code || 'NO CODE NEEDED';

		// Show modal with animation
		modal.classList.remove('hidden');
		modal.classList.add('flex');

		// Trigger animation
		setTimeout(() => {
			modalContent.classList.remove('scale-95', 'opacity-0');
			modalContent.classList.add('scale-100', 'opacity-100');
		}, 10);
	}

	// Close final modal
	window.closeFinalModal = function() {
		const modal = document.getElementById('final-coupon-modal');
		const modalContent = document.getElementById('final-modal-content');
		const copySuccess = document.getElementById('copy-success');

		// Hide success message
		copySuccess.classList.add('hidden');

		// Animate out
		modalContent.classList.remove('scale-100', 'opacity-100');
		modalContent.classList.add('scale-95', 'opacity-0');

		setTimeout(() => {
			modal.classList.add('hidden');
			modal.classList.remove('flex');
		}, 300);
	};

	// Copy coupon code
	window.copyCouponCode = function() {
		const code = currentCouponData.code;
		const copySuccess = document.getElementById('copy-success');

		if (code && code !== 'NO CODE NEEDED') {
			navigator.clipboard.writeText(code).then(() => {
				// Show success message
				copySuccess.classList.remove('hidden');
				setTimeout(() => {
					copySuccess.classList.add('hidden');
				}, 3000);
			}).catch(() => {
				// Fallback for older browsers
				const textArea = document.createElement('textarea');
				textArea.value = code;
				document.body.appendChild(textArea);
				textArea.select();
				document.execCommand('copy');
				document.body.removeChild(textArea);

				copySuccess.classList.remove('hidden');
				setTimeout(() => {
					copySuccess.classList.add('hidden');
				}, 3000);
			});
		}
	};

	// Visit store
	window.visitStore = function() {
		const storeUrl = currentCouponData.storeUrl;
		if (storeUrl && storeUrl !== '#') {
			window.open(storeUrl, '_blank');
		} else {
			// Fallback - you could redirect to a search or brand page
			console.log('No store URL available');
		}

		// Track coupon click (you can add analytics here)
		console.log('Coupon clicked:', currentCouponData.couponId);
	};

	// Record ad show (display count +1)
	async function recordAdShow() {
		if (!currentAdData.id) return;

		try {
			await fetch('http://127.0.0.1:8080/api/v1/ads/click', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
				},
				body: JSON.stringify({
					ad_code_id: currentAdData.id,
					entity_type: 'coupon',
					entity_id: parseInt(currentCouponData.couponId),
					action: 'show'
				})
			});
			console.log('Ad show recorded for ad:', currentAdData.name);
		} catch (error) {
			console.error('Failed to record ad show:', error);
		}
	}

	// Record ad click (click count +1)
	async function recordAdClick() {
		if (!currentAdData.id) return;

		try {
			await fetch('http://127.0.0.1:8080/api/v1/ads/click', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
				},
				body: JSON.stringify({
					ad_code_id: currentAdData.id,
					entity_type: 'coupon',
					entity_id: parseInt(currentCouponData.couponId),
					action: 'click'
				})
			});
			console.log('Ad click recorded for ad:', currentAdData.name);
		} catch (error) {
			console.error('Failed to record ad click:', error);
		}
	}

	// Disable closing modals by clicking outside or pressing Escape
	// Users must use the close button to close modals
	document.addEventListener('click', function(event) {
		// Prevent any modal from closing by clicking outside
		// All modals must be closed using the close button
	});

	// Disable Escape key for closing modals
	document.addEventListener('keydown', function(event) {
		if (event.key === 'Escape') {
			// Prevent closing modals with Escape key
			// Users must click the close button
			event.preventDefault();
		}
	});
</script>
