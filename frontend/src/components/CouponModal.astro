<!-- Choice Modal for Coupon/Deal -->
<div id="choice-modal" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden items-center justify-center p-4">
	<div class="bg-white rounded-2xl max-w-4xl w-full mx-auto transform transition-all duration-300 scale-95 opacity-0" id="modal-content">
		<div class="p-6">
			<!-- Header -->
			<div class="flex items-center justify-between mb-6">
				<div class="flex items-center space-x-3">
					<div class="w-12 h-12 bg-gradient-to-br from-primary-500 to-secondary-500 rounded-xl flex items-center justify-center">
						<svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 5v2m0 4v2m0 4v2M5 5a2 2 0 00-2 2v3a1 1 0 001 1h1a1 1 0 001-1V7a2 2 0 012-2h8a2 2 0 012 2v1a1 1 0 001 1h1a1 1 0 001-1V7a2 2 0 00-2-2H5z"></path>
						</svg>
					</div>
					<div>
						<h3 class="text-lg font-semibold text-gray-900" id="modal-title">Get Your Coupon</h3>
						<p class="text-sm text-gray-500" id="modal-brand">Choose an option to continue</p>
					</div>
				</div>
				<button onclick="closeChoiceModal()" class="text-gray-400 hover:text-gray-600 transition-colors">
					<svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
					</svg>
				</button>
			</div>

			<!-- Choice Options -->
			<div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
				<!-- Option 1: Complete Coupon Info -->
				<div class="border-2 border-gray-200 rounded-xl p-6 hover:border-primary-300 hover:shadow-lg transition-all cursor-pointer" id="affiliate-option">
					<div class="flex items-center space-x-3 mb-4">
						<img
							id="brand-logo"
							src="/images/placeholder-brand.svg"
							alt="Brand Logo"
							class="w-16 h-16 rounded-lg object-contain bg-gray-50 p-2"
						/>
						<div class="flex-1">
							<div class="text-lg font-semibold text-gray-900" id="brand-name">Brand Name</div>
							<div class="text-sm text-gray-500" id="discount-info">Up to 50% Off</div>
						</div>
					</div>

					<!-- Coupon Code Display -->
					<div class="bg-gradient-to-r from-primary-50 to-secondary-50 border-2 border-dashed border-primary-300 rounded-xl p-6 text-center mb-4">
						<div class="text-sm text-gray-600 mb-2">YOUR COUPON CODE</div>
						<div class="text-3xl font-mono font-bold text-primary-600 tracking-wider mb-4" id="full-code">
							LOADING...
						</div>
						<button
							id="copy-button"
							class="bg-primary-600 hover:bg-primary-700 text-white px-6 py-2 rounded-lg font-semibold transition-colors duration-200 flex items-center space-x-2 mx-auto"
						>
							<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
							</svg>
							<span>Copy Code</span>
						</button>
					</div>

					<!-- Instructions -->
					<div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
						<div class="flex items-start space-x-3">
							<svg class="w-5 h-5 text-blue-500 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
							</svg>
							<div>
								<h4 class="text-sm font-semibold text-blue-900 mb-1">Click anywhere to visit store:</h4>
								<ol class="text-sm text-blue-800 space-y-1">
									<li>1. Code will be copied automatically</li>
									<li>2. You'll be redirected to the store</li>
									<li>3. Paste the code at checkout</li>
								</ol>
							</div>
						</div>
					</div>
				</div>

				<!-- Option 2: Live Advertisement -->
				<div class="border-2 border-gray-200 rounded-xl p-4 hover:border-primary-300 hover:shadow-lg transition-all cursor-pointer" id="ad-option">
					<!-- Live Ad Content -->
					<div id="live-ad-content" class="min-h-[300px] flex items-center justify-center bg-gray-50 rounded-lg">
						<div class="text-center">
							<div class="text-sm text-gray-500 mb-1">Advertisement</div>
							<div class="text-xs text-gray-400">Loading...</div>
						</div>
					</div>
				</div>
			</div>

			<!-- Success Message -->
			<div id="copy-success" class="hidden p-3 bg-green-100 border border-green-300 rounded-lg">
				<div class="flex items-center space-x-2">
					<svg class="w-4 h-4 text-green-600" fill="currentColor" viewBox="0 0 20 20">
						<path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
					</svg>
					<span class="text-sm font-medium text-green-800">Coupon code copied to clipboard!</span>
				</div>
			</div>
		</div>
	</div>
</div>





<script>
	// Global variables for modal
	let currentCouponData = {};
	let currentAdData = {};

	// Show choice modal (entry point)
	window.showCouponModal = function(couponId, code, title, brand, storeUrl, brandLogo, discount) {
		currentCouponData = { couponId, code, title, brand, storeUrl, brandLogo, discount };

		const modal = document.getElementById('choice-modal');
		const modalContent = document.getElementById('modal-content');
		const modalTitle = document.getElementById('modal-title');
		const modalBrand = document.getElementById('modal-brand');
		const brandLogoImg = document.getElementById('brand-logo');
		const brandNameEl = document.getElementById('brand-name');
		const discountInfoEl = document.getElementById('discount-info');
		const fullCodeEl = document.getElementById('full-code');

		// Set content
		modalTitle.textContent = `Get "${title}"`;
		modalBrand.textContent = `Choose to continue`;

		// Set brand info
		brandNameEl.textContent = brand;
		discountInfoEl.textContent = discount || title;

		// Set brand logo
		if (brandLogo && brandLogo !== '#') {
			brandLogoImg.src = brandLogo;
			brandLogoImg.alt = `${brand} Logo`;
		} else {
			brandLogoImg.src = '/images/placeholder-brand.svg';
			brandLogoImg.alt = 'Brand Logo';
		}

		// Set full coupon code
		if (code && code !== 'NO CODE NEEDED') {
			fullCodeEl.textContent = code;
		} else {
			fullCodeEl.textContent = 'NO CODE NEEDED';
		}

		// Load and display live ad
		loadLiveAd();

		// Setup click event listeners
		setupClickListeners();

		// Show modal with animation
		modal.classList.remove('hidden');
		modal.classList.add('flex');

		// Trigger animation
		setTimeout(() => {
			modalContent.classList.remove('scale-95', 'opacity-0');
			modalContent.classList.add('scale-100', 'opacity-100');
		}, 10);
	};

	// Close choice modal
	window.closeChoiceModal = function() {
		const modal = document.getElementById('choice-modal');
		const modalContent = document.getElementById('modal-content');

		// Animate out
		modalContent.classList.remove('scale-100', 'opacity-100');
		modalContent.classList.add('scale-95', 'opacity-0');

		setTimeout(() => {
			modal.classList.add('hidden');
			modal.classList.remove('flex');
		}, 300);
	};

	// Copy coupon code function (internal use only)
	function copyCouponCodeInternal() {
		const code = currentCouponData.code;
		const copySuccess = document.getElementById('copy-success');

		if (code && code !== 'NO CODE NEEDED') {
			try {
				navigator.clipboard.writeText(code).then(() => {
					// Show success message
					copySuccess.classList.remove('hidden');
					setTimeout(() => {
						copySuccess.classList.add('hidden');
					}, 3000);
				}).catch(() => {
					// Fallback for older browsers
					const textArea = document.createElement('textarea');
					textArea.value = code;
					document.body.appendChild(textArea);
					textArea.select();
					document.execCommand('copy');
					document.body.removeChild(textArea);

					copySuccess.classList.remove('hidden');
					setTimeout(() => {
						copySuccess.classList.add('hidden');
					}, 3000);
				});
			} catch (error) {
				console.log('Copy failed:', error);
			}
		}
	}

	// Setup click event listeners
	function setupClickListeners() {
		// Remove any existing listeners first
		const affiliateOption = document.getElementById('affiliate-option');
		const adOption = document.getElementById('ad-option');
		const copyButton = document.getElementById('copy-button');

		// Clone elements to remove all event listeners
		if (affiliateOption) {
			const newAffiliateOption = affiliateOption.cloneNode(true);
			affiliateOption.parentNode.replaceChild(newAffiliateOption, affiliateOption);

			// Add click listener to the new element
			newAffiliateOption.addEventListener('click', function(event) {
				console.log('Affiliate option clicked');
				selectAffiliateLink();
			});
		}

		if (adOption) {
			const newAdOption = adOption.cloneNode(true);
			adOption.parentNode.replaceChild(newAdOption, adOption);

			// Add click listener to the new element
			newAdOption.addEventListener('click', function(event) {
				console.log('Ad option clicked');
				selectViewAd();
			});
		}

		// Setup copy button separately
		const newCopyButton = document.getElementById('copy-button');
		if (newCopyButton) {
			newCopyButton.addEventListener('click', function(event) {
				event.stopPropagation(); // Prevent triggering parent click
				console.log('Copy button clicked');
				copyCouponCodeInternal();
			});
		}
	}

	// Copy button click (standalone)
	window.copyCouponCode = function(event) {
		if (event) {
			event.stopPropagation(); // Prevent triggering parent click
		}
		copyCouponCodeInternal();
	};

	// Option 1: Select affiliate link (copy code + jump to store)
	window.selectAffiliateLink = function(event) {
		console.log('selectAffiliateLink called');

		const storeUrl = currentCouponData.storeUrl;
		console.log('Store URL:', storeUrl);

		// Copy code first
		copyCouponCodeInternal();

		// Close modal
		closeChoiceModal();

		// Jump directly to affiliate link
		if (storeUrl && storeUrl !== '#') {
			console.log('Opening affiliate link:', storeUrl);
			window.open(storeUrl, '_blank');
		} else {
			console.log('No store URL available');
		}

		// Track affiliate click
		console.log('Affiliate link clicked:', currentCouponData.couponId);
	};

	// Option 2: Select view ad (record ad click + copy code + open affiliate link)
	window.selectViewAd = function(event) {
		console.log('selectViewAd called');

		const storeUrl = currentCouponData.storeUrl;
		console.log('Store URL:', storeUrl);

		// Record ad click since user interacted with the ad
		recordAdClick();

		// Copy code
		copyCouponCodeInternal();

		// Close modal
		closeChoiceModal();

		// Open affiliate link in new tab
		if (storeUrl && storeUrl !== '#') {
			console.log('Opening affiliate link:', storeUrl);
			window.open(storeUrl, '_blank');
		} else {
			console.log('No store URL available');
		}

		// Note: The ad itself may also handle clicks and redirect to advertiser
		// This is handled by the ad script itself
		console.log('Ad option clicked:', currentCouponData.couponId);
	};

	// Load and display live ad in choice modal
	async function loadLiveAd() {
		const liveAdContainer = document.getElementById('live-ad-content');

		try {
			const response = await fetch(`http://127.0.0.1:8080/api/v1/ads/modal?entity_type=coupon&entity_id=${currentCouponData.couponId}`);
			const result = await response.json();

			if (result.success && result.data) {
				currentAdData = result.data;

				// Display the actual ad code directly
				liveAdContainer.innerHTML = result.data.ad_code;

				// Remove the default background and styling to let ad show naturally
				liveAdContainer.classList.remove('bg-gray-50');
				liveAdContainer.classList.add('bg-white');

				// Record ad show
				recordAdShow();

				console.log('Live ad loaded and displayed:', currentAdData.name);
			} else {
				console.error('Failed to load ad:', result.message);
				liveAdContainer.innerHTML = `
					<div class="text-center py-8">
						<div class="text-sm text-gray-500 mb-1">Advertisement</div>
						<div class="text-xs text-gray-400">No ads available</div>
					</div>
				`;
			}
		} catch (error) {
			console.error('Error loading live ad:', error);
			liveAdContainer.innerHTML = `
				<div class="text-center py-8">
					<div class="text-sm text-gray-500 mb-1">Advertisement</div>
					<div class="text-xs text-gray-400">Failed to load ad</div>
				</div>
			`;
		}
	}





	// Record ad show (display count +1)
	async function recordAdShow() {
		if (!currentAdData.id) return;

		try {
			await fetch('http://127.0.0.1:8080/api/v1/ads/click', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
				},
				body: JSON.stringify({
					ad_code_id: currentAdData.id,
					entity_type: 'coupon',
					entity_id: parseInt(currentCouponData.couponId),
					action: 'show'
				})
			});
			console.log('Ad show recorded for ad:', currentAdData.name);
		} catch (error) {
			console.error('Failed to record ad show:', error);
		}
	}

	// Record ad click (click count +1)
	async function recordAdClick() {
		if (!currentAdData.id) return;

		try {
			await fetch('http://127.0.0.1:8080/api/v1/ads/click', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
				},
				body: JSON.stringify({
					ad_code_id: currentAdData.id,
					entity_type: 'coupon',
					entity_id: parseInt(currentCouponData.couponId),
					action: 'click'
				})
			});
			console.log('Ad click recorded for ad:', currentAdData.name);
		} catch (error) {
			console.error('Failed to record ad click:', error);
		}
	}

	// Disable closing modals by clicking outside or pressing Escape
	// Users must use the close button to close modals
	document.addEventListener('click', function(event) {
		// Prevent any modal from closing by clicking outside
		// All modals must be closed using the close button
	});

	// Disable Escape key for closing modals
	document.addEventListener('keydown', function(event) {
		if (event.key === 'Escape') {
			// Prevent closing modals with Escape key
			// Users must click the close button
			event.preventDefault();
		}
	});
</script>
