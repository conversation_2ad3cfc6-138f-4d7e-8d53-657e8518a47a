<!-- Choice Modal for Coupon/Deal -->
<div id="choice-modal" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden items-center justify-center p-4">
	<div class="bg-white rounded-2xl max-w-lg w-full mx-auto transform transition-all duration-300 scale-95 opacity-0" id="modal-content">
		<div class="p-6">
			<!-- Header -->
			<div class="flex items-center justify-between mb-6">
				<div class="flex items-center space-x-3">
					<div class="w-12 h-12 bg-gradient-to-br from-primary-500 to-secondary-500 rounded-xl flex items-center justify-center">
						<svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 5v2m0 4v2m0 4v2M5 5a2 2 0 00-2 2v3a1 1 0 001 1h1a1 1 0 001-1V7a2 2 0 012-2h8a2 2 0 012 2v1a1 1 0 001 1h1a1 1 0 001-1V7a2 2 0 00-2-2H5z"></path>
						</svg>
					</div>
					<div>
						<h3 class="text-lg font-semibold text-gray-900" id="modal-title">Get Your Coupon</h3>
						<p class="text-sm text-gray-500" id="modal-brand">Choose an option to continue</p>
					</div>
				</div>
				<button onclick="closeChoiceModal()" class="text-gray-400 hover:text-gray-600 transition-colors">
					<svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
					</svg>
				</button>
			</div>

			<!-- Choice Options -->
			<div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
				<!-- Option 1: Brand + Coupon Info -->
				<div class="border-2 border-gray-200 rounded-xl p-4 hover:border-primary-300 hover:shadow-lg transition-all cursor-pointer" onclick="selectAffiliateLink()">
					<div class="flex items-center space-x-3 mb-3">
						<img
							id="brand-logo"
							src="/images/placeholder-brand.svg"
							alt="Brand Logo"
							class="w-12 h-12 rounded-lg object-contain bg-gray-50 p-2"
						/>
						<div class="flex-1">
							<div class="text-sm font-semibold text-gray-900" id="brand-name">Brand Name</div>
							<div class="text-xs text-gray-500" id="discount-info">Up to 50% Off</div>
						</div>
					</div>

					<!-- Hidden Coupon Code -->
					<div class="bg-gradient-to-r from-primary-50 to-secondary-50 border border-dashed border-primary-300 rounded-lg p-3 text-center">
						<div class="text-xs text-gray-600 mb-1">COUPON CODE</div>
						<div class="text-lg font-mono font-bold text-primary-600 tracking-wider" id="hidden-code">
							SAVE••••
						</div>
					</div>
				</div>

				<!-- Option 2: Live Advertisement -->
				<div class="border-2 border-gray-200 rounded-xl p-2 hover:border-primary-300 hover:shadow-lg transition-all cursor-pointer" onclick="selectViewAd()">
					<!-- Live Ad Content -->
					<div id="live-ad-content" class="min-h-[120px] flex items-center justify-center bg-gray-50 rounded-lg">
						<div class="text-center">
							<div class="text-sm text-gray-500 mb-1">Advertisement</div>
							<div class="text-xs text-gray-400">Loading...</div>
						</div>
					</div>
				</div>
			</div>

			<!-- Loading State -->
			<div id="loading-state" class="hidden text-center py-8">
				<div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
				<p class="mt-2 text-sm text-gray-600">Loading...</p>
			</div>
		</div>
	</div>
</div>





<script>
	// Global variables for modal
	let currentCouponData = {};
	let currentAdData = {};

	// Show choice modal (entry point)
	window.showCouponModal = function(couponId, code, title, brand, storeUrl, brandLogo, discount) {
		currentCouponData = { couponId, code, title, brand, storeUrl, brandLogo, discount };

		const modal = document.getElementById('choice-modal');
		const modalContent = document.getElementById('modal-content');
		const modalTitle = document.getElementById('modal-title');
		const modalBrand = document.getElementById('modal-brand');
		const brandLogoImg = document.getElementById('brand-logo');
		const brandNameEl = document.getElementById('brand-name');
		const discountInfoEl = document.getElementById('discount-info');
		const hiddenCodeEl = document.getElementById('hidden-code');

		// Set content
		modalTitle.textContent = `Get "${title}"`;
		modalBrand.textContent = `Choose to continue`;

		// Set brand info
		brandNameEl.textContent = brand;
		discountInfoEl.textContent = discount || title;

		// Set brand logo
		if (brandLogo && brandLogo !== '#') {
			brandLogoImg.src = brandLogo;
			brandLogoImg.alt = `${brand} Logo`;
		} else {
			brandLogoImg.src = '/images/placeholder-brand.svg';
			brandLogoImg.alt = 'Brand Logo';
		}

		// Set hidden coupon code (show first few characters + dots)
		if (code && code !== 'NO CODE NEEDED') {
			const hiddenCode = code.substring(0, Math.min(4, code.length)) + '••••';
			hiddenCodeEl.textContent = hiddenCode;
		} else {
			hiddenCodeEl.textContent = 'NO CODE';
		}

		// Load and display live ad
		loadLiveAd();

		// Show modal with animation
		modal.classList.remove('hidden');
		modal.classList.add('flex');

		// Trigger animation
		setTimeout(() => {
			modalContent.classList.remove('scale-95', 'opacity-0');
			modalContent.classList.add('scale-100', 'opacity-100');
		}, 10);
	};

	// Close choice modal
	window.closeChoiceModal = function() {
		const modal = document.getElementById('choice-modal');
		const modalContent = document.getElementById('modal-content');

		// Animate out
		modalContent.classList.remove('scale-100', 'opacity-100');
		modalContent.classList.add('scale-95', 'opacity-0');

		setTimeout(() => {
			modal.classList.add('hidden');
			modal.classList.remove('flex');
		}, 300);
	};

	// Option 1: Select affiliate link (direct jump to store)
	window.selectAffiliateLink = function() {
		const storeUrl = currentCouponData.storeUrl;

		// Close modal immediately
		closeChoiceModal();

		// Jump directly to affiliate link
		if (storeUrl && storeUrl !== '#') {
			window.open(storeUrl, '_blank');
		}

		// Track affiliate click
		console.log('Affiliate link clicked:', currentCouponData.couponId);
	};

	// Option 2: Select view ad (click ad + open affiliate link)
	window.selectViewAd = function() {
		const storeUrl = currentCouponData.storeUrl;

		// Record ad click since user interacted with the ad
		recordAdClick();

		// Close modal immediately
		closeChoiceModal();

		// Open affiliate link in new tab
		if (storeUrl && storeUrl !== '#') {
			window.open(storeUrl, '_blank');
		}

		// Let the ad handle its own click behavior (if any)
		// The ad script will handle navigation to advertiser's site
		console.log('Ad option clicked:', currentCouponData.couponId);
	};

	// Load and display live ad in choice modal
	async function loadLiveAd() {
		const liveAdContainer = document.getElementById('live-ad-content');

		try {
			const response = await fetch(`http://127.0.0.1:8080/api/v1/ads/modal?entity_type=coupon&entity_id=${currentCouponData.couponId}`);
			const result = await response.json();

			if (result.success && result.data) {
				currentAdData = result.data;

				// Create a wrapper div for the ad content
				const adWrapper = document.createElement('div');
				adWrapper.className = 'w-full h-full';
				adWrapper.innerHTML = result.data.ad_code;

				// Clear container and add the ad
				liveAdContainer.innerHTML = '';
				liveAdContainer.appendChild(adWrapper);

				// Record ad show
				recordAdShow();

				console.log('Live ad loaded and displayed:', currentAdData.name);
			} else {
				console.error('Failed to load ad:', result.message);
				liveAdContainer.innerHTML = `
					<div class="text-center py-8">
						<div class="text-sm text-gray-500 mb-1">Advertisement</div>
						<div class="text-xs text-gray-400">No ads available</div>
					</div>
				`;
			}
		} catch (error) {
			console.error('Error loading live ad:', error);
			liveAdContainer.innerHTML = `
				<div class="text-center py-8">
					<div class="text-sm text-gray-500 mb-1">Advertisement</div>
					<div class="text-xs text-gray-400">Failed to load ad</div>
				</div>
			`;
		}
	}





	// Record ad show (display count +1)
	async function recordAdShow() {
		if (!currentAdData.id) return;

		try {
			await fetch('http://127.0.0.1:8080/api/v1/ads/click', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
				},
				body: JSON.stringify({
					ad_code_id: currentAdData.id,
					entity_type: 'coupon',
					entity_id: parseInt(currentCouponData.couponId),
					action: 'show'
				})
			});
			console.log('Ad show recorded for ad:', currentAdData.name);
		} catch (error) {
			console.error('Failed to record ad show:', error);
		}
	}

	// Record ad click (click count +1)
	async function recordAdClick() {
		if (!currentAdData.id) return;

		try {
			await fetch('http://127.0.0.1:8080/api/v1/ads/click', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
				},
				body: JSON.stringify({
					ad_code_id: currentAdData.id,
					entity_type: 'coupon',
					entity_id: parseInt(currentCouponData.couponId),
					action: 'click'
				})
			});
			console.log('Ad click recorded for ad:', currentAdData.name);
		} catch (error) {
			console.error('Failed to record ad click:', error);
		}
	}

	// Disable closing modals by clicking outside or pressing Escape
	// Users must use the close button to close modals
	document.addEventListener('click', function(event) {
		// Prevent any modal from closing by clicking outside
		// All modals must be closed using the close button
	});

	// Disable Escape key for closing modals
	document.addEventListener('keydown', function(event) {
		if (event.key === 'Escape') {
			// Prevent closing modals with Escape key
			// Users must click the close button
			event.preventDefault();
		}
	});
</script>
