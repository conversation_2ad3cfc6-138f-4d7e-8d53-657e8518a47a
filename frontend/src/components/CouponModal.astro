<!-- Choice Modal for Coupon/Deal -->
<div id="choice-modal" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden items-center justify-center p-4">
	<div class="bg-white rounded-2xl max-w-lg w-full mx-auto transform transition-all duration-300 scale-95 opacity-0" id="modal-content">
		<div class="p-6">
			<!-- Header -->
			<div class="flex items-center justify-between mb-6">
				<div class="flex items-center space-x-3">
					<div class="w-12 h-12 bg-gradient-to-br from-primary-500 to-secondary-500 rounded-xl flex items-center justify-center">
						<svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 5v2m0 4v2m0 4v2M5 5a2 2 0 00-2 2v3a1 1 0 001 1h1a1 1 0 001-1V7a2 2 0 012-2h8a2 2 0 012 2v1a1 1 0 001 1h1a1 1 0 001-1V7a2 2 0 00-2-2H5z"></path>
						</svg>
					</div>
					<div>
						<h3 class="text-lg font-semibold text-gray-900" id="modal-title">Get Your Coupon</h3>
						<p class="text-sm text-gray-500" id="modal-brand">Choose an option to continue</p>
					</div>
				</div>
				<button onclick="closeChoiceModal()" class="text-gray-400 hover:text-gray-600 transition-colors">
					<svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
					</svg>
				</button>
			</div>

			<!-- Choice Instructions -->
			<div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
				<div class="flex items-start space-x-3">
					<svg class="w-5 h-5 text-blue-500 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
					</svg>
					<div>
						<h4 class="text-sm font-semibold text-blue-900 mb-1">Please choose one option to continue:</h4>
						<p class="text-sm text-blue-800">You must select either the affiliate link or view our sponsor ad to access your coupon code.</p>
					</div>
				</div>
			</div>

			<!-- Choice Options -->
			<div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
				<!-- Option 1: Affiliate Link (Brand Logo) -->
				<div class="border-2 border-gray-200 rounded-xl p-6 hover:border-primary-300 transition-colors cursor-pointer text-center" onclick="selectAffiliateLink()">
					<div class="mb-4">
						<img
							id="brand-logo"
							src="/images/placeholder-brand.svg"
							alt="Brand Logo"
							class="w-20 h-20 mx-auto rounded-lg object-contain bg-gray-50 p-2"
						/>
					</div>
					<h4 class="text-lg font-semibold text-gray-900 mb-2">Visit Store</h4>
					<p class="text-sm text-gray-600 mb-3">Go directly to the store and get your coupon code instantly.</p>
					<span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
						Instant Access
					</span>
				</div>

				<!-- Option 2: View Ad (Live Ad Display) -->
				<div class="border-2 border-gray-200 rounded-xl p-6 hover:border-primary-300 transition-colors cursor-pointer text-center" onclick="selectViewAd()">
					<div class="mb-4 min-h-[80px] flex items-center justify-center">
						<div id="preview-ad-content" class="w-full">
							<div class="bg-gradient-to-r from-purple-100 to-blue-100 rounded-lg p-4">
								<div class="text-sm text-gray-600 mb-1">Advertisement</div>
								<div class="text-xs text-gray-500">Loading preview...</div>
							</div>
						</div>
					</div>
					<h4 class="text-lg font-semibold text-gray-900 mb-2">View Sponsor</h4>
					<p class="text-sm text-gray-600 mb-3">Support our site by viewing our sponsor, then get your coupon.</p>
					<span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
						Support Us
					</span>
				</div>
			</div>

			<!-- Loading State -->
			<div id="loading-state" class="hidden text-center py-8">
				<div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
				<p class="mt-2 text-sm text-gray-600">Loading...</p>
			</div>
		</div>
	</div>
</div>

<!-- Ad Display Modal -->
<div id="ad-modal" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden items-center justify-center p-4">
	<div class="bg-white rounded-2xl max-w-2xl w-full mx-auto transform transition-all duration-300 scale-95 opacity-0" id="ad-modal-content">
		<div class="p-6">
			<!-- Ad Header -->
			<div class="flex items-center justify-between mb-6">
				<div class="flex items-center space-x-3">
					<div class="w-10 h-10 bg-yellow-100 rounded-lg flex items-center justify-center">
						<svg class="w-5 h-5 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"></path>
						</svg>
					</div>
					<div>
						<h3 class="text-lg font-semibold text-gray-900">Sponsor Message</h3>
						<p class="text-sm text-gray-500">Thank you for supporting our site!</p>
					</div>
				</div>
				<div class="text-sm text-gray-500">
					<span id="ad-countdown">5</span>s
				</div>
			</div>

			<!-- Ad Content -->
			<div id="ad-content" class="mb-6 min-h-[200px] flex items-center justify-center bg-gray-50 rounded-lg">
				<p class="text-gray-500">Loading advertisement...</p>
			</div>

			<!-- Ad Footer -->
			<div class="flex items-center justify-between">
				<p class="text-xs text-gray-500">Advertisement</p>
				<button
					id="continue-after-ad"
					onclick="continueAfterAd()"
					class="bg-primary-600 hover:bg-primary-700 text-white px-6 py-2 rounded-lg font-semibold transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
					disabled
				>
					Continue to Coupon
				</button>
			</div>
		</div>
	</div>
</div>

<!-- Final Coupon Modal -->
<div id="final-coupon-modal" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden items-center justify-center p-4">
	<div class="bg-white rounded-2xl max-w-md w-full mx-auto transform transition-all duration-300 scale-95 opacity-0" id="final-modal-content">
		<div class="p-6">
			<!-- Header -->
			<div class="flex items-center justify-between mb-6">
				<div class="flex items-center space-x-3">
					<div class="w-12 h-12 bg-gradient-to-br from-primary-500 to-secondary-500 rounded-xl flex items-center justify-center">
						<svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
						</svg>
					</div>
					<div>
						<h3 class="text-lg font-semibold text-gray-900" id="final-modal-title">Your Coupon Code</h3>
						<p class="text-sm text-gray-500" id="final-modal-brand">Brand Name</p>
					</div>
				</div>
				<button onclick="closeFinalModal()" class="text-gray-400 hover:text-gray-600 transition-colors">
					<svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
					</svg>
				</button>
			</div>

			<!-- Coupon Code Display -->
			<div class="bg-gradient-to-r from-primary-50 to-secondary-50 border-2 border-dashed border-primary-300 rounded-xl p-6 text-center mb-6">
				<div class="text-sm text-gray-600 mb-2">YOUR COUPON CODE</div>
				<div class="text-3xl font-mono font-bold text-primary-600 tracking-wider mb-4" id="final-modal-code">
					LOADING...
				</div>
				<button
					onclick="copyCouponCode()"
					class="bg-primary-600 hover:bg-primary-700 text-white px-6 py-2 rounded-lg font-semibold transition-colors duration-200 flex items-center space-x-2 mx-auto"
				>
					<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
					</svg>
					<span>Copy Code</span>
				</button>
			</div>

			<!-- Instructions -->
			<div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
				<div class="flex items-start space-x-3">
					<svg class="w-5 h-5 text-blue-500 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
					</svg>
					<div>
						<h4 class="text-sm font-semibold text-blue-900 mb-1">How to use this coupon:</h4>
						<ol class="text-sm text-blue-800 space-y-1">
							<li>1. Copy the coupon code above</li>
							<li>2. Click "Visit Store" to go to the website</li>
							<li>3. Add items to your cart</li>
							<li>4. Paste the code at checkout</li>
						</ol>
					</div>
				</div>
			</div>

			<!-- Action Buttons -->
			<div class="flex space-x-3">
				<button
					onclick="visitStore()"
					class="flex-1 bg-green-600 hover:bg-green-700 text-white py-3 rounded-lg font-semibold transition-colors duration-200 flex items-center justify-center space-x-2"
				>
					<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path>
					</svg>
					<span>Visit Store</span>
				</button>
				<button
					onclick="closeFinalModal()"
					class="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg font-semibold hover:bg-gray-50 transition-colors duration-200"
				>
					Close
				</button>
			</div>

			<!-- Success Message -->
			<div id="copy-success" class="hidden mt-4 p-3 bg-green-100 border border-green-300 rounded-lg">
				<div class="flex items-center space-x-2">
					<svg class="w-4 h-4 text-green-600" fill="currentColor" viewBox="0 0 20 20">
						<path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
					</svg>
					<span class="text-sm font-medium text-green-800">Coupon code copied to clipboard!</span>
				</div>
			</div>
		</div>
	</div>
</div>

<script>
	// Global variables for modal
	let currentCouponData = {};
	let currentAdData = {};
	let adCountdown = 5;
	let countdownInterval = null;

	// Show choice modal (entry point)
	window.showCouponModal = function(couponId, code, title, brand, storeUrl, brandLogo) {
		currentCouponData = { couponId, code, title, brand, storeUrl, brandLogo };

		const modal = document.getElementById('choice-modal');
		const modalContent = document.getElementById('modal-content');
		const modalTitle = document.getElementById('modal-title');
		const modalBrand = document.getElementById('modal-brand');
		const brandLogoImg = document.getElementById('brand-logo');

		// Set content
		modalTitle.textContent = `Get "${title}"`;
		modalBrand.textContent = `From ${brand}`;

		// Set brand logo
		if (brandLogo && brandLogo !== '#') {
			brandLogoImg.src = brandLogo;
			brandLogoImg.alt = `${brand} Logo`;
		} else {
			brandLogoImg.src = '/images/placeholder-brand.svg';
			brandLogoImg.alt = 'Brand Logo';
		}

		// Load ad preview
		loadAdPreview();

		// Show modal with animation
		modal.classList.remove('hidden');
		modal.classList.add('flex');

		// Trigger animation
		setTimeout(() => {
			modalContent.classList.remove('scale-95', 'opacity-0');
			modalContent.classList.add('scale-100', 'opacity-100');
		}, 10);

		// Track coupon view (display count +1) - load ad data first
		loadAdData();
	};

	// Close choice modal
	window.closeChoiceModal = function() {
		const modal = document.getElementById('choice-modal');
		const modalContent = document.getElementById('modal-content');

		// Animate out
		modalContent.classList.remove('scale-100', 'opacity-100');
		modalContent.classList.add('scale-95', 'opacity-0');

		setTimeout(() => {
			modal.classList.add('hidden');
			modal.classList.remove('flex');
		}, 300);
	};

	// Option 1: Select affiliate link (direct to store)
	window.selectAffiliateLink = function() {
		closeChoiceModal();

		// Show final coupon modal directly
		setTimeout(() => {
			showFinalCouponModal();
		}, 300);
	};

	// Option 2: Select view ad
	window.selectViewAd = function() {
		closeChoiceModal();

		// Load and show ad modal
		setTimeout(() => {
			loadAndShowAd();
		}, 300);
	};

	// Load ad data from backend (for preview and tracking)
	async function loadAdData() {
		try {
			const response = await fetch(`http://127.0.0.1:8080/api/v1/ads/modal?entity_type=coupon&entity_id=${currentCouponData.couponId}`);
			const result = await response.json();

			if (result.success && result.data) {
				currentAdData = result.data;
				console.log('Ad data loaded:', currentAdData);
			} else {
				console.error('Failed to load ad:', result.message);
			}
		} catch (error) {
			console.error('Error loading ad:', error);
		}
	}

	// Load ad preview for choice modal
	async function loadAdPreview() {
		const previewContainer = document.getElementById('preview-ad-content');

		try {
			const response = await fetch(`http://127.0.0.1:8080/api/v1/ads/modal?entity_type=coupon&entity_id=${currentCouponData.couponId}`);
			const result = await response.json();

			if (result.success && result.data) {
				// Create a scaled-down preview of the ad
				const adCode = result.data.ad_code;

				// For preview, we'll show a simplified version
				// You can customize this based on your ad format
				previewContainer.innerHTML = `
					<div class="bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg p-3 border border-gray-200">
						<div class="text-xs text-gray-600 mb-1">AdSense</div>
						<div class="text-xs text-gray-500">Click to view full ad</div>
						<div class="mt-1 text-xs text-blue-600 font-medium">${result.data.name}</div>
					</div>
				`;
			} else {
				previewContainer.innerHTML = `
					<div class="bg-gray-100 rounded-lg p-3">
						<div class="text-xs text-gray-500">Ad preview unavailable</div>
					</div>
				`;
			}
		} catch (error) {
			console.error('Error loading ad preview:', error);
			previewContainer.innerHTML = `
				<div class="bg-gray-100 rounded-lg p-3">
					<div class="text-xs text-gray-500">Ad preview unavailable</div>
				</div>
			`;
		}
	}

	// Load ad from backend and show ad modal
	async function loadAndShowAd() {
		if (!currentAdData.id) {
			// If ad data not loaded yet, try to load it
			await loadAdData();
		}

		if (currentAdData.id) {
			showAdModal();
		} else {
			console.error('No ad data available');
			// Fallback to direct coupon
			showFinalCouponModal();
		}
	}

	// Show ad modal
	function showAdModal() {
		const modal = document.getElementById('ad-modal');
		const modalContent = document.getElementById('ad-modal-content');
		const adContent = document.getElementById('ad-content');
		const continueBtn = document.getElementById('continue-after-ad');

		// Set ad content
		adContent.innerHTML = currentAdData.ad_code || '<p class="text-gray-500">Advertisement content</p>';

		// Reset countdown
		adCountdown = 5;
		continueBtn.disabled = true;
		continueBtn.textContent = `Continue (${adCountdown}s)`;

		// Show modal with animation
		modal.classList.remove('hidden');
		modal.classList.add('flex');

		// Trigger animation
		setTimeout(() => {
			modalContent.classList.remove('scale-95', 'opacity-0');
			modalContent.classList.add('scale-100', 'opacity-100');
		}, 10);

		// Start countdown
		startAdCountdown();
	}

	// Start ad countdown
	function startAdCountdown() {
		const countdownElement = document.getElementById('ad-countdown');
		const continueBtn = document.getElementById('continue-after-ad');

		countdownInterval = setInterval(() => {
			adCountdown--;
			countdownElement.textContent = adCountdown;
			continueBtn.textContent = `Continue (${adCountdown}s)`;

			if (adCountdown <= 0) {
				clearInterval(countdownInterval);
				continueBtn.disabled = false;
				continueBtn.textContent = 'Continue to Coupon';
			}
		}, 1000);
	}

	// Continue after ad
	window.continueAfterAd = function() {
		// Record ad click
		recordAdClick();

		// Close ad modal
		closeAdModal();

		// Show final coupon modal
		setTimeout(() => {
			showFinalCouponModal();
		}, 300);
	};

	// Close ad modal
	function closeAdModal() {
		const modal = document.getElementById('ad-modal');
		const modalContent = document.getElementById('ad-modal-content');

		// Clear countdown
		if (countdownInterval) {
			clearInterval(countdownInterval);
		}

		// Animate out
		modalContent.classList.remove('scale-100', 'opacity-100');
		modalContent.classList.add('scale-95', 'opacity-0');

		setTimeout(() => {
			modal.classList.add('hidden');
			modal.classList.remove('flex');
		}, 300);
	}

	// Show final coupon modal
	function showFinalCouponModal() {
		const modal = document.getElementById('final-coupon-modal');
		const modalContent = document.getElementById('final-modal-content');
		const modalTitle = document.getElementById('final-modal-title');
		const modalBrand = document.getElementById('final-modal-brand');
		const modalCode = document.getElementById('final-modal-code');

		// Set content
		modalTitle.textContent = currentCouponData.title;
		modalBrand.textContent = currentCouponData.brand;
		modalCode.textContent = currentCouponData.code || 'NO CODE NEEDED';

		// Show modal with animation
		modal.classList.remove('hidden');
		modal.classList.add('flex');

		// Trigger animation
		setTimeout(() => {
			modalContent.classList.remove('scale-95', 'opacity-0');
			modalContent.classList.add('scale-100', 'opacity-100');
		}, 10);
	}

	// Close final modal
	window.closeFinalModal = function() {
		const modal = document.getElementById('final-coupon-modal');
		const modalContent = document.getElementById('final-modal-content');
		const copySuccess = document.getElementById('copy-success');

		// Hide success message
		copySuccess.classList.add('hidden');

		// Animate out
		modalContent.classList.remove('scale-100', 'opacity-100');
		modalContent.classList.add('scale-95', 'opacity-0');

		setTimeout(() => {
			modal.classList.add('hidden');
			modal.classList.remove('flex');
		}, 300);
	};

	// Copy coupon code
	window.copyCouponCode = function() {
		const code = currentCouponData.code;
		const copySuccess = document.getElementById('copy-success');

		if (code && code !== 'NO CODE NEEDED') {
			navigator.clipboard.writeText(code).then(() => {
				// Show success message
				copySuccess.classList.remove('hidden');
				setTimeout(() => {
					copySuccess.classList.add('hidden');
				}, 3000);
			}).catch(() => {
				// Fallback for older browsers
				const textArea = document.createElement('textarea');
				textArea.value = code;
				document.body.appendChild(textArea);
				textArea.select();
				document.execCommand('copy');
				document.body.removeChild(textArea);

				copySuccess.classList.remove('hidden');
				setTimeout(() => {
					copySuccess.classList.add('hidden');
				}, 3000);
			});
		}
	};

	// Visit store
	window.visitStore = function() {
		const storeUrl = currentCouponData.storeUrl;
		if (storeUrl && storeUrl !== '#') {
			window.open(storeUrl, '_blank');
		} else {
			// Fallback - you could redirect to a search or brand page
			console.log('No store URL available');
		}

		// Track coupon click (you can add analytics here)
		console.log('Coupon clicked:', currentCouponData.couponId);
	};

	// Record ad show (display count +1)
	async function recordAdShow(entityId, entityType) {
		if (!currentAdData.id) return;

		try {
			await fetch('http://127.0.0.1:8080/api/v1/ads/click', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
				},
				body: JSON.stringify({
					ad_code_id: currentAdData.id,
					entity_type: entityType,
					entity_id: parseInt(entityId)
				})
			});
		} catch (error) {
			console.error('Failed to record ad show:', error);
		}
	}

	// Record ad click (click count +1)
	async function recordAdClick() {
		if (!currentAdData.id) return;

		try {
			await fetch('http://127.0.0.1:8080/api/v1/ads/click', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
				},
				body: JSON.stringify({
					ad_code_id: currentAdData.id,
					entity_type: 'coupon',
					entity_id: parseInt(currentCouponData.couponId)
				})
			});
		} catch (error) {
			console.error('Failed to record ad click:', error);
		}
	}

	// Disable closing modals by clicking outside or pressing Escape
	// Users must use the close button to close modals
	document.addEventListener('click', function(event) {
		// Prevent any modal from closing by clicking outside
		// All modals must be closed using the close button
	});

	// Disable Escape key for closing modals
	document.addEventListener('keydown', function(event) {
		if (event.key === 'Escape') {
			// Prevent closing modals with Escape key
			// Users must click the close button
			event.preventDefault();
		}
	});
</script>
