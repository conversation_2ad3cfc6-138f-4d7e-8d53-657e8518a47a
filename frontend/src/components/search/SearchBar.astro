---
// Search bar component with autocomplete functionality
---

<div class="relative w-full max-w-2xl">
	<form action="/search" method="GET" class="relative">
		<div class="relative">
			<input
				type="text"
				name="q"
				id="search-input"
				placeholder="Search for coupons, deals, brands..."
				class="w-full pl-12 pr-20 py-3 border border-gray-300 rounded-xl bg-white text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all duration-200"
				autocomplete="off"
				autocapitalize="off"
				spellcheck="false"
			/>
			
			<!-- Search Icon -->
			<div class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
				<svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
					<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
				</svg>
			</div>
			
			<!-- Search Button -->
			<button
				type="submit"
				class="absolute inset-y-0 right-0 pr-3 flex items-center"
			>
				<span class="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2">
					Search
				</span>
			</button>
		</div>
	</form>

	<!-- Search Suggestions Dropdown -->
	<div 
		id="search-suggestions" 
		class="absolute top-full left-0 right-0 mt-2 bg-white rounded-xl shadow-large border border-gray-100 z-50 hidden"
	>
		<!-- Popular Searches -->
		<div class="p-4 border-b border-gray-100">
			<div class="text-xs font-semibold text-gray-500 uppercase tracking-wide mb-3">
				Popular Searches
			</div>
			<div class="flex flex-wrap gap-2">
				<button class="search-suggestion px-3 py-1 bg-gray-100 hover:bg-gray-200 text-gray-700 text-sm rounded-full transition-colors duration-200">
					Nike shoes
				</button>
				<button class="search-suggestion px-3 py-1 bg-gray-100 hover:bg-gray-200 text-gray-700 text-sm rounded-full transition-colors duration-200">
					Amazon deals
				</button>
				<button class="search-suggestion px-3 py-1 bg-gray-100 hover:bg-gray-200 text-gray-700 text-sm rounded-full transition-colors duration-200">
					Fashion clothing
				</button>
				<button class="search-suggestion px-3 py-1 bg-gray-100 hover:bg-gray-200 text-gray-700 text-sm rounded-full transition-colors duration-200">
					Electronics
				</button>
				<button class="search-suggestion px-3 py-1 bg-gray-100 hover:bg-gray-200 text-gray-700 text-sm rounded-full transition-colors duration-200">
					Home furniture
				</button>
			</div>
		</div>

		<!-- Dynamic Suggestions -->
		<div id="dynamic-suggestions" class="p-4">
			<div class="text-xs font-semibold text-gray-500 uppercase tracking-wide mb-3">
				Suggestions
			</div>
			<div id="suggestions-list" class="space-y-1">
				<!-- Dynamic suggestions will be inserted here -->
			</div>
		</div>

		<!-- Quick Actions -->
		<div class="p-4 border-t border-gray-100 bg-gray-50 rounded-b-xl">
			<div class="flex items-center justify-between text-sm">
				<div class="flex items-center space-x-4">
					<a href="/coupons" class="text-primary-600 hover:text-primary-700 font-medium">
						Browse All Coupons
					</a>
					<a href="/deals" class="text-primary-600 hover:text-primary-700 font-medium">
						Hot Deals
					</a>
				</div>
				<div class="text-gray-500">
					Press <kbd class="px-2 py-1 bg-white border border-gray-200 rounded text-xs">Enter</kbd> to search
				</div>
			</div>
		</div>
	</div>
</div>

<script>
	document.addEventListener('DOMContentLoaded', () => {
		const searchInput = document.getElementById('search-input') as HTMLInputElement;
		const searchSuggestions = document.getElementById('search-suggestions');
		const suggestionsContainer = document.getElementById('suggestions-list');
		const dynamicSuggestions = document.getElementById('dynamic-suggestions');
		
		let debounceTimer: number;
		let currentSuggestions: string[] = [];
		let selectedIndex = -1;

		// Show suggestions on focus
		searchInput?.addEventListener('focus', () => {
			searchSuggestions?.classList.remove('hidden');
		});

		// Hide suggestions when clicking outside
		document.addEventListener('click', (e) => {
			if (!searchInput?.contains(e.target as Node) && !searchSuggestions?.contains(e.target as Node)) {
				searchSuggestions?.classList.add('hidden');
				selectedIndex = -1;
			}
		});

		// Handle input changes
		searchInput?.addEventListener('input', (e) => {
			const query = (e.target as HTMLInputElement).value.trim();
			
			clearTimeout(debounceTimer);
			
			if (query.length < 2) {
				dynamicSuggestions?.classList.add('hidden');
				return;
			}

			debounceTimer = setTimeout(() => {
				fetchSuggestions(query);
			}, 300);
		});

		// Handle keyboard navigation
		searchInput?.addEventListener('keydown', (e) => {
			const suggestionElements = searchSuggestions?.querySelectorAll('.suggestion-item');
			
			switch (e.key) {
				case 'ArrowDown':
					e.preventDefault();
					selectedIndex = Math.min(selectedIndex + 1, (suggestionElements?.length || 0) - 1);
					updateSelection(suggestionElements);
					break;
				case 'ArrowUp':
					e.preventDefault();
					selectedIndex = Math.max(selectedIndex - 1, -1);
					updateSelection(suggestionElements);
					break;
				case 'Enter':
					if (selectedIndex >= 0 && suggestionElements) {
						e.preventDefault();
						const selectedSuggestion = suggestionElements[selectedIndex] as HTMLElement;
						const suggestionText = selectedSuggestion.textContent?.trim();
						if (suggestionText) {
							searchInput.value = suggestionText;
							searchInput.form?.submit();
						}
					}
					break;
				case 'Escape':
					searchSuggestions?.classList.add('hidden');
					selectedIndex = -1;
					break;
			}
		});

		// Handle suggestion clicks
		document.addEventListener('click', (e) => {
			const target = e.target as HTMLElement;
			if (target.classList.contains('search-suggestion') || target.classList.contains('suggestion-item')) {
				const suggestionText = target.textContent?.trim();
				if (suggestionText) {
					searchInput.value = suggestionText;
					searchInput.form?.submit();
				}
			}
		});

		// Fetch suggestions from API
		async function fetchSuggestions(query: string) {
			try {
				const response = await fetch(`/api/search/suggestions?q=${encodeURIComponent(query)}`);
				const data = await response.json();
				
				if (data.data && Array.isArray(data.data)) {
					currentSuggestions = data.data;
					renderSuggestions(currentSuggestions);
					dynamicSuggestions?.classList.remove('hidden');
				}
			} catch (error) {
				console.error('Failed to fetch suggestions:', error);
				// Fallback to static suggestions
				const staticSuggestions = generateStaticSuggestions(query);
				renderSuggestions(staticSuggestions);
				dynamicSuggestions?.classList.remove('hidden');
			}
		}

		// Render suggestions in the dropdown
		function renderSuggestions(suggestions: string[]) {
			if (!suggestionsContainer) return;

			suggestionsContainer.innerHTML = '';
			
			suggestions.slice(0, 8).forEach((suggestion, index) => {
				const suggestionElement = document.createElement('button');
				suggestionElement.className = 'suggestion-item w-full text-left px-3 py-2 rounded-lg text-sm text-gray-700 hover:bg-gray-50 transition-colors duration-200';
				suggestionElement.textContent = suggestion;
				suggestionElement.setAttribute('data-index', index.toString());
				suggestionsContainer.appendChild(suggestionElement);
			});
		}

		// Update visual selection
		function updateSelection(suggestionElements: NodeListOf<Element> | undefined) {
			suggestionElements?.forEach((element, index) => {
				if (index === selectedIndex) {
					element.classList.add('bg-primary-50', 'text-primary-700');
				} else {
					element.classList.remove('bg-primary-50', 'text-primary-700');
				}
			});
		}

		// Generate static suggestions as fallback
		function generateStaticSuggestions(query: string): string[] {
			const staticSuggestions = [
				'Nike shoes',
				'Amazon deals',
				'Fashion clothing',
				'Electronics',
				'Home furniture',
				'Beauty products',
				'Sports equipment',
				'Kitchen appliances'
			];

			return staticSuggestions.filter(suggestion => 
				suggestion.toLowerCase().includes(query.toLowerCase())
			);
		}
	});
</script>

<style>
	/* Custom styles for search suggestions */
	#search-suggestions {
		max-height: 400px;
		overflow-y: auto;
	}

	.suggestion-item:hover {
		background-color: #f8fafc;
	}

	kbd {
		font-family: ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace;
	}
</style>
