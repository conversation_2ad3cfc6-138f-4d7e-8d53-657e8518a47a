---
// Hot Coupons component for displaying trending coupons
const API_BASE_URL = import.meta.env.PUBLIC_API_URL || 'http://127.0.0.1:8080';

let hotCoupons = [];
let error = null;

try {
	const response = await fetch(`${API_BASE_URL}/api/v1/coupons/hot?limit=8`);
	if (response.ok) {
		const data = await response.json();
		hotCoupons = data.data || [];
	} else {
		error = 'Failed to fetch hot coupons';
	}
} catch (err) {
	console.error('Error fetching hot coupons:', err);
	error = 'Network error';
}

// Helper function to format discount
function formatDiscount(coupon) {
	if (coupon.discount_type === 'percentage') {
		return `${coupon.discount_value}% OFF`;
	} else if (coupon.discount_type === 'fixed') {
		return `$${coupon.discount_value} OFF`;
	} else {
		return 'DEAL';
	}
}

// Helper function to format expiry date
function formatExpiryDate(dateString) {
	const date = new Date(dateString);
	const now = new Date();
	const diffTime = date - now;
	const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
	
	if (diffDays < 0) {
		return 'Expired';
	} else if (diffDays === 0) {
		return 'Expires today';
	} else if (diffDays === 1) {
		return 'Expires tomorrow';
	} else if (diffDays <= 7) {
		return `${diffDays} days left`;
	} else {
		return date.toLocaleDateString();
	}
}
---

<section class="py-16 bg-white">
	<div class="container mx-auto px-4">
		<div class="text-center mb-12">
			<h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
				🔥 <span class="text-gradient">Hot Coupons</span> Right Now
			</h2>
			<p class="text-lg text-gray-600 max-w-2xl mx-auto">
				Trending coupons that everyone is using. Don't miss out on these popular savings!
			</p>
		</div>

		{error ? (
			<div class="text-center py-12">
				<div class="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
					<svg class="w-8 h-8 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
					</svg>
				</div>
				<h3 class="text-lg font-semibold text-gray-900 mb-2">Unable to Load Hot Coupons</h3>
				<p class="text-gray-600">{error}</p>
			</div>
		) : hotCoupons.length > 0 ? (
			<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
				{hotCoupons.map((coupon) => (
					<div class="card p-6 rounded-2xl group hover:scale-105 transition-all duration-300 relative overflow-hidden">
						<!-- Hot Badge -->
						<div class="absolute top-4 right-4">
							<span class="bg-gradient-to-r from-orange-500 to-red-500 text-white px-3 py-1 rounded-full text-xs font-bold">
								HOT
							</span>
						</div>

						<!-- Brand Info -->
						<div class="flex items-center space-x-3 mb-4">
							<div class="w-12 h-12 bg-gradient-to-br from-purple-100 to-blue-100 rounded-xl flex items-center justify-center">
								{coupon.brand?.logo_url ? (
									<img 
										src={coupon.brand.logo_url} 
										alt={coupon.brand.name}
										class="w-8 h-8 object-contain rounded"
									/>
								) : (
									<span class="text-lg font-bold text-purple-600">
										{coupon.brand?.name?.charAt(0) || 'C'}
									</span>
								)}
							</div>
							<div class="flex-1">
								<h3 class="font-semibold text-gray-900 text-sm">{coupon.brand?.name || 'Brand'}</h3>
								{coupon.is_verified && (
									<span class="text-xs text-green-500 font-medium flex items-center">
										<svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
											<path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
										</svg>
										VERIFIED
									</span>
								)}
							</div>
						</div>

						<!-- Discount Badge -->
						<div class="text-center mb-4">
							<div class="inline-block bg-gradient-to-r from-purple-600 to-blue-600 text-white px-6 py-3 rounded-xl font-bold text-xl">
								{formatDiscount(coupon)}
							</div>
						</div>

						<!-- Coupon Title -->
						<h4 class="font-semibold text-gray-900 mb-3 line-clamp-2 group-hover:text-purple-600 transition-colors text-sm">
							{coupon.title}
						</h4>

						<!-- Stats -->
						<div class="flex items-center justify-between text-xs text-gray-500 mb-4">
							<div class="flex items-center space-x-1">
								<svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
									<path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
								</svg>
								<span>{coupon.success_rate || 0}% success</span>
							</div>
							<div class="flex items-center space-x-1">
								<svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
									<path d="M10 12a2 2 0 100-4 2 2 0 000 4z"></path>
									<path fill-rule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clip-rule="evenodd"></path>
								</svg>
								<span>{coupon.click_count || 0} used</span>
							</div>
						</div>

						<!-- Expiry Info -->
						<div class="text-xs text-orange-600 font-medium mb-4">
							⏰ {formatExpiryDate(coupon.expiry_date)}
						</div>

						<!-- Coupon Code -->
						<div class="flex items-center space-x-2">
							<div class="flex-1 bg-gray-100 rounded-lg p-3 font-mono text-center">
								<span class="text-purple-600 font-bold tracking-wider text-sm">
									{coupon.code || 'GET DEAL'}
								</span>
							</div>
							<button 
								class="copy-btn bg-purple-600 hover:bg-purple-700 text-white px-4 py-3 rounded-lg font-semibold text-xs transition-colors"
								data-code={coupon.code || ''}
								data-coupon-id={coupon.id}
							>
								Copy
							</button>
						</div>
					</div>
				))}
			</div>
		) : (
			<div class="text-center py-12">
				<div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
					<svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4"></path>
					</svg>
				</div>
				<h3 class="text-lg font-semibold text-gray-900 mb-2">No Hot Coupons Available</h3>
				<p class="text-gray-600">Check back later for trending deals</p>
			</div>
		)}

		<!-- View All Button -->
		{hotCoupons.length > 0 && (
			<div class="text-center mt-12">
				<a 
					href="/coupons" 
					class="inline-flex items-center px-8 py-4 bg-gradient-to-r from-purple-600 to-blue-600 text-white font-semibold rounded-xl hover:from-purple-700 hover:to-blue-700 transition-all duration-300 transform hover:scale-105"
				>
					<span>View All Coupons</span>
					<svg class="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
					</svg>
				</a>
			</div>
		)}
	</div>
</section>

<script>
	document.addEventListener('DOMContentLoaded', function() {
		// Copy coupon code functionality
		const copyButtons = document.querySelectorAll('.copy-btn');
		
		copyButtons.forEach(button => {
			button.addEventListener('click', async function() {
				const code = this.getAttribute('data-code');
				const couponId = this.getAttribute('data-coupon-id');
				
				if (code && code !== '') {
					try {
						await navigator.clipboard.writeText(code);
						
						// Update button text
						const originalText = this.textContent;
						this.textContent = 'Copied!';
						this.classList.add('bg-green-500');
						this.classList.remove('bg-purple-600', 'hover:bg-purple-700');
						
						// Track the click
						if (couponId) {
							fetch(`/api/v1/coupons/${couponId}/click`, {
								method: 'POST',
								headers: {
									'Content-Type': 'application/json',
								},
								body: JSON.stringify({
									user_ip: '',
									user_agent: navigator.userAgent,
									referrer: document.referrer
								})
							}).catch(err => console.log('Click tracking failed:', err));
						}
						
						// Reset button after 2 seconds
						setTimeout(() => {
							this.textContent = originalText;
							this.classList.remove('bg-green-500');
							this.classList.add('bg-purple-600', 'hover:bg-purple-700');
						}, 2000);
					} catch (err) {
						console.error('Failed to copy code:', err);
						this.textContent = 'Failed';
						setTimeout(() => {
							this.textContent = 'Copy';
						}, 2000);
					}
				}
			});
		});
	});
</script>

<style>
	.line-clamp-2 {
		display: -webkit-box;
		-webkit-line-clamp: 2;
		-webkit-box-orient: vertical;
		overflow: hidden;
	}
</style>
