---
// Stats section component showing platform achievements
const stats = [
	{
		number: "2M+",
		label: "Happy Users",
		description: "Customers saving money daily",
		icon: "👥",
		color: "purple"
	},
	{
		number: "$50M+",
		label: "Money Saved",
		description: "Total savings for our users",
		icon: "💰",
		color: "green"
	},
	{
		number: "10K+",
		label: "Active Coupons",
		description: "Verified deals available now",
		icon: "🎫",
		color: "blue"
	},
	{
		number: "500+",
		label: "Top Brands",
		description: "Premium partners worldwide",
		icon: "🏆",
		color: "orange"
	},
	{
		number: "95%",
		label: "Success Rate",
		description: "Coupons that actually work",
		icon: "✅",
		color: "emerald"
	},
	{
		number: "24/7",
		label: "Support",
		description: "Always here to help you",
		icon: "🛟",
		color: "pink"
	}
];
---

<section class="py-16 bg-white">
	<div class="container mx-auto px-4">
		<!-- Section Header -->
		<div class="text-center mb-12">
			<h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
				Trusted by Millions Worldwide
			</h2>
			<p class="text-lg text-gray-600 max-w-2xl mx-auto">
				Join the community of smart shoppers who save money every day with MaxCoupon
			</p>
		</div>

		<!-- Stats Grid -->
		<div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-6 mb-12">
			{stats.map((stat) => (
				<div class="text-center group">
					<div class="relative mb-4">
						<!-- Icon Background -->
						<div class={`w-16 h-16 mx-auto rounded-xl flex items-center justify-center transition-all duration-300 group-hover:scale-110 ${
							stat.color === 'purple' ? 'bg-purple-100 group-hover:bg-purple-200' :
							stat.color === 'green' ? 'bg-green-100 group-hover:bg-green-200' :
							stat.color === 'blue' ? 'bg-blue-100 group-hover:bg-blue-200' :
							stat.color === 'orange' ? 'bg-orange-100 group-hover:bg-orange-200' :
							stat.color === 'emerald' ? 'bg-emerald-100 group-hover:bg-emerald-200' :
							'bg-pink-100 group-hover:bg-pink-200'
						}`}>
							<span class="text-2xl">{stat.icon}</span>
						</div>
					</div>
					
					<!-- Number -->
					<div class="text-2xl md:text-3xl font-bold text-gray-900 mb-2 counter" data-target={stat.number}>
						{stat.number}
					</div>
					
					<!-- Label -->
					<div class="font-semibold text-gray-900 mb-1">
						{stat.label}
					</div>
					
					<!-- Description -->
					<div class="text-sm text-gray-600">
						{stat.description}
					</div>
				</div>
			))}
		</div>

		<!-- Achievement Badges -->
		<div class="bg-gradient-to-r from-purple-50 to-blue-50 rounded-2xl p-8">
			<div class="text-center mb-8">
				<h3 class="text-2xl font-bold text-gray-900 mb-2">
					Our Achievements
				</h3>
				<p class="text-gray-600">
					Recognition from industry leaders and satisfied customers
				</p>
			</div>

			<div class="grid grid-cols-1 md:grid-cols-3 gap-6">
				<!-- Award 1 -->
				<div class="text-center">
					<div class="w-16 h-16 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-4">
						<svg class="w-8 h-8 text-yellow-600" fill="currentColor" viewBox="0 0 20 20">
							<path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
						</svg>
					</div>
					<h4 class="font-semibold text-gray-900 mb-2">Best Coupon Platform 2024</h4>
					<p class="text-sm text-gray-600">Awarded by Consumer Choice Awards</p>
				</div>

				<!-- Award 2 -->
				<div class="text-center">
					<div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
						<svg class="w-8 h-8 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
							<path fill-rule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
						</svg>
					</div>
					<h4 class="font-semibold text-gray-900 mb-2">Trusted Partner Certification</h4>
					<p class="text-sm text-gray-600">Verified by major retail partners</p>
				</div>

				<!-- Award 3 -->
				<div class="text-center">
					<div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
						<svg class="w-8 h-8 text-green-600" fill="currentColor" viewBox="0 0 20 20">
							<path fill-rule="evenodd" d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z" clip-rule="evenodd"></path>
						</svg>
					</div>
					<h4 class="font-semibold text-gray-900 mb-2">Customer Love Award</h4>
					<p class="text-sm text-gray-600">4.9/5 rating from 50,000+ reviews</p>
				</div>
			</div>
		</div>

		<!-- Call to Action -->
		<div class="text-center mt-12">
			<div class="inline-flex items-center space-x-2 text-sm text-gray-600 mb-4">
				<svg class="w-5 h-5 text-green-500" fill="currentColor" viewBox="0 0 20 20">
					<path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
				</svg>
				<span>Join thousands of satisfied customers</span>
			</div>
			<a href="/signup" class="btn-primary inline-flex items-center px-8 py-3">
				<svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
					<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z"></path>
				</svg>
				Start Saving Today
			</a>
		</div>
	</div>
</section>

<script>
	document.addEventListener('DOMContentLoaded', () => {
		// Animate counters when they come into view
		const counters = document.querySelectorAll('.counter');
		const observerOptions = {
			threshold: 0.5,
			rootMargin: '0px 0px -100px 0px'
		};

		const observer = new IntersectionObserver((entries) => {
			entries.forEach(entry => {
				if (entry.isIntersecting) {
					animateCounter(entry.target);
					observer.unobserve(entry.target);
				}
			});
		}, observerOptions);

		counters.forEach(counter => {
			observer.observe(counter);
		});

		function animateCounter(element) {
			const target = element.getAttribute('data-target');
			const isPercentage = target.includes('%');
			const isTime = target.includes('/');
			const hasPlus = target.includes('+');
			const hasDollar = target.includes('$');
			
			// Extract numeric value
			let numericValue = parseFloat(target.replace(/[^\d.]/g, ''));
			
			// Handle special cases
			if (target.includes('M')) {
				numericValue = numericValue * 1000000;
			} else if (target.includes('K')) {
				numericValue = numericValue * 1000;
			}

			let current = 0;
			const increment = numericValue / 100;
			const duration = 2000; // 2 seconds
			const stepTime = duration / 100;

			const timer = setInterval(() => {
				current += increment;
				if (current >= numericValue) {
					current = numericValue;
					clearInterval(timer);
				}

				// Format the display value
				let displayValue = current;
				if (target.includes('M')) {
					displayValue = (current / 1000000).toFixed(1) + 'M';
				} else if (target.includes('K')) {
					displayValue = (current / 1000).toFixed(1) + 'K';
				} else if (isPercentage) {
					displayValue = Math.round(current) + '%';
				} else if (isTime) {
					displayValue = '24/7';
				} else {
					displayValue = Math.round(current);
				}

				if (hasDollar && !target.includes('M')) {
					displayValue = '$' + displayValue;
				} else if (hasDollar && target.includes('M')) {
					displayValue = '$' + displayValue;
				}

				if (hasPlus && !isPercentage && !isTime) {
					displayValue += '+';
				}

				element.textContent = displayValue;
			}, stepTime);
		}
	});
</script>
