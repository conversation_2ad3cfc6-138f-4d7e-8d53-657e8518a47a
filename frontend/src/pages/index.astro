---
import Layout from '../layouts/Layout.astro';
import Hero from '../components/Hero.astro';
import SearchSection from '../components/SearchSection.astro';
import FeaturedCoupons from '../components/FeaturedCoupons.astro';
import HotDeals from '../components/HotDeals.astro';
import PopularBrands from '../components/PopularBrands.astro';
import Categories from '../components/Categories.astro';
import StatsSection from '../components/StatsSection.astro';
---

<Layout
	title="MaxCoupon - Premium Deals & Exclusive Coupons 2025"
	description="Discover curated exclusive offers from the world's most prestigious brands. Save more, spend smarter with our AI-powered deal discovery platform."
>
	<Hero />
	<SearchSection />
	<FeaturedCoupons />
	<HotDeals />
	<PopularBrands />
	<Categories />
	<StatsSection />
	
	<!-- Newsletter Section -->
	<section class="py-16 bg-purple-50">
		<div class="container mx-auto px-4">
			<div class="max-w-4xl mx-auto text-center">
				<div class="bg-white rounded-2xl p-12 shadow-sm border border-purple-100">
					<h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
						Never Miss a <span class="text-gradient">Premium Deal</span> Again
					</h2>
					<p class="text-lg text-gray-600 mb-8 max-w-2xl mx-auto">
						Get exclusive access to VIP deals, early bird offers, and premium coupons
						delivered straight to your inbox.
					</p>

					<div class="flex flex-col sm:flex-row gap-4 max-w-lg mx-auto">
						<input
							type="email"
							placeholder="Enter your email address"
							class="flex-1 px-6 py-4 bg-white border border-gray-300 rounded-lg text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
						/>
						<button class="btn-primary px-8 py-4 font-semibold whitespace-nowrap">
							Join VIP List
						</button>
					</div>

					<p class="text-sm text-gray-500 mt-4">
						Join 100,000+ smart savers. Unsubscribe anytime.
					</p>
				</div>
			</div>
		</div>
	</section>

	<!-- Trust Indicators -->
	<section class="py-16 bg-white">
		<div class="container mx-auto px-4">
			<div class="text-center mb-12">
				<h3 class="text-2xl md:text-3xl font-bold text-gray-900 mb-4">
					Trusted by Premium Shoppers Worldwide
				</h3>
				<p class="text-gray-600">
					Join millions who save smarter with MaxCoupon
				</p>
			</div>

			<div class="grid grid-cols-2 md:grid-cols-4 gap-8 max-w-4xl mx-auto">
				<div class="text-center">
					<div class="w-16 h-16 bg-purple-600 rounded-xl flex items-center justify-center mx-auto mb-4">
						<svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.707-4.293a1 1 0 010 1.414L9.414 18.707a1 1 0 01-1.414 0L2.293 13a1 1 0 010-1.414l4.293-4.293a1 1 0 011.414 0L12 11.586l4.293-4.293a1 1 0 011.414 0z"></path>
						</svg>
					</div>
					<h4 class="font-semibold text-gray-900 mb-2">Verified Deals</h4>
					<p class="text-sm text-gray-600">All coupons tested and verified</p>
				</div>

				<div class="text-center">
					<div class="w-16 h-16 bg-purple-500 rounded-xl flex items-center justify-center mx-auto mb-4">
						<svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
						</svg>
					</div>
					<h4 class="font-semibold text-gray-900 mb-2">Secure & Safe</h4>
					<p class="text-sm text-gray-600">Your data is protected</p>
				</div>

				<div class="text-center">
					<div class="w-16 h-16 bg-purple-400 rounded-xl flex items-center justify-center mx-auto mb-4">
						<svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
						</svg>
					</div>
					<h4 class="font-semibold text-gray-900 mb-2">Instant Savings</h4>
					<p class="text-sm text-gray-600">Apply deals in one click</p>
				</div>

				<div class="text-center">
					<div class="w-16 h-16 bg-purple-700 rounded-xl flex items-center justify-center mx-auto mb-4">
						<svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
						</svg>
					</div>
					<h4 class="font-semibold text-gray-900 mb-2">Premium Support</h4>
					<p class="text-sm text-gray-600">24/7 VIP customer service</p>
				</div>
			</div>
		</div>
	</section>
</Layout>

<script>
	document.addEventListener('DOMContentLoaded', () => {
		// Newsletter form handling
		const newsletterForm = document.querySelector('input[type="email"]');
		const subscribeBtn = document.querySelector('.btn-primary');
		
		subscribeBtn?.addEventListener('click', (e) => {
			e.preventDefault();
			const email = newsletterForm?.value;
			
			if (email && email.includes('@')) {
				// Simulate subscription
				subscribeBtn.textContent = 'Subscribed! ✓';
				subscribeBtn.classList.add('bg-green-600');
				
				setTimeout(() => {
					subscribeBtn.textContent = 'Join VIP List';
					subscribeBtn.classList.remove('bg-green-600');
				}, 3000);
			} else {
				// Show error
				newsletterForm?.classList.add('border-red-500');
				setTimeout(() => {
					newsletterForm?.classList.remove('border-red-500');
				}, 2000);
			}
		});

		// Add parallax effect to floating elements
		window.addEventListener('scroll', () => {
			const scrolled = window.pageYOffset;
			const parallax = document.querySelectorAll('.floating-element');
			const speed = 0.5;

			parallax.forEach(element => {
				const yPos = -(scrolled * speed);
				element.style.transform = `translateY(${yPos}px)`;
			});
		});

		// Add smooth reveal animations
		const observerOptions = {
			threshold: 0.1,
			rootMargin: '0px 0px -50px 0px'
		};

		const observer = new IntersectionObserver((entries) => {
			entries.forEach(entry => {
				if (entry.isIntersecting) {
					entry.target.classList.add('animate-fade-in-up');
				}
			});
		}, observerOptions);

		// Observe all cards and sections
		document.querySelectorAll('.card, section').forEach(el => {
			observer.observe(el);
		});
	});
</script>

<style>
	@keyframes fadeInUp {
		from {
			opacity: 0;
			transform: translateY(30px);
		}
		to {
			opacity: 1;
			transform: translateY(0);
		}
	}

	.animate-fade-in-up {
		animation: fadeInUp 0.6s ease-out forwards;
	}
</style>
