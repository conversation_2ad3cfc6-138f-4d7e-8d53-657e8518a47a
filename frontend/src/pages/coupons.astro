---
import Layout from '../layouts/Layout.astro';
import CouponModal from '../components/CouponModal.astro';

const API_BASE_URL = import.meta.env.PUBLIC_API_BASE_URL || 'http://127.0.0.1:8080';

// Fetch coupons from API
let coupons = [];
let totalCoupons = 0;
let error = null;

try {
	const response = await fetch(`${API_BASE_URL}/api/v1/coupons?limit=20&page=1`);
	if (response.ok) {
		const data = await response.json();
		coupons = data.data || [];
		totalCoupons = data.pagination?.total || coupons.length;
	} else {
		error = 'Failed to load coupons';
	}
} catch (e) {
	error = 'Network error';
	console.error('Error fetching coupons:', e);
}

const currentPage = 1;
const totalPages = Math.ceil(totalCoupons / 20);
---

<Layout 
	title="All Coupons - MaxCoupon"
	description="Browse thousands of verified coupons and promo codes from top brands. Save money on your favorite products with our exclusive deals."
>
	<!-- <PERSON> Header -->
	<div class="bg-gradient-to-r from-primary-600 to-secondary-600 text-white py-12">
		<div class="container mx-auto px-4">
			<div class="max-w-3xl">
				<h1 class="text-4xl md:text-5xl font-display font-bold mb-4">
					All Coupons
				</h1>
				<p class="text-xl text-blue-100 mb-6">
					Discover {totalCoupons.toLocaleString()}+ verified coupons and promo codes from your favorite brands
				</p>
				
				<!-- Quick Stats -->
				<div class="flex flex-wrap gap-6 text-sm">
					<div class="flex items-center space-x-2">
						<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
						</svg>
						<span>All coupons verified</span>
					</div>
					<div class="flex items-center space-x-2">
						<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
						</svg>
						<span>Updated daily</span>
					</div>
					<div class="flex items-center space-x-2">
						<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
						</svg>
						<span>Instant savings</span>
					</div>
				</div>
			</div>
		</div>
	</div>

	<!-- Main Content -->
	<div class="container mx-auto px-4 py-8">
		{error ? (
			<div class="text-center py-12">
				<div class="text-6xl mb-4">😞</div>
				<h3 class="text-xl font-semibold text-gray-900 mb-2">Unable to Load Coupons</h3>
				<p class="text-gray-600 mb-4">{error}</p>
				<button onclick="window.location.reload()" class="btn-primary">
					Try Again
				</button>
			</div>
		) : (
			<div class="flex flex-col lg:flex-row gap-8">
				<!-- Quick Filters -->
				<div class="lg:w-1/4">
					<div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
						<h3 class="text-lg font-semibold text-gray-900 mb-4">Filter Coupons</h3>

						<!-- Category Filter -->
						<div class="mb-6">
							<label class="block text-sm font-medium text-gray-700 mb-2">Category</label>
							<select id="category-filter" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500">
								<option value="">All Categories</option>
								<option value="electronics">Electronics</option>
								<option value="fashion">Fashion</option>
								<option value="home-garden">Home & Garden</option>
								<option value="sports">Sports</option>
								<option value="beauty">Beauty</option>
							</select>
						</div>

						<!-- Brand Filter -->
						<div class="mb-6">
							<label class="block text-sm font-medium text-gray-700 mb-2">Brand</label>
							<select id="brand-filter" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500">
								<option value="">All Brands</option>
								<option value="amazon">Amazon</option>
								<option value="nike">Nike</option>
								<option value="adidas">Adidas</option>
								<option value="hm">H&M</option>
							</select>
						</div>

						<!-- Discount Filter -->
						<div class="mb-6">
							<label class="block text-sm font-medium text-gray-700 mb-2">Minimum Discount</label>
							<select id="discount-filter" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500">
								<option value="">Any Discount</option>
								<option value="10">10% or more</option>
								<option value="20">20% or more</option>
								<option value="30">30% or more</option>
								<option value="50">50% or more</option>
							</select>
						</div>

						<button id="apply-filters" class="w-full btn-primary">
							Apply Filters
						</button>
					</div>
				</div>

				<!-- Coupons List -->
				<div class="lg:w-3/4">
					<!-- Sort and View Options -->
					<div class="flex flex-col sm:flex-row items-start sm:items-center justify-between mb-6 gap-4">
						<div class="text-gray-600">
							Showing <span class="font-semibold text-gray-900" id="showing-count">1-{Math.min(20, coupons.length)}</span> of
							<span class="font-semibold text-gray-900" id="total-count">{totalCoupons.toLocaleString()}</span> coupons
						</div>

						<div class="flex items-center space-x-4">
							<!-- Sort Dropdown -->
							<div class="relative">
								<select id="sort-filter" class="appearance-none bg-white border border-gray-300 rounded-lg px-4 py-2 pr-8 text-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent">
									<option value="featured">Featured</option>
									<option value="newest">Newest First</option>
									<option value="expiry">Expiring Soon</option>
									<option value="discount">Highest Discount</option>
									<option value="popularity">Most Popular</option>
								</select>
								<svg class="absolute right-2 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400 pointer-events-none" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
								</svg>
							</div>
						</div>
					</div>

					<!-- Coupons Grid -->
					<div id="coupons-grid" class="space-y-4 mb-8">
						{coupons.length > 0 ? coupons.map((coupon) => (
							<div class="bg-white rounded-xl border border-gray-200 hover:border-primary-300 hover:shadow-lg transition-all duration-300 overflow-hidden">
								<div class="flex">
									<!-- Left side - Discount Badge -->
									<div class="flex-shrink-0 w-28 bg-gradient-to-br from-primary-500 to-secondary-500 flex flex-col items-center justify-center text-white relative">
										<div class="text-center">
											<div class="text-xl font-bold leading-tight">
												{coupon.discount_type === 'percentage' ? `${coupon.discount_value}%` : `$${coupon.discount_value}`}
											</div>
											<div class="text-xs font-medium">
												OFF
											</div>
										</div>
										{coupon.is_exclusive && (
											<div class="absolute top-2 right-2">
												<span class="bg-orange-500 text-white px-1 py-0.5 rounded text-xs font-semibold">
													VIP
												</span>
											</div>
										)}
										<!-- Decorative notch -->
										<div class="absolute right-0 top-1/2 transform translate-x-1/2 -translate-y-1/2 w-3 h-3 bg-gray-50 rounded-full border border-gray-200"></div>
									</div>

									<!-- Right side - Content -->
									<div class="flex-1 p-4">
										<div class="flex items-start justify-between">
											<div class="flex-1">
												<div class="flex items-center space-x-2 mb-2">
													<div class="w-6 h-6 bg-purple-100 rounded flex items-center justify-center">
														<span class="text-sm font-bold text-purple-600">
															{coupon.brand?.name?.charAt(0) || 'C'}
														</span>
													</div>
													<span class="text-sm font-medium text-gray-600">{coupon.brand?.name || 'Brand'}</span>
													{coupon.is_verified && (
														<span class="text-xs bg-green-100 text-green-700 px-2 py-0.5 rounded-full font-medium">
															VERIFIED
														</span>
													)}
												</div>

												<h4 class="text-base font-semibold text-gray-900 mb-2 line-clamp-2">
													{coupon.title}
												</h4>

												<p class="text-gray-600 text-sm mb-3 line-clamp-1">
													{coupon.description}
												</p>

												<div class="flex items-center space-x-4 text-xs text-gray-500">
													<div class="flex items-center space-x-1">
														<svg class="w-3 h-3 text-green-500" fill="currentColor" viewBox="0 0 20 20">
															<path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
														</svg>
														<span>{coupon.success_rate || 0}% success</span>
													</div>
													<div class="flex items-center space-x-1">
														<svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
															<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
															<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
														</svg>
														<span>{coupon.click_count || 0} used</span>
													</div>
													{coupon.expiry_date && (
														<div class="flex items-center space-x-1">
															<svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
																<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
															</svg>
															<span>Expires {new Date(coupon.expiry_date).toLocaleDateString()}</span>
														</div>
													)}
												</div>
											</div>

											<!-- Code and Button -->
											<div class="flex flex-col items-end space-y-2">
												<div class="bg-gray-100 border-2 border-dashed border-gray-300 rounded-lg px-3 py-2 text-center min-w-[100px]">
													<div class="text-xs text-gray-500 mb-1">CODE</div>
													<div class="text-sm font-mono font-bold text-gray-900 tracking-wider">
														{coupon.code ? coupon.code.substring(0, 4) + '***' : 'GET***'}
													</div>
												</div>
												<button
													class="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-lg font-semibold text-sm transition-colors duration-200"
													onclick={`showCouponModal('${coupon.id}', '${coupon.code}', '${coupon.title}', '${coupon.brand?.name || 'Brand'}', '${coupon.brand?.website_url || '#'}', '${coupon.brand?.logo_url || '/images/placeholder-brand.svg'}', '${coupon.discount_amount || coupon.title}')`}
												>
													Get Code
												</button>
											</div>
										</div>
									</div>
								</div>
							</div>
						)) : (
							<div class="text-center py-12">
								<div class="text-6xl mb-4">🎫</div>
								<h3 class="text-xl font-semibold text-gray-900 mb-2">No Coupons Found</h3>
								<p class="text-gray-600">Try adjusting your filters or check back later for new coupons.</p>
							</div>
						)}
					</div>
				</div>
			</div>
		)}

				<!-- Load More / Pagination -->
				{coupons.length >= 20 && (
					<div class="flex justify-center">
						<button
							id="load-more-btn"
							class="px-8 py-3 bg-primary-600 hover:bg-primary-700 text-white font-semibold rounded-lg transition-colors duration-200"
						>
							Load More Coupons
						</button>
					</div>
				)}
			</div>
		</div>
	</div>

	<!-- Newsletter CTA -->
	<div class="bg-gradient-to-r from-primary-600 to-secondary-600 text-white py-16">
		<div class="container mx-auto px-4 text-center">
			<h2 class="text-3xl font-display font-bold mb-4">
				Never Miss a Deal Again!
			</h2>
			<p class="text-xl text-blue-100 mb-8 max-w-2xl mx-auto">
				Get the latest coupons and exclusive deals delivered straight to your inbox
			</p>
			<div class="flex flex-col sm:flex-row gap-4 max-w-md mx-auto">
				<input 
					type="email" 
					placeholder="Enter your email"
					class="flex-1 px-4 py-3 rounded-lg text-gray-900 focus:outline-none focus:ring-2 focus:ring-white focus:ring-offset-2 focus:ring-offset-primary-600"
				/>
				<button class="px-6 py-3 bg-white text-primary-600 font-semibold rounded-lg hover:bg-gray-50 transition-colors duration-200">
					Subscribe
				</button>
			</div>
		</div>
	</div>

	<!-- Coupon Modal -->
	<CouponModal />
</Layout>

<script>
	document.addEventListener('DOMContentLoaded', () => {
		const API_BASE_URL = 'http://127.0.0.1:8080';
		let currentPage = 1;
		let currentFilters = {};

		// Get DOM elements
		const couponsGrid = document.getElementById('coupons-grid');
		const loadMoreBtn = document.getElementById('load-more-btn');
		const applyFiltersBtn = document.getElementById('apply-filters');
		const categoryFilter = document.getElementById('category-filter');
		const brandFilter = document.getElementById('brand-filter');
		const discountFilter = document.getElementById('discount-filter');
		const sortFilter = document.getElementById('sort-filter');
		const showingCount = document.getElementById('showing-count');
		const totalCount = document.getElementById('total-count');

		// Apply filters functionality
		applyFiltersBtn?.addEventListener('click', async () => {
			currentFilters = {
				category: categoryFilter?.value || '',
				brand: brandFilter?.value || '',
				discount: discountFilter?.value || '',
				sort: sortFilter?.value || 'featured'
			};
			currentPage = 1;
			await loadCoupons(true);
		});

		// Sort functionality
		sortFilter?.addEventListener('change', async () => {
			currentFilters.sort = sortFilter.value;
			currentPage = 1;
			await loadCoupons(true);
		});

		// Load more functionality
		loadMoreBtn?.addEventListener('click', async () => {
			currentPage++;
			await loadCoupons(false);
		});

		// Load coupons function
		async function loadCoupons(replace = false) {
			try {
				// Show loading state
				if (loadMoreBtn) {
					loadMoreBtn.textContent = 'Loading...';
					loadMoreBtn.disabled = true;
				}

				// Build query parameters
				const params = new URLSearchParams({
					page: currentPage.toString(),
					limit: '20'
				});

				if (currentFilters.category) params.append('category', currentFilters.category);
				if (currentFilters.brand) params.append('brand', currentFilters.brand);
				if (currentFilters.discount) params.append('min_discount', currentFilters.discount);
				if (currentFilters.sort) params.append('sort', currentFilters.sort);

				// Make API call
				const response = await fetch(`${API_BASE_URL}/api/v1/coupons?${params}`);
				const data = await response.json();

				if (response.ok && data.data) {
					if (replace) {
						// Replace existing coupons
						displayCoupons(data.data, true);
					} else {
						// Append new coupons
						displayCoupons(data.data, false);
					}

					// Update counts
					const total = data.pagination?.total || data.data.length;
					const showing = replace ? data.data.length : (currentPage * 20);
					if (showingCount) showingCount.textContent = `1-${Math.min(showing, total)}`;
					if (totalCount) totalCount.textContent = total.toLocaleString();

					// Hide load more button if no more data
					if (loadMoreBtn) {
						if (data.data.length < 20 || showing >= total) {
							loadMoreBtn.style.display = 'none';
						} else {
							loadMoreBtn.style.display = 'block';
							loadMoreBtn.textContent = 'Load More Coupons';
							loadMoreBtn.disabled = false;
						}
					}
				} else {
					throw new Error('Failed to load coupons');
				}
			} catch (error) {
				console.error('Error loading coupons:', error);
				if (loadMoreBtn) {
					loadMoreBtn.textContent = 'Try Again';
					loadMoreBtn.disabled = false;
				}
			}
		}

		// Display coupons function
		function displayCoupons(coupons, replace = false) {
			const couponsHTML = coupons.map(coupon => `
				<div class="bg-white rounded-xl border border-gray-200 hover:border-primary-300 hover:shadow-lg transition-all duration-300 overflow-hidden">
					<div class="flex">
						<!-- Left side - Discount Badge -->
						<div class="flex-shrink-0 w-28 bg-gradient-to-br from-primary-500 to-secondary-500 flex flex-col items-center justify-center text-white relative">
							<div class="text-center">
								<div class="text-xl font-bold leading-tight">
									${coupon.discount_type === 'percentage' ? `${coupon.discount_value}%` : `$${coupon.discount_value}`}
								</div>
								<div class="text-xs font-medium">
									OFF
								</div>
							</div>
							${coupon.is_exclusive ? '<div class="absolute top-2 right-2"><span class="bg-orange-500 text-white px-1 py-0.5 rounded text-xs font-semibold">VIP</span></div>' : ''}
							<!-- Decorative notch -->
							<div class="absolute right-0 top-1/2 transform translate-x-1/2 -translate-y-1/2 w-3 h-3 bg-gray-50 rounded-full border border-gray-200"></div>
						</div>

						<!-- Right side - Content -->
						<div class="flex-1 p-4">
							<div class="flex items-start justify-between">
								<div class="flex-1">
									<div class="flex items-center space-x-2 mb-2">
										<div class="w-6 h-6 bg-purple-100 rounded flex items-center justify-center">
											<span class="text-sm font-bold text-purple-600">
												${coupon.brand?.name?.charAt(0) || 'C'}
											</span>
										</div>
										<span class="text-sm font-medium text-gray-600">${coupon.brand?.name || 'Brand'}</span>
										${coupon.is_verified ? '<span class="text-xs bg-green-100 text-green-700 px-2 py-0.5 rounded-full font-medium">VERIFIED</span>' : ''}
									</div>

									<h4 class="text-base font-semibold text-gray-900 mb-2 line-clamp-2">
										${coupon.title}
									</h4>

									<p class="text-gray-600 text-sm mb-3 line-clamp-1">
										${coupon.description}
									</p>

									<div class="flex items-center space-x-4 text-xs text-gray-500">
										<div class="flex items-center space-x-1">
											<svg class="w-3 h-3 text-green-500" fill="currentColor" viewBox="0 0 20 20">
												<path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
											</svg>
											<span>${coupon.success_rate || 0}% success</span>
										</div>
										<div class="flex items-center space-x-1">
											<svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
												<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
												<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
											</svg>
											<span>${coupon.click_count || 0} used</span>
										</div>
										${coupon.expiry_date ? `<div class="flex items-center space-x-1"><svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg><span>Expires ${new Date(coupon.expiry_date).toLocaleDateString()}</span></div>` : ''}
									</div>
								</div>

								<!-- Code and Button -->
								<div class="flex flex-col items-end space-y-2">
									<div class="bg-gray-100 border-2 border-dashed border-gray-300 rounded-lg px-3 py-2 text-center min-w-[100px]">
										<div class="text-xs text-gray-500 mb-1">CODE</div>
										<div class="text-sm font-mono font-bold text-gray-900 tracking-wider">
											${coupon.code ? coupon.code.substring(0, 4) + '***' : 'GET***'}
										</div>
									</div>
									<button
										class="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-lg font-semibold text-sm transition-colors duration-200"
										onclick="showCouponModal('${coupon.id}', '${coupon.code}', '${coupon.title}', '${coupon.brand?.name || 'Brand'}', '${coupon.brand?.website_url || '#'}', '${coupon.brand?.logo_url || '/images/placeholder-brand.svg'}', '${coupon.discount_amount || coupon.title}')"
									>
										Get Code
									</button>
								</div>
							</div>
						</div>
					</div>
				</div>
			`).join('');

			if (replace) {
				couponsGrid.innerHTML = couponsHTML;
			} else {
				couponsGrid.innerHTML += couponsHTML;
			}
		}

		// Copy coupon function
		window.copyCoupon = function(code) {
			navigator.clipboard.writeText(code).then(() => {
				// Show success message
				const toast = document.createElement('div');
				toast.className = 'fixed top-4 right-4 bg-green-500 text-white px-4 py-2 rounded-lg z-50';
				toast.textContent = `Copied: ${code}`;
				document.body.appendChild(toast);
				setTimeout(() => toast.remove(), 3000);
			}).catch(() => {
				// Fallback for older browsers
				const textArea = document.createElement('textarea');
				textArea.value = code;
				document.body.appendChild(textArea);
				textArea.select();
				document.execCommand('copy');
				document.body.removeChild(textArea);
			});
		};
	});
</script>

<style>
	.line-clamp-2 {
		display: -webkit-box;
		-webkit-line-clamp: 2;
		-webkit-box-orient: vertical;
		overflow: hidden;
	}
</style>
