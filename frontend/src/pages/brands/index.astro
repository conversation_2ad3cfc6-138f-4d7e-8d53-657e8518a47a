---
import Layout from '../../layouts/Layout.astro';

// Mock brands data - in real app this would come from API
const brands = [
	{
		id: 1,
		name: "Amazon",
		uniqueName: "amazon",
		description: "Global e-commerce giant with millions of products",
		logoUrl: "/images/brands/amazon.png",
		websiteUrl: "https://amazon.com",
		totalCoupons: 156,
		totalDeals: 89,
		averageDiscount: 25.0,
		popularityScore: 98,
		category: "Electronics",
		country: "United States"
	},
	{
		id: 2,
		name: "Nike",
		uniqueName: "nike",
		description: "Leading athletic footwear and apparel brand",
		logoUrl: "/images/brands/nike.png",
		websiteUrl: "https://nike.com",
		totalCoupons: 78,
		totalDeals: 45,
		averageDiscount: 30.0,
		popularityScore: 95,
		category: "Sports",
		country: "United States"
	},
	{
		id: 3,
		name: "H&M",
		uniqueName: "hm",
		description: "Swedish multinational clothing retailer",
		logoUrl: "/images/brands/hm.png",
		websiteUrl: "https://hm.com",
		totalCoupons: 92,
		totalDeals: 67,
		averageDiscount: 20.0,
		popularityScore: 88,
		category: "Fashion",
		country: "Sweden"
	},
	{
		id: 4,
		name: "IKEA",
		uniqueName: "ikea",
		description: "Swedish furniture and home goods retailer",
		logoUrl: "/images/brands/ikea.png",
		websiteUrl: "https://ikea.com",
		totalCoupons: 34,
		totalDeals: 28,
		averageDiscount: 15.0,
		popularityScore: 85,
		category: "Home",
		country: "Sweden"
	},
	{
		id: 5,
		name: "Adidas",
		uniqueName: "adidas",
		description: "German multinational sportswear corporation",
		logoUrl: "/images/brands/adidas.png",
		websiteUrl: "https://adidas.com",
		totalCoupons: 65,
		totalDeals: 41,
		averageDiscount: 28.0,
		popularityScore: 92,
		category: "Sports",
		country: "Germany"
	},
	{
		id: 6,
		name: "Apple",
		uniqueName: "apple",
		description: "Technology company known for innovative products",
		logoUrl: "/images/brands/apple.png",
		websiteUrl: "https://apple.com",
		totalCoupons: 23,
		totalDeals: 15,
		averageDiscount: 10.0,
		popularityScore: 96,
		category: "Electronics",
		country: "United States"
	}
];

const totalBrands = brands.length;
---

<Layout 
	title="Premium Brands - MaxCoupon"
	description="Discover exclusive deals and coupons from the world's most prestigious brands. Shop smarter with verified offers from top retailers."
>
	<!-- Page Header -->
	<div class="relative py-20 overflow-hidden">
		<div class="container mx-auto px-4">
			<div class="max-w-4xl mx-auto text-center">
				<h1 class="text-5xl md:text-6xl font-display font-bold mb-6">
					Premium <span class="text-gradient">Brands</span>
				</h1>
				<p class="text-xl text-neutral-300 mb-8 max-w-2xl mx-auto">
					Discover exclusive deals from the world's most prestigious brands. 
					From luxury fashion to cutting-edge technology.
				</p>
				
				<!-- Quick Stats -->
				<div class="flex flex-wrap justify-center gap-8 text-sm">
					<div class="flex items-center space-x-2">
						<div class="w-2 h-2 bg-primary-500 rounded-full"></div>
						<span class="text-neutral-400">All brands verified</span>
					</div>
					<div class="flex items-center space-x-2">
						<div class="w-2 h-2 bg-secondary-500 rounded-full"></div>
						<span class="text-neutral-400">Exclusive partnerships</span>
					</div>
					<div class="flex items-center space-x-2">
						<div class="w-2 h-2 bg-accent-500 rounded-full"></div>
						<span class="text-neutral-400">Premium support</span>
					</div>
				</div>
			</div>
		</div>
	</div>

	<!-- Main Content -->
	<div class="container mx-auto px-4 py-8">
		<!-- Filter Bar -->
		<div class="flex flex-col sm:flex-row items-start sm:items-center justify-between mb-8 gap-4">
			<div class="text-neutral-400">
				Showing <span class="font-semibold text-white">{totalBrands}</span> premium brands
			</div>
			
			<div class="flex items-center space-x-4">
				<!-- Category Filter -->
				<select class="bg-white/10 border border-white/20 rounded-xl px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent backdrop-blur-sm">
					<option value="">All Categories</option>
					<option value="electronics">Electronics</option>
					<option value="fashion">Fashion</option>
					<option value="sports">Sports</option>
					<option value="home">Home</option>
				</select>

				<!-- Sort Dropdown -->
				<select class="bg-white/10 border border-white/20 rounded-xl px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent backdrop-blur-sm">
					<option value="popularity">Most Popular</option>
					<option value="name">Name A-Z</option>
					<option value="discount">Highest Discount</option>
					<option value="coupons">Most Coupons</option>
				</select>
			</div>
		</div>

		<!-- Brands Grid -->
		<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
			{brands.map((brand) => (
				<a 
					href={`/brands/${brand.uniqueName}`}
					class="card card-hover p-8 group block"
				>
					<!-- Brand Header -->
					<div class="flex items-center justify-between mb-6">
						<div class="flex items-center space-x-4">
							<div class="w-16 h-16 bg-gradient-to-br from-neutral-700 to-neutral-800 rounded-2xl flex items-center justify-center">
								<span class="text-2xl font-bold text-white">
									{brand.name.charAt(0)}
								</span>
							</div>
							<div>
								<h3 class="text-xl font-semibold text-white group-hover:text-primary-400 transition-colors">
									{brand.name}
								</h3>
								<p class="text-sm text-neutral-400">{brand.category}</p>
							</div>
						</div>
						
						<!-- Popularity Score -->
						<div class="text-right">
							<div class="text-2xl font-bold text-primary-400">{brand.popularityScore}</div>
							<div class="text-xs text-neutral-500">Popularity</div>
						</div>
					</div>

					<!-- Description -->
					<p class="text-neutral-400 text-sm mb-6 line-clamp-2">
						{brand.description}
					</p>

					<!-- Stats -->
					<div class="grid grid-cols-3 gap-4 mb-6">
						<div class="text-center">
							<div class="text-lg font-bold text-white">{brand.totalCoupons}</div>
							<div class="text-xs text-neutral-500">Coupons</div>
						</div>
						<div class="text-center">
							<div class="text-lg font-bold text-white">{brand.totalDeals}</div>
							<div class="text-xs text-neutral-500">Deals</div>
						</div>
						<div class="text-center">
							<div class="text-lg font-bold text-secondary-400">{brand.averageDiscount}%</div>
							<div class="text-xs text-neutral-500">Avg Save</div>
						</div>
					</div>

					<!-- Action Button -->
					<div class="flex items-center justify-between">
						<span class="text-sm text-neutral-500">{brand.country}</span>
						<div class="flex items-center text-primary-400 group-hover:text-primary-300 transition-colors">
							<span class="text-sm font-medium">View Deals</span>
							<svg class="w-4 h-4 ml-1 group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
							</svg>
						</div>
					</div>
				</a>
			))}
		</div>

		<!-- Load More -->
		<div class="text-center">
			<button class="btn-outline px-8 py-4 rounded-xl font-semibold">
				Load More Brands
			</button>
		</div>
	</div>

	<!-- Featured Brands Section -->
	<section class="py-16 relative">
		<div class="container mx-auto px-4">
			<div class="text-center mb-12">
				<h2 class="text-3xl font-display font-bold text-white mb-4">
					🏆 <span class="text-gradient">Top Performing Brands</span>
				</h2>
				<p class="text-neutral-400 max-w-2xl mx-auto">
					Brands with the highest customer satisfaction and best deals
				</p>
			</div>

			<div class="grid grid-cols-1 md:grid-cols-3 gap-8">
				{brands.slice(0, 3).map((brand, index) => (
					<div class="card p-8 text-center">
						<div class="w-20 h-20 bg-gradient-to-br from-primary-500 to-secondary-500 rounded-2xl flex items-center justify-center mx-auto mb-6">
							<span class="text-3xl font-bold text-white">
								{brand.name.charAt(0)}
							</span>
						</div>
						<h3 class="text-xl font-semibold text-white mb-2">{brand.name}</h3>
						<p class="text-neutral-400 text-sm mb-4">{brand.description}</p>
						<div class="flex items-center justify-center space-x-4 mb-6">
							<div class="text-center">
								<div class="text-lg font-bold text-primary-400">{brand.totalCoupons}</div>
								<div class="text-xs text-neutral-500">Coupons</div>
							</div>
							<div class="text-center">
								<div class="text-lg font-bold text-secondary-400">{brand.averageDiscount}%</div>
								<div class="text-xs text-neutral-500">Avg Save</div>
							</div>
						</div>
						<a 
							href={`/brands/${brand.uniqueName}`}
							class="btn-primary px-6 py-3 rounded-xl font-medium inline-flex items-center space-x-2"
						>
							<span>Explore Deals</span>
							<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
							</svg>
						</a>
					</div>
				))}
			</div>
		</div>
	</section>
</Layout>

<script>
	document.addEventListener('DOMContentLoaded', () => {
		// Filter functionality
		const categoryFilter = document.querySelector('select[value=""]');
		const sortFilter = document.querySelector('select[value="popularity"]');
		
		// Track brand clicks
		const brandLinks = document.querySelectorAll('[href^="/brands/"]');
		
		brandLinks.forEach(link => {
			link.addEventListener('click', (e) => {
				const brandName = link.querySelector('h3')?.textContent?.trim();
				
				if (brandName) {
					try {
						fetch('/api/analytics/brand-click', {
							method: 'POST',
							headers: {
								'Content-Type': 'application/json',
							},
							body: JSON.stringify({
								brand_name: brandName,
								user_agent: navigator.userAgent,
								referrer: document.referrer,
								timestamp: new Date().toISOString()
							})
						});
					} catch (error) {
						console.error('Failed to track brand click:', error);
					}
				}
			});
		});

		// Add intersection observer for animations
		const observer = new IntersectionObserver((entries) => {
			entries.forEach((entry) => {
				if (entry.isIntersecting) {
					entry.target.classList.add('animate-fade-in-up');
				}
			});
		}, {
			threshold: 0.1,
			rootMargin: '0px 0px -50px 0px'
		});

		document.querySelectorAll('.card').forEach((card) => {
			observer.observe(card);
		});
	});
</script>

<style>
	.line-clamp-2 {
		display: -webkit-box;
		-webkit-line-clamp: 2;
		-webkit-box-orient: vertical;
		overflow: hidden;
	}
</style>
