---
import Layout from '../../layouts/Layout.astro';

const API_BASE_URL = import.meta.env.PUBLIC_API_URL || 'http://127.0.0.1:8080';

// Export getStaticPaths for dynamic routes
export async function getStaticPaths() {
	const API_BASE_URL = import.meta.env.PUBLIC_API_URL || 'http://127.0.0.1:8080';

	try {
		const response = await fetch(`${API_BASE_URL}/api/v1/brands`);
		if (response.ok) {
			const data = await response.json();
			const brands = data.data || [];

			return brands.map((brand) => ({
				params: { brand: brand.unique_name },
			}));
		}
	} catch (error) {
		console.error('Error fetching brands for static paths:', error);
	}

	// Return empty array if fetch fails
	return [];
}

// Get brand slug from URL
const { brand: brandSlug } = Astro.params;

// Fetch brand data
let brand = null;
let brandDeals = [];
let brandCoupons = [];
let relatedBrands = [];
let error = null;

try {
	// Fetch brand details
	const brandResponse = await fetch(`${API_BASE_URL}/api/v1/brands/${brandSlug}`);
	if (brandResponse.ok) {
		const brandData = await brandResponse.json();
		brand = brandData.data;
	} else {
		error = 'Brand not found';
	}

	if (brand) {
		// Fetch brand deals
		const dealsResponse = await fetch(`${API_BASE_URL}/api/v1/brands/${brandSlug}/deals?limit=12`);
		if (dealsResponse.ok) {
			const dealsData = await dealsResponse.json();
			brandDeals = dealsData.data || [];
		}

		// Fetch brand coupons
		const couponsResponse = await fetch(`${API_BASE_URL}/api/v1/brands/${brandSlug}/coupons?limit=8`);
		if (couponsResponse.ok) {
			const couponsData = await couponsResponse.json();
			brandCoupons = couponsData.data || [];
		}

		// Fetch related brands
		const relatedResponse = await fetch(`${API_BASE_URL}/api/v1/brands/related/${brand.id}?limit=6`);
		if (relatedResponse.ok) {
			const relatedData = await relatedResponse.json();
			relatedBrands = relatedData.data || [];
		}
	}
} catch (err) {
	console.error('Error fetching brand data:', err);
	error = 'Network error';
}

// Helper functions
function formatDiscount(item) {
	if (item.discount_type === 'percentage') {
		return `${item.discount_value}% OFF`;
	} else if (item.discount_type === 'fixed') {
		return `$${item.discount_value} OFF`;
	} else {
		return 'DEAL';
	}
}

function formatPrice(price) {
	return new Intl.NumberFormat('en-US', {
		style: 'currency',
		currency: 'USD'
	}).format(price);
}

// Return 404 if brand not found
if (error === 'Brand not found') {
	return Astro.redirect('/404');
}
---

<Layout 
	title={brand ? `${brand.name} Deals & Coupons - MaxCoupon` : 'Brand Not Found - MaxCoupon'}
	description={brand ? `Find the best deals and coupon codes for ${brand.name}. Save money with exclusive offers and verified discount codes.` : 'Brand not found.'}
>
	{brand && (
		<>
			<!-- Brand Hero Section -->
			<section class="relative py-20 overflow-hidden">
				<div class="absolute inset-0 bg-gradient-to-br from-purple-900/20 via-blue-900/20 to-indigo-900/20"></div>
				<div class="container mx-auto px-4 relative z-10">
					<div class="max-w-4xl mx-auto text-center">
						<!-- Brand Logo and Info -->
						<div class="flex flex-col items-center mb-8">
							<div class="relative mb-6">
								<div class="absolute inset-0 bg-gradient-to-r from-purple-500/20 to-blue-500/20 rounded-3xl blur-2xl"></div>
								<div class="relative bg-white/10 backdrop-blur-xl border border-white/20 rounded-3xl p-8">
									<img 
										src={brand.logo_url || '/images/placeholder-brand.png'} 
										alt={brand.name}
										class="w-24 h-24 mx-auto object-contain rounded-2xl"
									/>
								</div>
							</div>
							
							<div class="flex items-center space-x-3 mb-4">
								<h1 class="text-4xl md:text-5xl font-display font-bold text-white">
									{brand.name}
								</h1>
								{brand.is_verified && (
									<svg class="w-8 h-8 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
										<path fill-rule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
									</svg>
								)}
							</div>
							
							<p class="text-xl text-neutral-300 mb-6 max-w-2xl">
								{brand.description}
							</p>
							
							<!-- Brand Stats -->
							<div class="grid grid-cols-2 md:grid-cols-4 gap-6">
								<div class="text-center">
									<div class="text-2xl font-bold text-primary-400 mb-1">{brand.total_deals || 0}</div>
									<div class="text-neutral-400 text-sm">Active Deals</div>
								</div>
								<div class="text-center">
									<div class="text-2xl font-bold text-secondary-400 mb-1">{brand.total_coupons || 0}</div>
									<div class="text-neutral-400 text-sm">Coupons</div>
								</div>
								<div class="text-center">
									<div class="text-2xl font-bold text-accent-400 mb-1">{brand.average_discount || 0}%</div>
									<div class="text-neutral-400 text-sm">Avg Discount</div>
								</div>
								<div class="text-center">
									<div class="text-2xl font-bold text-primary-400 mb-1">{brand.popularity_score || 0}</div>
									<div class="text-neutral-400 text-sm">Popularity</div>
								</div>
							</div>
							
							<!-- Visit Store Button -->
							<div class="mt-8">
								<a 
									href={brand.website_url} 
									target="_blank" 
									rel="noopener noreferrer"
									class="group relative overflow-hidden"
								>
									<div class="absolute inset-0 bg-gradient-to-r from-purple-600 to-blue-600 rounded-2xl blur-xl group-hover:blur-2xl transition-all duration-300"></div>
									<div class="relative btn-primary text-white px-8 py-4 rounded-2xl font-semibold text-lg flex items-center space-x-3 hover:scale-105 transition-transform">
										<span>Visit {brand.name} Store</span>
										<svg class="w-5 h-5 group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
											<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path>
										</svg>
									</div>
								</a>
							</div>
						</div>
					</div>
				</div>
			</section>

			<!-- Brand Coupons Section -->
			{brandCoupons.length > 0 && (
				<section class="py-16">
					<div class="container mx-auto px-4">
						<div class="flex items-center justify-between mb-8">
							<h2 class="text-3xl font-display font-bold text-white">
								🎫 {brand.name} Coupons
							</h2>
							<a href={`/search-premium?brand=${brandSlug}&type=coupons`} class="text-primary-400 hover:text-primary-300 font-semibold">
								View All Coupons →
							</a>
						</div>
						
						<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
							{brandCoupons.map((coupon) => (
								<div class="card p-6 rounded-2xl group hover:scale-105 transition-all duration-300">
									<div class="flex items-center justify-between mb-4">
										<div class="flex items-center space-x-3">
											<img 
												src={brand.logo_url || '/images/placeholder-brand.png'} 
												alt={brand.name}
												class="w-8 h-8 rounded object-contain"
											/>
											<div>
												<h3 class="font-semibold text-white text-sm">{brand.name}</h3>
												{coupon.is_verified && (
													<span class="text-xs text-green-400 font-medium">VERIFIED</span>
												)}
											</div>
										</div>
										<div class="text-right">
											<div class="text-xl font-bold text-primary-400">
												{formatDiscount(coupon)}
											</div>
										</div>
									</div>
									
									<div class="space-y-3">
										<h4 class="font-semibold text-white line-clamp-2 group-hover:text-primary-400 transition-colors text-sm">
											{coupon.title}
										</h4>
										
										<div class="flex items-center justify-between text-sm">
											<div class="flex items-center space-x-1">
												<span class="text-green-400">✓</span>
												<span class="text-neutral-400">{coupon.success_rate}% success</span>
											</div>
											<span class="text-neutral-400">{coupon.used_count} used</span>
										</div>
										
										<div class="flex items-center space-x-2">
											<div class="flex-1 bg-neutral-800 rounded-lg p-2 font-mono text-center">
												<span class="text-primary-400 font-bold tracking-wider text-sm">
													{coupon.code || 'GET DEAL'}
												</span>
											</div>
											<button class="btn-secondary px-3 py-2 rounded-lg font-semibold text-sm hover:scale-105 transition-transform">
												Copy
											</button>
										</div>
									</div>
								</div>
							))}
						</div>
					</div>
				</section>
			)}

			<!-- Brand Deals Section -->
			{brandDeals.length > 0 && (
				<section class="py-16">
					<div class="container mx-auto px-4">
						<div class="flex items-center justify-between mb-8">
							<h2 class="text-3xl font-display font-bold text-white">
								🔥 {brand.name} Deals
							</h2>
							<a href={`/search-premium?brand=${brandSlug}&type=deals`} class="text-primary-400 hover:text-primary-300 font-semibold">
								View All Deals →
							</a>
						</div>
						
						<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
							{brandDeals.map((deal) => (
								<div class="card p-6 rounded-2xl group hover:scale-105 transition-all duration-300">
									<div class="relative mb-4">
										<img 
											src={deal.image_url} 
											alt={deal.title}
											class="w-full h-48 object-cover rounded-xl"
											loading="lazy"
										/>
										<div class="absolute top-3 right-3">
											<span class="bg-black/70 text-white px-2 py-1 rounded-lg text-sm font-bold">
												{formatDiscount(deal)}
											</span>
										</div>
										{deal.is_hot_deal && (
											<div class="absolute top-3 left-3">
												<span class="bg-orange-500 text-white px-3 py-1 rounded-full text-sm font-semibold">
													HOT
												</span>
											</div>
										)}
									</div>
									
									<div class="space-y-3">
										<h3 class="font-semibold text-white line-clamp-2 group-hover:text-primary-400 transition-colors text-sm">
											{deal.title}
										</h3>
										
										<div class="flex items-center space-x-2">
											<span class="text-lg font-bold text-primary-400">
												{formatPrice(deal.sale_price)}
											</span>
											{deal.original_price && (
												<span class="text-neutral-500 line-through text-sm">
													{formatPrice(deal.original_price)}
												</span>
											)}
										</div>
										
										<div class="flex items-center justify-between text-sm">
											<div class="flex items-center space-x-1">
												<span class="text-green-400">✓</span>
												<span class="text-neutral-400">{deal.success_rate}% success</span>
											</div>
											<span class="text-neutral-400">{deal.view_count} views</span>
										</div>
										
										<button class="w-full btn-secondary py-2 rounded-lg font-semibold text-sm hover:scale-105 transition-transform">
											Get Deal
										</button>
									</div>
								</div>
							))}
						</div>
					</div>
				</section>
			)}

			<!-- Related Brands Section -->
			{relatedBrands.length > 0 && (
				<section class="py-16">
					<div class="container mx-auto px-4">
						<div class="text-center mb-12">
							<h2 class="text-3xl font-display font-bold text-white mb-4">
								Similar Brands
							</h2>
							<p class="text-neutral-400">
								Discover more brands like {brand.name}
							</p>
						</div>
						
						<div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-6">
							{relatedBrands.map((relatedBrand) => (
								<a 
									href={`/brands/${relatedBrand.unique_name}`}
									class="card p-6 rounded-2xl group hover:scale-105 transition-all duration-300 text-center"
								>
									<div class="relative mb-4">
										<img 
											src={relatedBrand.logo_url || '/images/placeholder-brand.png'} 
											alt={relatedBrand.name}
											class="w-16 h-16 mx-auto object-contain rounded-lg"
											loading="lazy"
										/>
										{relatedBrand.is_verified && (
											<div class="absolute -top-2 -right-2">
												<svg class="w-6 h-6 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
													<path fill-rule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
												</svg>
											</div>
										)}
									</div>
									
									<h3 class="font-semibold text-white text-sm group-hover:text-primary-400 transition-colors">
										{relatedBrand.name}
									</h3>
									
									{relatedBrand.total_deals > 0 && (
										<p class="text-xs text-neutral-400 mt-1">
											{relatedBrand.total_deals} deals
										</p>
									)}
								</a>
							))}
						</div>
					</div>
				</section>
			)}
		</>
	)}

	{error && error !== 'Brand not found' && (
		<section class="py-16">
			<div class="container mx-auto px-4 text-center">
				<div class="card p-8 rounded-2xl max-w-md mx-auto">
					<div class="text-red-400 mb-4">
						<svg class="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
						</svg>
					</div>
					<h3 class="text-xl font-semibold text-white mb-2">Unable to Load Brand</h3>
					<p class="text-neutral-400 mb-4">{error}</p>
					<button class="btn-primary px-6 py-2 rounded-lg" onclick="window.location.reload()">
						Try Again
					</button>
				</div>
			</div>
		</section>
	)}
</Layout>

<script>
	// Add interactive functionality
	document.addEventListener('DOMContentLoaded', function() {
		// Copy coupon code functionality
		const copyButtons = document.querySelectorAll('button:contains("Copy")');
		copyButtons.forEach(button => {
			button.addEventListener('click', function() {
				const codeElement = this.parentElement.querySelector('.font-mono span');
				const code = codeElement.textContent.trim();
				
				if (code && code !== 'GET DEAL') {
					navigator.clipboard.writeText(code).then(() => {
						this.textContent = 'Copied!';
						this.classList.add('bg-green-500');
						
						setTimeout(() => {
							this.textContent = 'Copy';
							this.classList.remove('bg-green-500');
						}, 2000);
					});
				}
			});
		});

		// Deal click tracking
		const dealButtons = document.querySelectorAll('button:contains("Get Deal")');
		dealButtons.forEach(button => {
			button.addEventListener('click', function() {
				// Add click tracking logic here
				console.log('Deal clicked');
			});
		});
	});
</script>
