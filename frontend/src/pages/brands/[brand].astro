---
import Layout from '../../layouts/Layout.astro';
import CouponModal from '../../components/CouponModal.astro';

const API_BASE_URL = import.meta.env.PUBLIC_API_URL || 'http://127.0.0.1:8080';

// Server-side rendering - no getStaticPaths needed

// Get brand slug from URL
const { brand: brandSlug } = Astro.params;

// Fetch brand data
let brand = null;
let brandDeals = [];
let brandCoupons = [];
let relatedBrands = [];
let error = null;

try {
	// Fetch brand details using unique name
	const brandResponse = await fetch(`${API_BASE_URL}/api/v1/brands/name/${brandSlug}`);
	if (brandResponse.ok) {
		const brandData = await brandResponse.json();
		brand = brandData.data;
	} else {
		error = 'Brand not found';
	}

	if (brand) {
		// Fetch brand deals using brand ID
		const dealsResponse = await fetch(`${API_BASE_URL}/api/v1/brands/${brand.id}/deals?limit=12`);
		if (dealsResponse.ok) {
			const dealsData = await dealsResponse.json();
			brandDeals = dealsData.data || [];
		}

		// Fetch brand coupons using brand ID
		const couponsResponse = await fetch(`${API_BASE_URL}/api/v1/brands/${brand.id}/coupons?limit=8`);
		if (couponsResponse.ok) {
			const couponsData = await couponsResponse.json();
			brandCoupons = couponsData.data || [];
		}

		// Fetch related brands
		const relatedResponse = await fetch(`${API_BASE_URL}/api/v1/brands/related/${brand.id}?limit=6`);
		if (relatedResponse.ok) {
			const relatedData = await relatedResponse.json();
			relatedBrands = relatedData.data || [];
		}
	}
} catch (err) {
	console.error('Error fetching brand data:', err);
	error = 'Network error';
}

// Helper functions
function formatDiscount(item) {
	if (item.discount_type === 'percentage') {
		return `${item.discount_value}% OFF`;
	} else if (item.discount_type === 'fixed') {
		return `$${item.discount_value} OFF`;
	} else {
		return 'DEAL';
	}
}

function formatPrice(price) {
	return new Intl.NumberFormat('en-US', {
		style: 'currency',
		currency: 'USD'
	}).format(price);
}

// Return 404 if brand not found
if (error === 'Brand not found') {
	return Astro.redirect('/404');
}
---

<Layout 
	title={brand ? `${brand.name} Deals & Coupons - MaxCoupon` : 'Brand Not Found - MaxCoupon'}
	description={brand ? `Find the best deals and coupon codes for ${brand.name}. Save money with exclusive offers and verified discount codes.` : 'Brand not found.'}
>
	{brand && (
		<>
			<!-- Brand Hero Section -->
			<section class="relative py-20 overflow-hidden">
				<div class="absolute inset-0 bg-gradient-to-br from-purple-900/20 via-blue-900/20 to-indigo-900/20"></div>
				<div class="container mx-auto px-4 relative z-10">
					<div class="max-w-4xl mx-auto text-center">
						<!-- Brand Logo and Info -->
						<div class="flex flex-col items-center mb-8">
							<div class="relative mb-6">
								<div class="absolute inset-0 bg-gradient-to-r from-purple-500/20 to-blue-500/20 rounded-3xl blur-2xl"></div>
								<div class="relative bg-white/10 backdrop-blur-xl border border-white/20 rounded-3xl p-8">
									<img 
										src={brand.logo_url || '/images/placeholder-brand.png'} 
										alt={brand.name}
										class="w-24 h-24 mx-auto object-contain rounded-2xl"
									/>
								</div>
							</div>
							
							<div class="flex items-center space-x-3 mb-4">
								<h1 class="text-4xl md:text-5xl font-display font-bold text-white">
									{brand.name}
								</h1>
								{brand.is_verified && (
									<svg class="w-8 h-8 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
										<path fill-rule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
									</svg>
								)}
							</div>
							
							<p class="text-xl text-neutral-300 mb-6 max-w-2xl">
								{brand.description}
							</p>
							
							<!-- Brand Stats -->
							<div class="grid grid-cols-2 md:grid-cols-4 gap-6">
								<div class="text-center">
									<div class="text-2xl font-bold text-primary-400 mb-1">{brand.total_deals || 0}</div>
									<div class="text-neutral-400 text-sm">Active Deals</div>
								</div>
								<div class="text-center">
									<div class="text-2xl font-bold text-secondary-400 mb-1">{brand.total_coupons || 0}</div>
									<div class="text-neutral-400 text-sm">Coupons</div>
								</div>
								<div class="text-center">
									<div class="text-2xl font-bold text-accent-400 mb-1">{brand.average_discount || 0}%</div>
									<div class="text-neutral-400 text-sm">Avg Discount</div>
								</div>
								<div class="text-center">
									<div class="text-2xl font-bold text-primary-400 mb-1">{brand.popularity_score || 0}</div>
									<div class="text-neutral-400 text-sm">Popularity</div>
								</div>
							</div>
							
							<!-- Visit Store Button -->
							<div class="mt-8">
								<a 
									href={brand.website_url} 
									target="_blank" 
									rel="noopener noreferrer"
									class="group relative overflow-hidden"
								>
									<div class="absolute inset-0 bg-gradient-to-r from-purple-600 to-blue-600 rounded-2xl blur-xl group-hover:blur-2xl transition-all duration-300"></div>
									<div class="relative btn-primary text-white px-8 py-4 rounded-2xl font-semibold text-lg flex items-center space-x-3 hover:scale-105 transition-transform">
										<span>Visit {brand.name} Store</span>
										<svg class="w-5 h-5 group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
											<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path>
										</svg>
									</div>
								</a>
							</div>
						</div>
					</div>
				</div>
			</section>

			<!-- Brand Coupons Section - PRIORITY DISPLAY -->
			<section class="py-20 bg-gradient-to-br from-purple-900/10 via-blue-900/10 to-indigo-900/10">
				<div class="container mx-auto px-4">
					<div class="text-center mb-12">
						<h2 class="text-4xl font-display font-bold text-white mb-4">
							🎫 {brand.name} Exclusive Coupons
						</h2>
						<p class="text-xl text-neutral-300 max-w-2xl mx-auto">
							Save big with verified coupon codes from {brand.name}. Get instant discounts on your favorite products!
						</p>
					</div>

					{brandCoupons.length > 0 ? (
						<>
							<div class="space-y-4 mb-8">
								{brandCoupons.map((coupon) => (
									<div class="bg-white rounded-xl border border-gray-200 hover:border-primary-300 hover:shadow-lg transition-all duration-300 overflow-hidden">
										<div class="flex">
											<!-- Left side - Discount Badge -->
											<div class="flex-shrink-0 w-32 bg-gradient-to-br from-primary-500 to-secondary-500 flex flex-col items-center justify-center text-white relative">
												<div class="text-center">
													<div class="text-2xl font-bold leading-tight">
														{coupon.discount_type === 'percentage' ? `${coupon.discount_value}%` : `$${coupon.discount_value}`}
													</div>
													<div class="text-sm font-medium">
														OFF
													</div>
												</div>
												{coupon.is_exclusive && (
													<div class="absolute top-2 right-2">
														<span class="bg-orange-500 text-white px-2 py-1 rounded text-xs font-semibold">
															VIP
														</span>
													</div>
												)}
												<!-- Decorative notch -->
												<div class="absolute right-0 top-1/2 transform translate-x-1/2 -translate-y-1/2 w-4 h-4 bg-gray-50 rounded-full border-2 border-gray-200"></div>
											</div>

											<!-- Right side - Content -->
											<div class="flex-1 p-6">
												<div class="flex items-start justify-between">
													<div class="flex-1">
														<div class="flex items-center space-x-2 mb-2">
															<img
																src={brand.logo_url || '/images/placeholder-brand.png'}
																alt={brand.name}
																class="w-6 h-6 rounded object-contain"
															/>
															<span class="text-sm font-medium text-gray-600">{brand.name}</span>
															{coupon.is_verified && (
																<span class="text-xs bg-green-100 text-green-700 px-2 py-1 rounded-full font-medium">
																	VERIFIED
																</span>
															)}
														</div>

														<h3 class="text-lg font-semibold text-gray-900 mb-2 line-clamp-2">
															{coupon.title}
														</h3>

														<p class="text-gray-600 text-sm mb-4 line-clamp-2">
															{coupon.description}
														</p>

														<div class="flex items-center space-x-4 text-sm text-gray-500 mb-4">
															<div class="flex items-center space-x-1">
																<svg class="w-4 h-4 text-green-500" fill="currentColor" viewBox="0 0 20 20">
																	<path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
																</svg>
																<span>{coupon.success_rate}% success</span>
															</div>
															<div class="flex items-center space-x-1">
																<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
																	<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
																	<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
																</svg>
																<span>{coupon.click_count} used today</span>
															</div>
															{coupon.expiry_date && (
																<div class="flex items-center space-x-1">
																	<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
																		<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
																	</svg>
																	<span>Expires {new Date(coupon.expiry_date).toLocaleDateString()}</span>
																</div>
															)}
														</div>
													</div>

													<!-- Code and Button -->
													<div class="flex flex-col items-end space-y-3">
														<div class="bg-gray-100 border-2 border-dashed border-gray-300 rounded-lg px-4 py-2 text-center min-w-[120px]">
															<div class="text-xs text-gray-500 mb-1">CODE</div>
															<div class="text-sm font-mono font-bold text-gray-900 tracking-wider">
																{coupon.code ? coupon.code.substring(0, 4) + '***' : 'GET***'}
															</div>
														</div>
														<button
															class="bg-primary-600 hover:bg-primary-700 text-white px-6 py-2 rounded-lg font-semibold text-sm transition-colors duration-200"
															onclick={`showCouponModal('${coupon.id}', '${coupon.code}', '${coupon.title}', '${brand.name}', '${brand.website_url || '#'}', '${brand.logo_url || '/images/placeholder-brand.svg'}', '${coupon.discount_amount || coupon.title}')`}
														>
															Get Code
														</button>
													</div>
												</div>
											</div>
										</div>
									</div>
								))}
							</div>
							<div class="text-center">
								<a href={`/search?q=${brand.name}&type=coupons`} class="btn-secondary px-8 py-4 rounded-xl font-semibold text-lg hover:scale-105 transition-transform">
									View All {brand.name} Coupons →
								</a>
							</div>
						</>
					) : (
						<div class="text-center py-16">
							<div class="text-8xl mb-6">🎫</div>
							<h3 class="text-2xl font-semibold text-white mb-4">No Coupons Available Right Now</h3>
							<p class="text-neutral-400 text-lg">Check back soon for new {brand.name} coupon codes!</p>
						</div>
					)}
				</div>
			</section>

			<!-- Brand Deals Section - Secondary Content -->
			{brandDeals.length > 0 && (
				<section class="py-16 border-t border-gray-800/50">
					<div class="container mx-auto px-4">
						<div class="flex items-center justify-between mb-8">
							<div>
								<h2 class="text-2xl font-display font-bold text-white mb-2">
									🔥 {brand.name} Hot Deals
								</h2>
								<p class="text-neutral-400">Limited time offers and special discounts</p>
							</div>
							<a href={`/search?q=${brand.name}&type=deals`} class="text-primary-400 hover:text-primary-300 font-semibold">
								View All Deals →
							</a>
						</div>

						<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
							{brandDeals.slice(0, 8).map((deal) => (
								<div class="card p-4 rounded-xl group hover:scale-105 transition-all duration-300">
									<div class="relative mb-3">
										<img
											src={deal.image_url}
											alt={deal.title}
											class="w-full h-32 object-cover rounded-lg"
											loading="lazy"
										/>
										<div class="absolute top-2 right-2">
											<span class="bg-black/80 text-white px-2 py-1 rounded text-xs font-bold">
												{formatDiscount(deal)}
											</span>
										</div>
										{deal.is_hot_deal && (
											<div class="absolute top-2 left-2">
												<span class="bg-orange-500 text-white px-2 py-1 rounded text-xs font-semibold">
													HOT
												</span>
											</div>
										)}
									</div>

									<div class="space-y-2">
										<h3 class="font-medium text-white line-clamp-2 group-hover:text-primary-400 transition-colors text-sm">
											{deal.title}
										</h3>

										<div class="flex items-center space-x-2">
											<span class="text-base font-bold text-primary-400">
												{formatPrice(deal.sale_price)}
											</span>
											{deal.original_price && (
												<span class="text-neutral-500 line-through text-xs">
													{formatPrice(deal.original_price)}
												</span>
											)}
										</div>

										<div class="flex items-center justify-between text-xs">
											<div class="flex items-center space-x-1">
												<span class="text-green-400">✓</span>
												<span class="text-neutral-400">{deal.success_rate}%</span>
											</div>
											<span class="text-neutral-400">{deal.view_count} views</span>
										</div>

										<button class="w-full btn-secondary py-2 rounded-lg font-medium text-sm hover:scale-105 transition-transform">
											Get Deal
										</button>
									</div>
								</div>
							))}
						</div>
					</div>
				</section>
			)}

			<!-- Related Brands Section -->
			{relatedBrands.length > 0 && (
				<section class="py-16">
					<div class="container mx-auto px-4">
						<div class="text-center mb-12">
							<h2 class="text-3xl font-display font-bold text-white mb-4">
								Similar Brands
							</h2>
							<p class="text-neutral-400">
								Discover more brands like {brand.name}
							</p>
						</div>
						
						<div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-6">
							{relatedBrands.map((relatedBrand) => (
								<a 
									href={`/brands/${relatedBrand.unique_name}`}
									class="card p-6 rounded-2xl group hover:scale-105 transition-all duration-300 text-center"
								>
									<div class="relative mb-4">
										<img 
											src={relatedBrand.logo_url || '/images/placeholder-brand.png'} 
											alt={relatedBrand.name}
											class="w-16 h-16 mx-auto object-contain rounded-lg"
											loading="lazy"
										/>
										{relatedBrand.is_verified && (
											<div class="absolute -top-2 -right-2">
												<svg class="w-6 h-6 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
													<path fill-rule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
												</svg>
											</div>
										)}
									</div>
									
									<h3 class="font-semibold text-white text-sm group-hover:text-primary-400 transition-colors">
										{relatedBrand.name}
									</h3>
									
									{relatedBrand.total_deals > 0 && (
										<p class="text-xs text-neutral-400 mt-1">
											{relatedBrand.total_deals} deals
										</p>
									)}
								</a>
							))}
						</div>
					</div>
				</section>
			)}
		</>
	)}

	{error && error !== 'Brand not found' && (
		<section class="py-16">
			<div class="container mx-auto px-4 text-center">
				<div class="card p-8 rounded-2xl max-w-md mx-auto">
					<div class="text-red-400 mb-4">
						<svg class="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
						</svg>
					</div>
					<h3 class="text-xl font-semibold text-white mb-2">Unable to Load Brand</h3>
					<p class="text-neutral-400 mb-4">{error}</p>
					<button class="btn-primary px-6 py-2 rounded-lg" onclick="window.location.reload()">
						Try Again
					</button>
				</div>
			</div>
		</section>
	)}

	<!-- Coupon Modal -->
	<CouponModal />
</Layout>

<script>
	// Add interactive functionality
	document.addEventListener('DOMContentLoaded', function() {
		// Copy coupon code functionality
		const copyButtons = document.querySelectorAll('button:contains("Copy")');
		copyButtons.forEach(button => {
			button.addEventListener('click', function() {
				const codeElement = this.parentElement.querySelector('.font-mono span');
				const code = codeElement.textContent.trim();
				
				if (code && code !== 'GET DEAL') {
					navigator.clipboard.writeText(code).then(() => {
						this.textContent = 'Copied!';
						this.classList.add('bg-green-500');
						
						setTimeout(() => {
							this.textContent = 'Copy';
							this.classList.remove('bg-green-500');
						}, 2000);
					});
				}
			});
		});

		// Deal click tracking
		const dealButtons = document.querySelectorAll('button:contains("Get Deal")');
		dealButtons.forEach(button => {
			button.addEventListener('click', function() {
				// Add click tracking logic here
				console.log('Deal clicked');
			});
		});
	});
</script>
