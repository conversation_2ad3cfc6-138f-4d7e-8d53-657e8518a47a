---
import Layout from '../../layouts/Layout.astro';
import CouponCard from '../../components/coupons/CouponCard.astro';

// Get the brand unique name from the URL
const { uniqueName } = Astro.params;

// Mock brand data - in real app this would come from API
const brand = {
	id: 1,
	name: "Amazon",
	uniqueName: "amazon",
	description: "Amazon is a global e-commerce giant offering millions of products across countless categories. From electronics and books to home goods and fashion, Amazon provides convenient online shopping with fast delivery options.",
	logoUrl: "/images/brands/amazon.png",
	websiteUrl: "https://amazon.com",
	affiliateUrl: "https://amazon.com/?tag=maxcoupon",
	totalCoupons: 156,
	totalDeals: 89,
	averageDiscount: 25.0,
	popularityScore: 98,
	category: "Electronics",
	country: "United States",
	tags: ["electronics", "books", "home", "fashion"]
};

// Mock coupons for this brand
const coupons = [
	{
		id: 1,
		title: "Amazon Prime Day Special - 20% Off Electronics",
		description: "Get 20% off on all electronics during Prime Day sale",
		code: "PRIME20",
		discount: "20%",
		discountType: "percentage",
		brand: { name: "Amazon", logo: "/images/brands/amazon.png" },
		category: "Electronics",
		expiryDate: "2024-12-31",
		isExclusive: true,
		isFeatured: true,
		clickCount: 1250,
		successRate: 95
	},
	{
		id: 2,
		title: "Amazon Books - Buy 2 Get 1 Free",
		description: "Buy any 2 books and get the third one absolutely free",
		code: "BOOKS3FOR2",
		discount: "33%",
		discountType: "buy_one_get_one",
		brand: { name: "Amazon", logo: "/images/brands/amazon.png" },
		category: "Books",
		expiryDate: "2024-12-25",
		isExclusive: false,
		isFeatured: true,
		clickCount: 890,
		successRate: 92
	}
];

// Mock deals for this brand
const deals = [
	{
		id: 1,
		title: "Apple iPhone 15 Pro - Limited Time Deal",
		description: "Latest iPhone 15 Pro with advanced camera system",
		originalPrice: 999,
		salePrice: 849,
		discountPercentage: 15,
		brand: { name: "Amazon" },
		category: "Electronics"
	}
];
---

<Layout 
	title={`${brand.name} Coupons & Deals - MaxCoupon`}
	description={`Find the best ${brand.name} coupons and deals. Save money with verified promo codes and exclusive offers from ${brand.name}.`}
>
	<!-- Brand Header -->
	<div class="relative py-20 overflow-hidden">
		<div class="container mx-auto px-4">
			<div class="max-w-4xl mx-auto">
				<!-- Brand Info -->
				<div class="flex flex-col md:flex-row items-start md:items-center gap-8 mb-8">
					<div class="w-24 h-24 bg-gradient-to-br from-neutral-700 to-neutral-800 rounded-3xl flex items-center justify-center">
						<span class="text-4xl font-bold text-white">
							{brand.name.charAt(0)}
						</span>
					</div>
					
					<div class="flex-1">
						<h1 class="text-4xl md:text-5xl font-display font-bold text-white mb-4">
							{brand.name} <span class="text-gradient">Deals</span>
						</h1>
						<p class="text-xl text-neutral-300 mb-6 leading-relaxed">
							{brand.description}
						</p>
						
						<!-- Tags -->
						<div class="flex flex-wrap gap-2 mb-6">
							{brand.tags.map((tag) => (
								<span class="badge badge-primary text-xs">
									{tag}
								</span>
							))}
						</div>
						
						<!-- Quick Actions -->
						<div class="flex flex-wrap gap-4">
							<a 
								href={brand.websiteUrl}
								target="_blank"
								rel="noopener noreferrer"
								class="btn-primary px-6 py-3 rounded-xl font-medium inline-flex items-center space-x-2"
							>
								<span>Visit Store</span>
								<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path>
								</svg>
							</a>
							<button class="btn-outline px-6 py-3 rounded-xl font-medium inline-flex items-center space-x-2">
								<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
								</svg>
								<span>Follow Brand</span>
							</button>
						</div>
					</div>
				</div>

				<!-- Stats -->
				<div class="grid grid-cols-2 md:grid-cols-4 gap-6">
					<div class="card p-6 text-center">
						<div class="text-3xl font-bold text-primary-400 mb-2">{brand.totalCoupons}</div>
						<div class="text-sm text-neutral-400">Active Coupons</div>
					</div>
					<div class="card p-6 text-center">
						<div class="text-3xl font-bold text-secondary-400 mb-2">{brand.totalDeals}</div>
						<div class="text-sm text-neutral-400">Hot Deals</div>
					</div>
					<div class="card p-6 text-center">
						<div class="text-3xl font-bold text-accent-400 mb-2">{brand.averageDiscount}%</div>
						<div class="text-sm text-neutral-400">Avg Discount</div>
					</div>
					<div class="card p-6 text-center">
						<div class="text-3xl font-bold text-primary-400 mb-2">{brand.popularityScore}</div>
						<div class="text-sm text-neutral-400">Popularity</div>
					</div>
				</div>
			</div>
		</div>
	</div>

	<!-- Main Content -->
	<div class="container mx-auto px-4 py-8">
		<!-- Navigation Tabs -->
		<div class="flex space-x-1 mb-8 bg-white/5 rounded-2xl p-1 max-w-md">
			<button class="tab-btn active flex-1 px-4 py-3 rounded-xl font-medium transition-all" data-tab="coupons">
				Coupons ({brand.totalCoupons})
			</button>
			<button class="tab-btn flex-1 px-4 py-3 rounded-xl font-medium transition-all" data-tab="deals">
				Deals ({brand.totalDeals})
			</button>
		</div>

		<!-- Coupons Section -->
		<div id="coupons-section" class="tab-content">
			<div class="flex items-center justify-between mb-8">
				<h2 class="text-2xl font-display font-bold text-white">
					{brand.name} Coupons
				</h2>
				<select class="bg-white/10 border border-white/20 rounded-xl px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-primary-500">
					<option value="featured">Featured First</option>
					<option value="newest">Newest First</option>
					<option value="expiry">Expiring Soon</option>
					<option value="discount">Highest Discount</option>
				</select>
			</div>

			<div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
				{coupons.map((coupon) => (
					<CouponCard coupon={coupon} />
				))}
			</div>

			{coupons.length === 0 && (
				<div class="text-center py-12">
					<div class="text-6xl mb-4">🎫</div>
					<h3 class="text-xl font-semibold text-white mb-2">No coupons available</h3>
					<p class="text-neutral-400">Check back soon for new {brand.name} coupons!</p>
				</div>
			)}
		</div>

		<!-- Deals Section -->
		<div id="deals-section" class="tab-content hidden">
			<div class="flex items-center justify-between mb-8">
				<h2 class="text-2xl font-display font-bold text-white">
					{brand.name} Deals
				</h2>
				<select class="bg-white/10 border border-white/20 rounded-xl px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-primary-500">
					<option value="featured">Featured First</option>
					<option value="discount">Highest Discount</option>
					<option value="price">Lowest Price</option>
					<option value="newest">Newest First</option>
				</select>
			</div>

			<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
				{deals.map((deal) => (
					<div class="card p-6">
						<h3 class="text-lg font-semibold text-white mb-2">{deal.title}</h3>
						<p class="text-neutral-400 mb-4">{deal.description}</p>
						<div class="flex items-center justify-between mb-4">
							<div class="flex items-center space-x-4">
								<span class="text-2xl font-bold text-green-400">
									${deal.salePrice}
								</span>
								<span class="text-lg text-neutral-500 line-through">
									${deal.originalPrice}
								</span>
							</div>
							<span class="badge badge-secondary">
								{deal.discountPercentage}% off
							</span>
						</div>
						<button class="w-full btn-primary py-3 rounded-xl font-medium">
							View Deal
						</button>
					</div>
				))}
			</div>

			{deals.length === 0 && (
				<div class="text-center py-12">
					<div class="text-6xl mb-4">🏷️</div>
					<h3 class="text-xl font-semibold text-white mb-2">No deals available</h3>
					<p class="text-neutral-400">Check back soon for new {brand.name} deals!</p>
				</div>
			)}
		</div>

		<!-- Load More -->
		<div class="text-center mt-12">
			<button class="btn-outline px-8 py-4 rounded-xl font-semibold">
				Load More
			</button>
		</div>
	</div>

	<!-- About Brand Section -->
	<section class="py-16 relative">
		<div class="container mx-auto px-4">
			<div class="max-w-4xl mx-auto">
				<div class="card p-8">
					<h2 class="text-2xl font-display font-bold text-white mb-6">
						About {brand.name}
					</h2>
					<p class="text-neutral-300 leading-relaxed mb-6">
						{brand.description}
					</p>
					
					<div class="grid grid-cols-1 md:grid-cols-2 gap-8">
						<div>
							<h3 class="text-lg font-semibold text-white mb-4">Quick Facts</h3>
							<ul class="space-y-2 text-neutral-400">
								<li class="flex items-center space-x-2">
									<span class="w-2 h-2 bg-primary-500 rounded-full"></span>
									<span>Category: {brand.category}</span>
								</li>
								<li class="flex items-center space-x-2">
									<span class="w-2 h-2 bg-secondary-500 rounded-full"></span>
									<span>Country: {brand.country}</span>
								</li>
								<li class="flex items-center space-x-2">
									<span class="w-2 h-2 bg-accent-500 rounded-full"></span>
									<span>Popularity Score: {brand.popularityScore}/100</span>
								</li>
							</ul>
						</div>
						
						<div>
							<h3 class="text-lg font-semibold text-white mb-4">Savings Summary</h3>
							<ul class="space-y-2 text-neutral-400">
								<li class="flex items-center space-x-2">
									<span class="w-2 h-2 bg-primary-500 rounded-full"></span>
									<span>Average Discount: {brand.averageDiscount}%</span>
								</li>
								<li class="flex items-center space-x-2">
									<span class="w-2 h-2 bg-secondary-500 rounded-full"></span>
									<span>Total Offers: {brand.totalCoupons + brand.totalDeals}</span>
								</li>
								<li class="flex items-center space-x-2">
									<span class="w-2 h-2 bg-accent-500 rounded-full"></span>
									<span>Updated: Daily</span>
								</li>
							</ul>
						</div>
					</div>
				</div>
			</div>
		</div>
	</section>
</Layout>

<script>
	document.addEventListener('DOMContentLoaded', () => {
		// Tab functionality
		const tabBtns = document.querySelectorAll('.tab-btn');
		const tabContents = document.querySelectorAll('.tab-content');
		
		tabBtns.forEach(btn => {
			btn.addEventListener('click', () => {
				const tabName = btn.getAttribute('data-tab');
				
				// Update active tab
				tabBtns.forEach(b => b.classList.remove('active'));
				btn.classList.add('active');
				
				// Show corresponding content
				tabContents.forEach(content => {
					if (content.id === `${tabName}-section`) {
						content.classList.remove('hidden');
					} else {
						content.classList.add('hidden');
					}
				});
			});
		});

		// Track brand page visit
		try {
			fetch('/api/analytics/brand-view', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
				},
				body: JSON.stringify({
					brand_name: '{brand.name}',
					unique_name: '{brand.uniqueName}',
					user_agent: navigator.userAgent,
					referrer: document.referrer,
					timestamp: new Date().toISOString()
				})
			});
		} catch (error) {
			console.error('Failed to track brand view:', error);
		}
	});
</script>

<style>
	.tab-btn.active {
		background: linear-gradient(135deg, #d946ef, #c026d3);
		color: white;
	}
	
	.tab-btn:not(.active) {
		color: #a3a3a3;
	}
	
	.tab-btn:not(.active):hover {
		color: #d4d4d4;
		background: rgba(255, 255, 255, 0.05);
	}
</style>
