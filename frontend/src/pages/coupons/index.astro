---
import Layout from '../../layouts/Layout.astro';
import CouponCard from '../../components/coupons/CouponCard.astro';
import FilterSidebar from '../../components/filters/FilterSidebar.astro';

// Mock coupons data - in real app this would come from API
const coupons = [
	{
		id: 1,
		title: "Amazon Prime Day Special - 20% Off Electronics",
		description: "Get 20% off on all electronics during Prime Day sale",
		code: "PRIME20",
		discount: "20%",
		discountType: "percentage",
		brand: { name: "Amazon", logo: "/images/brands/amazon.png" },
		category: "Electronics",
		expiryDate: "2024-12-31",
		isExclusive: true,
		isFeatured: true,
		clickCount: 1250,
		successRate: 95
	},
	{
		id: 2,
		title: "Nike Summer Sale - 25% Off Athletic Wear",
		description: "Get 25% off on all athletic wear and footwear",
		code: "SUMMER25",
		discount: "25%",
		discountType: "percentage",
		brand: { name: "<PERSON>", logo: "/images/brands/nike.png" },
		category: "Sports",
		expiryDate: "2024-12-25",
		isExclusive: false,
		isFeatured: true,
		clickCount: 890,
		successRate: 92
	},
	{
		id: 3,
		title: "H&M New Collection - $10 Off $50",
		description: "Get $10 off when you spend $50 or more on new collection",
		code: "NEW10",
		discount: "$10",
		discountType: "fixed_amount",
		brand: { name: "H&M", logo: "/images/brands/hm.png" },
		category: "Fashion",
		expiryDate: "2024-12-20",
		isExclusive: true,
		isFeatured: true,
		clickCount: 567,
		successRate: 88
	},
	{
		id: 4,
		title: "IKEA Kitchen Sale - 20% Off Kitchen Items",
		description: "Save 20% on all kitchen furniture and accessories",
		code: "KITCHEN20",
		discount: "20%",
		discountType: "percentage",
		brand: { name: "IKEA", logo: "/images/brands/ikea.png" },
		category: "Home",
		expiryDate: "2024-12-30",
		isExclusive: false,
		isFeatured: false,
		clickCount: 423,
		successRate: 90
	},
	{
		id: 5,
		title: "Adidas Flash Sale - 30% Off Sneakers",
		description: "Limited time 30% off on all sneakers and running shoes",
		code: "FLASH30",
		discount: "30%",
		discountType: "percentage",
		brand: { name: "Adidas", logo: "/images/brands/adidas.png" },
		category: "Sports",
		expiryDate: "2024-12-15",
		isExclusive: true,
		isFeatured: true,
		clickCount: 756,
		successRate: 93
	},
	{
		id: 6,
		title: "Apple Student Discount - $100 Off MacBook",
		description: "Students get $100 off on MacBook Air and MacBook Pro",
		code: "STUDENT100",
		discount: "$100",
		discountType: "fixed_amount",
		brand: { name: "Apple", logo: "/images/brands/apple.png" },
		category: "Electronics",
		expiryDate: "2024-12-31",
		isExclusive: false,
		isFeatured: false,
		clickCount: 234,
		successRate: 85
	}
];

const totalCoupons = coupons.length;
---

<Layout 
	title="Premium Coupons & Promo Codes - MaxCoupon"
	description="Discover thousands of verified coupons and promo codes from top brands. Save money with exclusive deals and instant discounts."
>
	<!-- Page Header -->
	<div class="relative py-20 overflow-hidden">
		<div class="container mx-auto px-4">
			<div class="max-w-4xl mx-auto text-center">
				<h1 class="text-5xl md:text-6xl font-display font-bold mb-6">
					Premium <span class="text-gradient">Coupons</span>
				</h1>
				<p class="text-xl text-neutral-300 mb-8 max-w-2xl mx-auto">
					Discover thousands of verified coupons and promo codes from the world's top brands. 
					Save money with exclusive deals and instant discounts.
				</p>
				
				<!-- Quick Stats -->
				<div class="flex flex-wrap justify-center gap-8 text-sm">
					<div class="flex items-center space-x-2">
						<div class="w-2 h-2 bg-primary-500 rounded-full"></div>
						<span class="text-neutral-400">All codes verified</span>
					</div>
					<div class="flex items-center space-x-2">
						<div class="w-2 h-2 bg-secondary-500 rounded-full"></div>
						<span class="text-neutral-400">Updated daily</span>
					</div>
					<div class="flex items-center space-x-2">
						<div class="w-2 h-2 bg-accent-500 rounded-full"></div>
						<span class="text-neutral-400">Instant savings</span>
					</div>
				</div>
			</div>
		</div>
	</div>

	<!-- Main Content -->
	<div class="container mx-auto px-4 py-8">
		<div class="flex flex-col lg:flex-row gap-8">
			<!-- Sidebar Filters -->
			<div class="lg:w-1/4">
				<FilterSidebar />
			</div>

			<!-- Coupons List -->
			<div class="lg:w-3/4">
				<!-- Results Header -->
				<div class="flex flex-col sm:flex-row items-start sm:items-center justify-between mb-8 gap-4">
					<div class="text-neutral-400">
						Showing <span class="font-semibold text-white">{totalCoupons}</span> premium coupons
					</div>
					
					<!-- Sort Options -->
					<div class="flex items-center space-x-4">
						<select class="bg-white/10 border border-white/20 rounded-xl px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent backdrop-blur-sm">
							<option value="featured">Featured First</option>
							<option value="newest">Newest First</option>
							<option value="expiry">Expiring Soon</option>
							<option value="discount">Highest Discount</option>
							<option value="popularity">Most Popular</option>
							<option value="success">Highest Success Rate</option>
						</select>

						<!-- View Toggle -->
						<div class="flex items-center space-x-2 bg-white/5 rounded-xl p-1">
							<button class="view-toggle active p-2 rounded-lg transition-all" data-view="grid">
								<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z"></path>
								</svg>
							</button>
							<button class="view-toggle p-2 rounded-lg transition-all" data-view="list">
								<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 10h16M4 14h16M4 18h16"></path>
								</svg>
							</button>
						</div>
					</div>
				</div>

				<!-- Featured Coupons Banner -->
				<div class="card p-6 mb-8 bg-gradient-to-r from-primary-500/20 to-secondary-500/20 border-primary-500/30">
					<div class="flex items-center justify-between">
						<div>
							<h2 class="text-xl font-semibold text-white mb-2">
								🔥 Featured Premium Coupons
							</h2>
							<p class="text-neutral-300">
								Hand-picked exclusive deals with the highest savings
							</p>
						</div>
						<div class="text-right">
							<div class="text-2xl font-bold text-primary-400">
								{coupons.filter(c => c.isFeatured).length}
							</div>
							<div class="text-sm text-neutral-400">Featured</div>
						</div>
					</div>
				</div>

				<!-- Coupons Grid -->
				<div id="coupons-grid" class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
					{coupons.map((coupon) => (
						<CouponCard coupon={coupon} />
					))}
				</div>

				<!-- Coupons List View (Hidden by default) -->
				<div id="coupons-list" class="hidden space-y-4 mb-8">
					{coupons.map((coupon) => (
						<div class="card p-6 flex items-center justify-between">
							<div class="flex items-center space-x-4 flex-1">
								<div class="w-12 h-12 bg-gradient-to-br from-neutral-700 to-neutral-800 rounded-xl flex items-center justify-center">
									<span class="text-lg font-bold text-white">
										{coupon.brand.name.charAt(0)}
									</span>
								</div>
								<div class="flex-1">
									<h3 class="text-lg font-semibold text-white mb-1">{coupon.title}</h3>
									<p class="text-neutral-400 text-sm">{coupon.description}</p>
								</div>
							</div>
							<div class="flex items-center space-x-4">
								<div class="text-center">
									<div class="text-xl font-bold text-primary-400">{coupon.discount}</div>
									<div class="text-xs text-neutral-500">Save</div>
								</div>
								<div class="text-center">
									<div class="text-lg font-semibold text-white">{coupon.successRate}%</div>
									<div class="text-xs text-neutral-500">Success</div>
								</div>
								<button class="btn-primary px-6 py-3 rounded-xl font-medium">
									Get Code
								</button>
							</div>
						</div>
					))}
				</div>

				<!-- Pagination -->
				<div class="flex items-center justify-between">
					<div class="text-neutral-400 text-sm">
						Showing 1-{totalCoupons} of {totalCoupons} coupons
					</div>
					
					<div class="flex items-center space-x-2">
						<button class="p-2 rounded-lg text-neutral-400 hover:text-white hover:bg-white/10 transition-colors" disabled>
							<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
							</svg>
						</button>
						<span class="px-4 py-2 bg-primary-600 text-white rounded-lg font-medium">1</span>
						<button class="p-2 rounded-lg text-neutral-400 hover:text-white hover:bg-white/10 transition-colors">
							<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
							</svg>
						</button>
					</div>
				</div>
			</div>
		</div>
	</div>

	<!-- Newsletter Section -->
	<section class="py-16 relative">
		<div class="container mx-auto px-4">
			<div class="max-w-3xl mx-auto text-center">
				<div class="card p-8">
					<h2 class="text-3xl font-display font-bold text-white mb-4">
						Never Miss a <span class="text-gradient">Premium Deal</span>
					</h2>
					<p class="text-neutral-400 mb-6">
						Get the latest coupons and exclusive deals delivered to your inbox
					</p>
					
					<div class="flex flex-col sm:flex-row gap-4 max-w-md mx-auto">
						<input 
							type="email" 
							placeholder="Enter your email"
							class="flex-1 px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-neutral-400 focus:outline-none focus:ring-2 focus:ring-primary-500"
						/>
						<button class="btn-primary px-6 py-3 rounded-xl font-medium whitespace-nowrap">
							Subscribe
						</button>
					</div>
				</div>
			</div>
		</div>
	</section>
</Layout>

<script>
	document.addEventListener('DOMContentLoaded', () => {
		// View toggle functionality
		const viewToggles = document.querySelectorAll('.view-toggle');
		const gridView = document.getElementById('coupons-grid');
		const listView = document.getElementById('coupons-list');
		
		viewToggles.forEach(toggle => {
			toggle.addEventListener('click', () => {
				const view = toggle.getAttribute('data-view');
				
				// Update active toggle
				viewToggles.forEach(t => t.classList.remove('active'));
				toggle.classList.add('active');
				
				// Switch views
				if (view === 'grid') {
					gridView?.classList.remove('hidden');
					listView?.classList.add('hidden');
				} else {
					gridView?.classList.add('hidden');
					listView?.classList.remove('hidden');
				}
			});
		});

		// Sort functionality
		const sortSelect = document.querySelector('select');
		sortSelect?.addEventListener('change', (e) => {
			const sortBy = e.target.value;
			console.log('Sorting by:', sortBy);
			// In a real app, this would trigger a new API call or client-side sorting
		});

		// Track page view
		try {
			fetch('/api/analytics/page-view', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
				},
				body: JSON.stringify({
					page: 'coupons',
					user_agent: navigator.userAgent,
					referrer: document.referrer,
					timestamp: new Date().toISOString()
				})
			});
		} catch (error) {
			console.error('Failed to track page view:', error);
		}
	});
</script>

<style>
	.view-toggle.active {
		background: linear-gradient(135deg, #d946ef, #c026d3);
		color: white;
	}
	
	.view-toggle:not(.active) {
		color: #a3a3a3;
	}
	
	.view-toggle:not(.active):hover {
		color: #d4d4d4;
		background: rgba(255, 255, 255, 0.05);
	}
</style>
