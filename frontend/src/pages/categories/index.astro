---
import Layout from '../../layouts/Layout.astro';

const API_BASE_URL = import.meta.env.PUBLIC_API_BASE_URL || 'http://127.0.0.1:8080';

// Fetch categories from API
let categories = [];
let error = null;

try {
	const response = await fetch(`${API_BASE_URL}/api/v1/categories`);
	if (response.ok) {
		const data = await response.json();
		categories = data.data || [];
	} else {
		error = 'Failed to load categories';
	}
} catch (e) {
	error = 'Network error';
	console.error('Error fetching categories:', e);
}
---

<Layout 
	title="All Categories - MaxCoupon"
	description="Browse all coupon and deal categories. Find discounts organized by category including electronics, fashion, travel, food, and more."
	keywords="categories, coupons by category, deals by category, discount categories, shopping categories"
>
	<!-- Page Header -->
	<section class="relative py-20 overflow-hidden">
		<div class="container mx-auto px-4">
			<div class="max-w-4xl mx-auto text-center">
				<h1 class="text-5xl md:text-6xl font-bold mb-6">
					Browse by <span class="text-gradient">Category</span>
				</h1>
				<p class="text-xl text-gray-600 mb-8 leading-relaxed">
					Discover amazing deals and coupons organized by category. Find exactly what you're looking for.
				</p>
			</div>
		</div>
	</section>

	<!-- Categories Grid -->
	<section class="py-16">
		<div class="container mx-auto px-4">
			{error ? (
				<div class="text-center py-12">
					<div class="text-red-400 mb-4">
						<svg class="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
						</svg>
					</div>
					<h3 class="text-xl font-semibold text-gray-900 mb-2">Unable to Load Categories</h3>
					<p class="text-gray-600 mb-4">{error}</p>
					<button class="btn-primary px-6 py-2 rounded-lg" onclick="window.location.reload()">
						Try Again
					</button>
				</div>
			) : categories.length > 0 ? (
				<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
					{categories.map((category) => (
						<a 
							href={`/categories/${category.slug}`}
							class="card group p-6 hover:scale-105 transition-all duration-300"
						>
							<!-- Category Icon -->
							<div class="flex items-center justify-center w-16 h-16 mx-auto mb-4 rounded-xl bg-gradient-to-br from-purple-100 to-pink-100 group-hover:from-purple-200 group-hover:to-pink-200 transition-colors">
								{category.icon ? (
									<span class="text-2xl">{category.icon}</span>
								) : (
									<svg class="w-8 h-8 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
										<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
									</svg>
								)}
							</div>

							<!-- Category Info -->
							<div class="text-center">
								<h3 class="text-lg font-semibold text-gray-900 mb-2 group-hover:text-purple-600 transition-colors">
									{category.name}
								</h3>
								{category.description && (
									<p class="text-sm text-gray-600 mb-4 line-clamp-2">
										{category.description}
									</p>
								)}
								
								<!-- Stats -->
								<div class="flex items-center justify-center space-x-4 text-xs text-gray-500">
									<div class="flex items-center space-x-1">
										<svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
											<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.997 1.997 0 013 12V7a4 4 0 014-4z"></path>
										</svg>
										<span>Coupons</span>
									</div>
									<div class="flex items-center space-x-1">
										<svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
											<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
										</svg>
										<span>Deals</span>
									</div>
								</div>
							</div>

							<!-- Hover Arrow -->
							<div class="flex items-center justify-center mt-4 opacity-0 group-hover:opacity-100 transition-opacity">
								<svg class="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
								</svg>
							</div>
						</a>
					))}
				</div>
			) : (
				<div class="text-center py-12">
					<div class="text-6xl mb-4">📂</div>
					<h3 class="text-xl font-semibold text-gray-900 mb-2">No Categories Found</h3>
					<p class="text-gray-600">Categories will appear here once they are added.</p>
				</div>
			)}
		</div>
	</section>

	<!-- Popular Categories Section -->
	{categories.length > 0 && (
		<section class="py-16 bg-gray-50">
			<div class="container mx-auto px-4">
				<div class="text-center mb-12">
					<h2 class="text-3xl font-bold text-gray-900 mb-4">
						Popular Categories
					</h2>
					<p class="text-gray-600">
						Most searched categories with the best deals
					</p>
				</div>

				<div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
					{categories.slice(0, 12).map((category) => (
						<a 
							href={`/categories/${category.slug}`}
							class="bg-white rounded-lg p-4 text-center hover:shadow-lg transition-shadow group"
						>
							<div class="w-12 h-12 mx-auto mb-2 rounded-lg bg-gradient-to-br from-purple-100 to-pink-100 flex items-center justify-center group-hover:from-purple-200 group-hover:to-pink-200 transition-colors">
								{category.icon ? (
									<span class="text-lg">{category.icon}</span>
								) : (
									<svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
										<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
									</svg>
								)}
							</div>
							<h4 class="text-sm font-medium text-gray-900 group-hover:text-purple-600 transition-colors">
								{category.name}
							</h4>
						</a>
					))}
				</div>
			</div>
		</section>
	)}

	<!-- CTA Section -->
	<section class="py-16">
		<div class="container mx-auto px-4">
			<div class="bg-gradient-to-r from-purple-600 to-pink-600 rounded-2xl p-8 text-center text-white">
				<h2 class="text-3xl font-bold mb-4">
					Can't Find What You're Looking For?
				</h2>
				<p class="text-lg mb-6 opacity-90">
					Use our search feature to find specific brands, products, or deals
				</p>
				<a href="/search" class="btn-secondary inline-flex items-center px-8 py-3">
					<svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
					</svg>
					Search Now
				</a>
			</div>
		</div>
	</section>
</Layout>

<style>
	.line-clamp-2 {
		display: -webkit-box;
		-webkit-line-clamp: 2;
		-webkit-box-orient: vertical;
		overflow: hidden;
	}
</style>
