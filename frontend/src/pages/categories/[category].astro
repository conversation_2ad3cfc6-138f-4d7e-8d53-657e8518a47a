---
import Layout from '../../layouts/Layout.astro';
import CouponModal from '../../components/CouponModal.astro';
import DealModal from '../../components/DealModal.astro';

const API_BASE_URL = import.meta.env.PUBLIC_API_URL || 'http://127.0.0.1:8080';

// Get category slug from URL
const { category: categorySlug } = Astro.params;

// Fetch category data
let category = null;
let categoryCoupons = [];
let categoryDeals = [];
let categoryBrands = [];
let error = null;

try {
	// Fetch category details using slug
	const categoryResponse = await fetch(`${API_BASE_URL}/api/v1/categories/slug/${categorySlug}`);
	if (categoryResponse.ok) {
		const categoryData = await categoryResponse.json();
		category = categoryData.data;
		
		// Fetch coupons for this category
		const couponsResponse = await fetch(`${API_BASE_URL}/api/v1/categories/${category.id}/coupons?limit=20`);
		if (couponsResponse.ok) {
			const couponsData = await couponsResponse.json();
			categoryCoupons = couponsData.data || [];
		}
		
		// Fetch deals for this category
		const dealsResponse = await fetch(`${API_BASE_URL}/api/v1/categories/${category.id}/deals?limit=20`);
		if (dealsResponse.ok) {
			const dealsData = await dealsResponse.json();
			categoryDeals = dealsData.data || [];
		}
		
		// Fetch brands for this category
		const brandsResponse = await fetch(`${API_BASE_URL}/api/v1/categories/${category.id}/brands?limit=12`);
		if (brandsResponse.ok) {
			const brandsData = await brandsResponse.json();
			categoryBrands = brandsData.data || [];
		}
	} else {
		error = 'Category not found';
	}
} catch (e) {
	error = 'Network error';
	console.error('Error fetching category data:', e);
}

// Helper functions
function formatDiscount(coupon) {
	if (coupon.discount_type === 'percentage') {
		return `${coupon.discount_value}% OFF`;
	} else if (coupon.discount_type === 'fixed') {
		return `$${coupon.discount_value} OFF`;
	} else {
		return coupon.discount_amount || 'DEAL';
	}
}

function formatPrice(price) {
	return price ? `$${parseFloat(price).toFixed(2)}` : 'N/A';
}
---

<Layout 
	title={category ? `${category.name} Coupons & Deals - MaxCoupon` : 'Category Not Found - MaxCoupon'}
	description={category ? `Find the best ${category.name.toLowerCase()} coupons and deals. ${category.description || `Save money on ${category.name.toLowerCase()} with verified promo codes and discounts.`}` : 'Category not found'}
	keywords={category ? `${category.name.toLowerCase()}, ${category.name.toLowerCase()} coupons, ${category.name.toLowerCase()} deals, ${category.name.toLowerCase()} discounts` : 'category not found'}
>
	{category ? (
		<>
			<!-- Category Header -->
			<section class="relative py-20 overflow-hidden">
				<div class="container mx-auto px-4">
					<div class="max-w-4xl mx-auto text-center">
						<!-- Category Icon -->
						<div class="flex items-center justify-center w-20 h-20 mx-auto mb-6 rounded-2xl bg-gradient-to-br from-purple-100 to-pink-100">
							{category.icon ? (
								<span class="text-3xl">{category.icon}</span>
							) : (
								<svg class="w-10 h-10 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
								</svg>
							)}
						</div>
						
						<h1 class="text-5xl md:text-6xl font-bold mb-6">
							{category.name} <span class="text-gradient">Deals</span>
						</h1>
						
						{category.description && (
							<p class="text-xl text-gray-600 mb-8 leading-relaxed">
								{category.description}
							</p>
						)}
						
						<!-- Stats -->
						<div class="flex items-center justify-center space-x-8 text-sm text-gray-600">
							<div class="flex items-center space-x-2">
								<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.997 1.997 0 013 12V7a4 4 0 014-4z"></path>
								</svg>
								<span>{categoryCoupons.length} Coupons</span>
							</div>
							<div class="flex items-center space-x-2">
								<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
								</svg>
								<span>{categoryDeals.length} Deals</span>
							</div>
							<div class="flex items-center space-x-2">
								<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
								</svg>
								<span>{categoryBrands.length} Brands</span>
							</div>
						</div>
					</div>
				</div>
			</section>

			<!-- Navigation Tabs -->
			<section class="py-8 border-b border-gray-200">
				<div class="container mx-auto px-4">
					<div class="flex items-center justify-center space-x-8">
						<button class="tab-btn active" data-tab="coupons">
							<svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.997 1.997 0 013 12V7a4 4 0 014-4z"></path>
							</svg>
							Coupons ({categoryCoupons.length})
						</button>
						<button class="tab-btn" data-tab="deals">
							<svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
							</svg>
							Deals ({categoryDeals.length})
						</button>
						<button class="tab-btn" data-tab="brands">
							<svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
							</svg>
							Brands ({categoryBrands.length})
						</button>
					</div>
				</div>
			</section>

			<!-- Coupons Tab -->
			<section id="coupons-tab" class="tab-content py-16">
				<div class="container mx-auto px-4">
					{categoryCoupons.length > 0 ? (
						<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
							{categoryCoupons.map((coupon) => (
								<div class="card p-6 group hover:scale-105 transition-all duration-300">
									<!-- Brand Info -->
									<div class="flex items-center space-x-3 mb-4">
										<img 
											src={coupon.brand?.logo_url || '/images/placeholder-brand.svg'} 
											alt={coupon.brand?.name}
											class="w-12 h-12 rounded-lg object-contain bg-gray-50 p-1"
										/>
										<div class="flex-1">
											<h3 class="font-semibold text-gray-900 text-sm">{coupon.brand?.name}</h3>
											<span class="text-xs text-purple-600 font-medium">
												{formatDiscount(coupon)}
											</span>
										</div>
									</div>

									<!-- Coupon Details -->
									<div class="mb-4">
										<h4 class="font-medium text-gray-900 mb-2 line-clamp-2">
											{coupon.title}
										</h4>
										{coupon.description && (
											<p class="text-sm text-gray-600 line-clamp-2">
												{coupon.description}
											</p>
										)}
									</div>

									<!-- Action Button -->
									<button 
										class="w-full bg-purple-600 hover:bg-purple-700 text-white py-2 px-4 rounded-lg font-semibold text-sm transition-colors"
										onclick={`showCouponModal('${coupon.id}', '${coupon.code}', '${coupon.title}', '${coupon.brand?.name || 'Brand'}', '${coupon.brand?.affiliate_url || coupon.brand?.website_url || '#'}', '${coupon.brand?.logo_url || '/images/placeholder-brand.svg'}', '${coupon.discount_amount || coupon.title}')`}
									>
										Get Code
									</button>
								</div>
							))}
						</div>
					) : (
						<div class="text-center py-12">
							<div class="text-6xl mb-4">🎫</div>
							<h3 class="text-xl font-semibold text-gray-900 mb-2">No Coupons Found</h3>
							<p class="text-gray-600">No coupons available in this category yet.</p>
						</div>
					)}
				</div>
			</section>

			<!-- Deals Tab -->
			<section id="deals-tab" class="tab-content py-16 hidden">
				<div class="container mx-auto px-4">
					{categoryDeals.length > 0 ? (
						<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
							{categoryDeals.map((deal) => (
								<div class="card p-6 group hover:scale-105 transition-all duration-300">
									<!-- Deal Image -->
									<div class="relative mb-4">
										<img
											src={deal.image_url || '/images/placeholder-deal.jpg'}
											alt={deal.title}
											class="w-full h-40 object-cover rounded-lg"
											loading="lazy"
										/>
										<div class="absolute top-2 right-2">
											<span class="bg-red-500 text-white px-2 py-1 rounded text-xs font-bold">
												{deal.discount_percentage}% OFF
											</span>
										</div>
									</div>

									<!-- Deal Info -->
									<div class="mb-4">
										<h4 class="font-medium text-gray-900 mb-2 line-clamp-2">
											{deal.title}
										</h4>
										<div class="flex items-center space-x-2 mb-2">
											<span class="text-lg font-bold text-green-600">
												{formatPrice(deal.sale_price)}
											</span>
											{deal.original_price && (
												<span class="text-gray-500 line-through text-sm">
													{formatPrice(deal.original_price)}
												</span>
											)}
										</div>
									</div>

									<!-- Action Button -->
									<button 
										class="w-full bg-purple-600 hover:bg-purple-700 text-white py-2 px-4 rounded-lg font-semibold text-sm transition-colors get-deal-btn"
										data-deal-id={deal.id}
										data-title={deal.title}
										data-brand={deal.brand?.name || 'Brand'}
										data-store-url={deal.brand?.affiliate_url || deal.brand?.website_url || '#'}
										data-brand-logo={deal.brand?.logo_url || '/images/placeholder-brand.svg'}
										data-description={deal.description || deal.title}
										data-original-price={deal.original_price || 99.99}
										data-deal-price={deal.sale_price || 49.99}
										data-discount-percentage={deal.discount_percentage || 50}
									>
										Get Deal
									</button>
								</div>
							))}
						</div>
					) : (
						<div class="text-center py-12">
							<div class="text-6xl mb-4">🛍️</div>
							<h3 class="text-xl font-semibold text-gray-900 mb-2">No Deals Found</h3>
							<p class="text-gray-600">No deals available in this category yet.</p>
						</div>
					)}
				</div>
			</section>

			<!-- Brands Tab -->
			<section id="brands-tab" class="tab-content py-16 hidden">
				<div class="container mx-auto px-4">
					{categoryBrands.length > 0 ? (
						<div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6 gap-6">
							{categoryBrands.map((brand) => (
								<a 
									href={`/brands/${brand.unique_name}`}
									class="card p-4 text-center group hover:scale-105 transition-all duration-300"
								>
									<img 
										src={brand.logo_url || '/images/placeholder-brand.svg'} 
										alt={brand.name}
										class="w-16 h-16 mx-auto mb-3 object-contain rounded-lg bg-gray-50 p-2"
									/>
									<h4 class="font-medium text-gray-900 text-sm group-hover:text-purple-600 transition-colors">
										{brand.name}
									</h4>
									<p class="text-xs text-gray-500 mt-1">
										{brand.total_coupons || 0} coupons
									</p>
								</a>
							))}
						</div>
					) : (
						<div class="text-center py-12">
							<div class="text-6xl mb-4">🏪</div>
							<h3 class="text-xl font-semibold text-gray-900 mb-2">No Brands Found</h3>
							<p class="text-gray-600">No brands available in this category yet.</p>
						</div>
					)}
				</div>
			</section>
		</>
	) : (
		<!-- Category Not Found -->
		<section class="py-20">
			<div class="container mx-auto px-4 text-center">
				<div class="max-w-md mx-auto">
					<div class="text-6xl mb-6">😕</div>
					<h1 class="text-3xl font-bold text-gray-900 mb-4">Category Not Found</h1>
					<p class="text-gray-600 mb-8">
						{error === 'Category not found' 
							? "The category you're looking for doesn't exist or has been moved."
							: "We're having trouble loading this category. Please try again later."
						}
					</p>
					<div class="space-x-4">
						<a href="/categories" class="btn-primary px-6 py-3">
							Browse Categories
						</a>
						<button onclick="window.history.back()" class="btn-secondary px-6 py-3">
							Go Back
						</button>
					</div>
				</div>
			</div>
		</section>
	)}

	<!-- Coupon Modal -->
	<CouponModal />
	
	<!-- Deal Modal -->
	<DealModal />
</Layout>

<script>
	// Tab functionality
	document.addEventListener('DOMContentLoaded', function() {
		const tabButtons = document.querySelectorAll('.tab-btn');
		const tabContents = document.querySelectorAll('.tab-content');

		tabButtons.forEach(button => {
			button.addEventListener('click', function() {
				const targetTab = this.getAttribute('data-tab');

				// Remove active class from all buttons
				tabButtons.forEach(btn => btn.classList.remove('active'));
				// Add active class to clicked button
				this.classList.add('active');

				// Hide all tab contents
				tabContents.forEach(content => content.classList.add('hidden'));
				// Show target tab content
				document.getElementById(`${targetTab}-tab`).classList.remove('hidden');
			});
		});

		// Deal button functionality
		const dealButtons = document.querySelectorAll('.get-deal-btn');
		dealButtons.forEach(button => {
			button.addEventListener('click', function() {
				const dealId = button.getAttribute('data-deal-id');
				const title = button.getAttribute('data-title');
				const brand = button.getAttribute('data-brand');
				const storeUrl = button.getAttribute('data-store-url');
				const brandLogo = button.getAttribute('data-brand-logo');
				const description = button.getAttribute('data-description');
				const originalPrice = parseFloat(button.getAttribute('data-original-price'));
				const dealPrice = parseFloat(button.getAttribute('data-deal-price'));
				const discountPercentage = parseInt(button.getAttribute('data-discount-percentage'));

				// Show deal modal
				if (typeof showDealModal === 'function') {
					showDealModal(dealId, title, brand, storeUrl, brandLogo, description, originalPrice, dealPrice, discountPercentage);
				} else {
					console.error('showDealModal function not found');
					// Fallback: direct redirect
					if (storeUrl && storeUrl !== '#') {
						window.open(storeUrl, '_blank');
					}
				}
			});
		});
	});
</script>

<style>
	.tab-btn {
		@apply flex items-center px-6 py-3 text-gray-600 font-medium border-b-2 border-transparent hover:text-purple-600 hover:border-purple-200 transition-colors;
	}
	
	.tab-btn.active {
		@apply text-purple-600 border-purple-600;
	}
	
	.line-clamp-2 {
		display: -webkit-box;
		-webkit-line-clamp: 2;
		-webkit-box-orient: vertical;
		overflow: hidden;
	}
</style>
