---
import Layout from '../layouts/Layout.astro';

const API_BASE_URL = import.meta.env.PUBLIC_API_URL || 'http://127.0.0.1:8080';

// Fetch brands from API
let brands = [];
let featuredBrands = [];
let error = null;

try {
	// Fetch all brands
	const response = await fetch(`${API_BASE_URL}/api/v1/brands?limit=50`);
	if (response.ok) {
		const data = await response.json();
		brands = data.data || [];
		
		// Separate featured brands
		featuredBrands = brands.filter(brand => brand.is_featured).slice(0, 8);
	}
} catch (err) {
	console.error('Error fetching brands:', err);
	error = 'Network error';
}

// Group brands by category
const brandsByCategory = brands.reduce((acc, brand) => {
	const category = brand.category || 'Other';
	if (!acc[category]) {
		acc[category] = [];
	}
	acc[category].push(brand);
	return acc;
}, {});
---

<Layout 
	title="Premium Brands - MaxCoupon"
	description="Explore deals from the world's most prestigious brands. Find exclusive offers from top retailers and luxury brands."
>
	<!-- Hero Section -->
	<section class="relative py-20 overflow-hidden">
		<div class="absolute inset-0 bg-gradient-to-br from-purple-900/20 via-blue-900/20 to-indigo-900/20"></div>
		<div class="container mx-auto px-4 relative z-10">
			<div class="text-center max-w-4xl mx-auto">
				<h1 class="text-5xl md:text-6xl font-display font-bold text-white mb-6">
					Premium <span class="text-gradient">Brands</span>
				</h1>
				<p class="text-xl text-neutral-300 mb-8 max-w-2xl mx-auto">
					Discover exclusive deals from the world's most prestigious brands and retailers
				</p>
				
				<!-- Search Bar -->
				<div class="max-w-2xl mx-auto">
					<div class="relative">
						<input 
							type="text" 
							placeholder="Search brands..."
							class="w-full px-6 py-4 pl-12 bg-white/10 border border-white/20 rounded-2xl text-white placeholder-neutral-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent backdrop-blur-sm"
						/>
						<svg class="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-neutral-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
						</svg>
					</div>
				</div>
			</div>
		</div>
	</section>

	<!-- Featured Brands Section -->
	{featuredBrands.length > 0 && (
		<section class="py-16">
			<div class="container mx-auto px-4">
				<div class="text-center mb-12">
					<h2 class="text-3xl font-display font-bold text-white mb-4">
						✨ Featured Brands
					</h2>
					<p class="text-neutral-400 max-w-2xl mx-auto">
						Top brands with the best deals and exclusive offers
					</p>
				</div>
				
				<div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-8 gap-6">
					{featuredBrands.map((brand) => (
						<a 
							href={`/brands/${brand.unique_name}`}
							class="card p-6 rounded-2xl group hover:scale-105 transition-all duration-300 text-center"
						>
							<div class="relative mb-4">
								<img 
									src={brand.logo_url || '/images/placeholder-brand.png'} 
									alt={brand.name}
									class="w-16 h-16 mx-auto object-contain rounded-lg"
									loading="lazy"
								/>
								{brand.is_verified && (
									<div class="absolute -top-2 -right-2">
										<svg class="w-6 h-6 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
											<path fill-rule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
										</svg>
									</div>
								)}
							</div>
							
							<h3 class="font-semibold text-white text-sm group-hover:text-primary-400 transition-colors">
								{brand.name}
							</h3>
							
							{brand.deal_count > 0 && (
								<p class="text-xs text-neutral-400 mt-1">
									{brand.deal_count} deals
								</p>
							)}
						</a>
					))}
				</div>
			</div>
		</section>
	)}

	<!-- Brand Categories -->
	{Object.keys(brandsByCategory).length > 0 && (
		<section class="py-16">
			<div class="container mx-auto px-4">
				<div class="text-center mb-12">
					<h2 class="text-3xl font-display font-bold text-white mb-4">
						Browse by Category
					</h2>
					<p class="text-neutral-400">
						Find brands organized by category
					</p>
				</div>
				
				{Object.entries(brandsByCategory).map(([category, categoryBrands]) => (
					<div class="mb-12">
						<h3 class="text-2xl font-bold text-white mb-6 flex items-center">
							<span class="w-8 h-8 bg-primary-500 rounded-lg flex items-center justify-center mr-3 text-sm">
								{category.charAt(0)}
							</span>
							{category}
							<span class="ml-3 text-sm text-neutral-400 font-normal">
								({categoryBrands.length} brands)
							</span>
						</h3>
						
						<div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 xl:grid-cols-8 gap-4">
							{categoryBrands.map((brand) => (
								<a 
									href={`/brands/${brand.unique_name}`}
									class="card p-4 rounded-xl group hover:scale-105 transition-all duration-300 text-center"
								>
									<div class="relative mb-3">
										<img 
											src={brand.logo_url || '/images/placeholder-brand.png'} 
											alt={brand.name}
											class="w-12 h-12 mx-auto object-contain rounded-lg"
											loading="lazy"
										/>
										{brand.is_verified && (
											<div class="absolute -top-1 -right-1">
												<svg class="w-4 h-4 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
													<path fill-rule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
												</svg>
											</div>
										)}
									</div>
									
									<h4 class="font-medium text-white text-xs group-hover:text-primary-400 transition-colors line-clamp-2">
										{brand.name}
									</h4>
									
									{brand.deal_count > 0 && (
										<p class="text-xs text-neutral-500 mt-1">
											{brand.deal_count}
										</p>
									)}
								</a>
							))}
						</div>
					</div>
				))}
			</div>
		</section>
	)}

	<!-- All Brands Grid -->
	{brands.length > 0 && (
		<section class="py-16">
			<div class="container mx-auto px-4">
				<div class="flex items-center justify-between mb-8">
					<h2 class="text-3xl font-display font-bold text-white">
						All Brands
					</h2>
					<div class="flex items-center space-x-4">
						<select class="bg-white/10 border border-white/20 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-primary-500">
							<option value="name">A-Z</option>
							<option value="deals">Most Deals</option>
							<option value="popular">Most Popular</option>
							<option value="newest">Newest</option>
						</select>
					</div>
				</div>
				
				<div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 xl:grid-cols-8 gap-4">
					{brands.map((brand) => (
						<a 
							href={`/brands/${brand.unique_name}`}
							class="card p-4 rounded-xl group hover:scale-105 transition-all duration-300 text-center"
						>
							<div class="relative mb-3">
								<img 
									src={brand.logo_url || '/images/placeholder-brand.png'} 
									alt={brand.name}
									class="w-12 h-12 mx-auto object-contain rounded-lg"
									loading="lazy"
								/>
								{brand.is_verified && (
									<div class="absolute -top-1 -right-1">
										<svg class="w-4 h-4 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
											<path fill-rule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
										</svg>
									</div>
								)}
							</div>
							
							<h4 class="font-medium text-white text-xs group-hover:text-primary-400 transition-colors line-clamp-2">
								{brand.name}
							</h4>
							
							{brand.deal_count > 0 && (
								<p class="text-xs text-neutral-500 mt-1">
									{brand.deal_count} deals
								</p>
							)}
						</a>
					))}
				</div>
				
				<!-- Load More Button -->
				<div class="text-center mt-12">
					<button class="btn-primary px-8 py-3 rounded-xl font-semibold hover:scale-105 transition-transform">
						Load More Brands
					</button>
				</div>
			</div>
		</section>
	)}

	{error && (
		<section class="py-16">
			<div class="container mx-auto px-4 text-center">
				<div class="card p-8 rounded-2xl max-w-md mx-auto">
					<div class="text-red-400 mb-4">
						<svg class="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
						</svg>
					</div>
					<h3 class="text-xl font-semibold text-white mb-2">Unable to Load Brands</h3>
					<p class="text-neutral-400 mb-4">{error}</p>
					<button class="btn-primary px-6 py-2 rounded-lg" onclick="window.location.reload()">
						Try Again
					</button>
				</div>
			</div>
		</section>
	)}
</Layout>

<script>
	// Add interactive functionality
	document.addEventListener('DOMContentLoaded', function() {
		// Search functionality
		const searchInput = document.querySelector('input[type="text"]');
		if (searchInput) {
			searchInput.addEventListener('input', function() {
				const query = this.value.toLowerCase().trim();
				const brandCards = document.querySelectorAll('a[href^="/brands/"]');
				
				brandCards.forEach(card => {
					const brandName = card.querySelector('h4, h3')?.textContent.toLowerCase();
					if (!query || brandName?.includes(query)) {
						card.style.display = '';
					} else {
						card.style.display = 'none';
					}
				});
			});
		}

		// Sort functionality
		const sortSelect = document.querySelector('select');
		if (sortSelect) {
			sortSelect.addEventListener('change', function() {
				// Add sorting logic here
				console.log('Sort by:', this.value);
			});
		}
	});
</script>
