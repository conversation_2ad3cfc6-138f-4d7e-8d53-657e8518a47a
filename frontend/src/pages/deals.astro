---
import Layout from '../layouts/Layout.astro';

const API_BASE_URL = import.meta.env.PUBLIC_API_URL || 'http://127.0.0.1:8080';

// Fetch deals from API
let deals = [];
let error = null;

try {
	const response = await fetch(`${API_BASE_URL}/api/v1/deals?limit=20`);
	if (response.ok) {
		const data = await response.json();
		deals = data.data || [];
	} else {
		error = 'Failed to fetch deals';
	}
} catch (err) {
	console.error('Error fetching deals:', err);
	error = 'Network error';
}

// Helper function to format discount
function formatDiscount(deal) {
	if (deal.discount_type === 'percentage') {
		return `${deal.discount_value}% OFF`;
	} else if (deal.discount_type === 'fixed') {
		return `$${deal.discount_value} OFF`;
	} else {
		return 'DEAL';
	}
}

// Helper function to get time remaining
function getTimeRemaining(expiryDate) {
	const now = new Date();
	const expiry = new Date(expiryDate);
	const diff = expiry - now;
	
	if (diff <= 0) return 'Expired';
	
	const days = Math.floor(diff / (1000 * 60 * 60 * 24));
	const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
	
	if (days > 0) {
		return `${days}d ${hours}h left`;
	} else if (hours > 0) {
		return `${hours}h left`;
	} else {
		return 'Ending soon';
	}
}
---

<Layout 
	title="Hot Deals - MaxCoupon"
	description="Discover the hottest deals and exclusive offers from top brands. Limited time offers you don't want to miss!"
>
	<!-- Hero Section -->
	<section class="py-16 bg-gradient-to-br from-purple-600 to-purple-800 text-white">
		<div class="container mx-auto px-4">
			<div class="max-w-4xl mx-auto text-center">
				<h1 class="text-4xl md:text-5xl font-bold mb-6">
					🔥 Hot Deals
				</h1>
				<p class="text-xl text-purple-100 mb-8">
					Discover exclusive deals and limited-time offers from your favorite brands
				</p>
				
				<!-- Quick Stats -->
				<div class="grid grid-cols-2 md:grid-cols-4 gap-4 max-w-2xl mx-auto">
					<div class="bg-white/10 rounded-lg p-4">
						<div class="text-2xl font-bold">{deals.length}</div>
						<div class="text-sm text-purple-200">Active Deals</div>
					</div>
					<div class="bg-white/10 rounded-lg p-4">
						<div class="text-2xl font-bold">50+</div>
						<div class="text-sm text-purple-200">Brands</div>
					</div>
					<div class="bg-white/10 rounded-lg p-4">
						<div class="text-2xl font-bold">95%</div>
						<div class="text-sm text-purple-200">Success Rate</div>
					</div>
					<div class="bg-white/10 rounded-lg p-4">
						<div class="text-2xl font-bold">24/7</div>
						<div class="text-sm text-purple-200">Updated</div>
					</div>
				</div>
			</div>
		</div>
	</section>

	<!-- Filter Section -->
	<section class="py-8 bg-white border-b">
		<div class="container mx-auto px-4">
			<div class="flex flex-wrap items-center justify-between gap-4">
				<div class="flex items-center space-x-4">
					<span class="font-semibold text-gray-700">Filter by:</span>
					<select id="category-filter" class="px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500">
						<option value="">All Categories</option>
						<option value="electronics">Electronics</option>
						<option value="fashion">Fashion</option>
						<option value="home-garden">Home & Garden</option>
						<option value="sports-outdoors">Sports & Outdoors</option>
						<option value="beauty-health">Beauty & Health</option>
						<option value="travel">Travel</option>
					</select>
					
					<select id="sort-filter" class="px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500">
						<option value="newest">Newest First</option>
						<option value="discount-high">Highest Discount</option>
						<option value="expiry">Expiring Soon</option>
						<option value="popularity">Most Popular</option>
					</select>
				</div>
				
				<div class="text-sm text-gray-600">
					Showing {deals.length} deals
				</div>
			</div>
		</div>
	</section>

	<!-- Deals Grid -->
	<section class="py-16 bg-gray-50">
		<div class="container mx-auto px-4">
			{error ? (
				<div class="text-center py-12">
					<div class="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
						<svg class="w-8 h-8 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
						</svg>
					</div>
					<h3 class="text-lg font-semibold text-gray-900 mb-2">Error Loading Deals</h3>
					<p class="text-gray-600">{error}</p>
				</div>
			) : deals.length === 0 ? (
				<div class="text-center py-12">
					<div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
						<svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4"></path>
						</svg>
					</div>
					<h3 class="text-lg font-semibold text-gray-900 mb-2">No Deals Available</h3>
					<p class="text-gray-600">Check back later for new deals!</p>
				</div>
			) : (
				<div id="deals-grid" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
					{deals.map((deal, index) => (
						<div class="card group relative overflow-hidden">
							<!-- Hot Badge for top deals -->
							{index < 3 && (
								<div class="absolute top-4 left-4 z-10">
									<span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-bold bg-red-500 text-white">
										<svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
											<path fill-rule="evenodd" d="M12.395 2.553a1 1 0 00-1.45-.385c-.345.23-.614.558-.822.88-.214.33-.403.713-.57 1.116-.334.804-.614 1.768-.84 2.734a31.365 31.365 0 00-.613 3.58 2.64 2.64 0 01-.945-1.067c-.328-.68-.398-1.534-.398-2.654A1 1 0 005.05 6.05 6.981 6.981 0 003 11a7 7 0 1011.95-4.95c-.592-.591-.98-.985-1.348-1.467-.363-.476-.724-1.063-1.207-2.03zM12.12 15.12A3 3 0 017 13s.879.5 2.5.5c0-1 .5-4 1.25-4.5.5 1 .786 1.293 1.371 1.879A2.99 2.99 0 0113 13a2.99 2.99 0 01-.879 2.121z" clip-rule="evenodd"></path>
										</svg>
										HOT
									</span>
								</div>
							)}

							<!-- Exclusive Badge -->
							{deal.is_exclusive && (
								<div class="absolute top-4 right-4 z-10">
									<span class="badge badge-primary text-xs">
										Exclusive
									</span>
								</div>
							)}

							<!-- Deal Content -->
							<div class="p-6">
								<!-- Brand Header -->
								<div class="flex items-center space-x-3 mb-4">
									<div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
										<span class="text-xl font-bold text-purple-600">
											{deal.brand?.name?.charAt(0) || 'D'}
										</span>
									</div>
									<div>
										<h3 class="font-semibold text-gray-900">{deal.brand?.name || 'Brand'}</h3>
										<p class="text-sm text-gray-500">{deal.category?.name || 'General'}</p>
									</div>
								</div>

								<!-- Deal Title -->
								<h4 class="text-lg font-semibold text-gray-900 mb-3 line-clamp-2 group-hover:text-purple-600 transition-colors">
									{deal.title}
								</h4>

								<!-- Deal Description -->
								<p class="text-gray-600 text-sm mb-4 line-clamp-2">
									{deal.description}
								</p>

								<!-- Discount Badge -->
								<div class="flex items-center justify-between mb-4">
									<div class="bg-purple-600 text-white px-4 py-2 rounded-lg font-bold text-xl">
										{formatDiscount(deal)}
									</div>
									<div class="text-right">
										<div class="text-sm text-gray-500">Success Rate</div>
										<div class="text-lg font-semibold text-green-600">{deal.success_rate || 0}%</div>
									</div>
								</div>

								<!-- Stats Row -->
								<div class="flex items-center justify-between text-sm text-gray-500 mb-4">
									<div class="flex items-center space-x-4">
										<span class="flex items-center">
											<svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
												<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
												<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
											</svg>
											{deal.click_count || 0}
										</span>
										<span class="flex items-center">
											<svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
												<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
											</svg>
											{getTimeRemaining(deal.expiry_date)}
										</span>
									</div>
									{deal.is_verified && (
										<span class="flex items-center text-green-600">
											<svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
												<path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
											</svg>
											Verified
										</span>
									)}
								</div>

								<!-- Action Buttons -->
								<div class="flex space-x-2">
									<button 
										class="btn-primary flex-1 text-sm py-2 get-deal-btn"
										data-url={deal.deal_url}
										data-title={deal.title}
										data-id={deal.id}
									>
										Get Deal
									</button>
									<button class="btn-secondary text-sm py-2 px-4 share-btn" data-title={deal.title} data-url={deal.deal_url}>
										<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
											<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z"></path>
										</svg>
									</button>
								</div>
							</div>
						</div>
					))}
				</div>
			)}
		</div>
	</section>

	<!-- Load More Section -->
	{deals.length > 0 && (
		<section class="py-8 bg-white">
			<div class="container mx-auto px-4 text-center">
				<button id="load-more-btn" class="btn-outline px-8 py-3">
					Load More Deals
				</button>
			</div>
		</section>
	)}
</Layout>

<script>
	document.addEventListener('DOMContentLoaded', () => {
		const getDealButtons = document.querySelectorAll('.get-deal-btn');
		const shareButtons = document.querySelectorAll('.share-btn');
		const loadMoreBtn = document.getElementById('load-more-btn');

		// Handle get deal buttons
		getDealButtons.forEach(button => {
			button.addEventListener('click', async () => {
				const dealUrl = button.getAttribute('data-url');
				const dealTitle = button.getAttribute('data-title');
				const dealId = button.getAttribute('data-id');

				// Track click
				try {
					await fetch(`/api/v1/deals/${dealId}/click`, {
						method: 'POST',
						headers: {
							'Content-Type': 'application/json'
						}
					});
				} catch (error) {
					console.error('Failed to track click:', error);
				}

				// Open deal URL
				if (dealUrl) {
					window.open(dealUrl, '_blank');
				}
			});
		});

		// Handle share buttons
		shareButtons.forEach(button => {
			button.addEventListener('click', async () => {
				const title = button.getAttribute('data-title');
				const url = button.getAttribute('data-url');

				if (navigator.share) {
					try {
						await navigator.share({
							title: title,
							url: url
						});
					} catch (error) {
						console.error('Error sharing:', error);
					}
				} else {
					// Fallback to clipboard
					try {
						await navigator.clipboard.writeText(url);
						alert('Deal link copied to clipboard!');
					} catch (error) {
						console.error('Failed to copy to clipboard:', error);
					}
				}
			});
		});

		// Handle load more
		if (loadMoreBtn) {
			loadMoreBtn.addEventListener('click', () => {
				// TODO: Implement pagination
				alert('Load more functionality coming soon!');
			});
		}
	});
</script>

<style>
	.line-clamp-2 {
		display: -webkit-box;
		-webkit-line-clamp: 2;
		-webkit-box-orient: vertical;
		overflow: hidden;
	}
</style>
