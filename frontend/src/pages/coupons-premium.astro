---
import Layout from '../layouts/Layout.astro';

const API_BASE_URL = import.meta.env.PUBLIC_API_URL || 'http://127.0.0.1:8080';

// Fetch coupons from API
let coupons = [];
let hotCoupons = [];
let exclusiveCoupons = [];
let error = null;

try {
	// Fetch all coupons
	const response = await fetch(`${API_BASE_URL}/api/v1/coupons?limit=20`);
	if (response.ok) {
		const data = await response.json();
		coupons = data.data || [];
	}

	// Fetch hot coupons
	const hotResponse = await fetch(`${API_BASE_URL}/api/v1/coupons/hot?limit=6`);
	if (hotResponse.ok) {
		const hotData = await hotResponse.json();
		hotCoupons = hotData.data || [];
	}

	// Fetch exclusive coupons
	const exclusiveResponse = await fetch(`${API_BASE_URL}/api/v1/coupons/exclusive?limit=4`);
	if (exclusiveResponse.ok) {
		const exclusiveData = await exclusiveResponse.json();
		exclusiveCoupons = exclusiveData.data || [];
	}
} catch (err) {
	console.error('Error fetching coupons:', err);
	error = 'Network error';
}

// Helper functions
function formatDiscount(coupon) {
	if (coupon.discount_type === 'percentage') {
		return `${coupon.discount_value}% OFF`;
	} else if (coupon.discount_type === 'fixed') {
		return `$${coupon.discount_value} OFF`;
	} else {
		return 'COUPON';
	}
}

function getTimeRemaining(expiryDate) {
	if (!expiryDate) return null;
	
	const now = new Date();
	const expiry = new Date(expiryDate);
	const diff = expiry - now;
	
	if (diff <= 0) return 'Expired';
	
	const days = Math.floor(diff / (1000 * 60 * 60 * 24));
	const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
	
	if (days > 0) {
		return `${days}d ${hours}h left`;
	} else if (hours > 0) {
		return `${hours}h left`;
	} else {
		return 'Ending soon';
	}
}
---

<Layout 
	title="Premium Coupons - MaxCoupon"
	description="Discover exclusive coupon codes and promo codes from top brands. Save money with verified discount codes."
>
	<!-- Hero Section -->
	<section class="relative py-20 overflow-hidden">
		<div class="absolute inset-0 bg-gradient-to-br from-purple-900/20 via-blue-900/20 to-indigo-900/20"></div>
		<div class="container mx-auto px-4 relative z-10">
			<div class="text-center max-w-4xl mx-auto">
				<h1 class="text-5xl md:text-6xl font-display font-bold text-white mb-6">
					Premium <span class="text-gradient">Coupons</span>
				</h1>
				<p class="text-xl text-neutral-300 mb-8 max-w-2xl mx-auto">
					Unlock exclusive savings with verified coupon codes from the world's top brands
				</p>
				
				<!-- Search Bar -->
				<div class="max-w-2xl mx-auto">
					<div class="relative">
						<input 
							type="text" 
							placeholder="Search for coupons, brands, or stores..."
							class="w-full px-6 py-4 pl-12 bg-white/10 border border-white/20 rounded-2xl text-white placeholder-neutral-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent backdrop-blur-sm"
						/>
						<svg class="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-neutral-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
						</svg>
					</div>
				</div>
			</div>
		</div>
	</section>

	<!-- Exclusive Coupons Section -->
	{exclusiveCoupons.length > 0 && (
		<section class="py-16">
			<div class="container mx-auto px-4">
				<div class="flex items-center justify-between mb-8">
					<div>
						<h2 class="text-3xl font-display font-bold text-white mb-2">
							👑 Exclusive Coupons
						</h2>
						<p class="text-neutral-400">Premium codes available only on MaxCoupon</p>
					</div>
				</div>
				
				<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
					{exclusiveCoupons.map((coupon) => (
						<div class="card p-6 rounded-2xl group hover:scale-105 transition-all duration-300 border border-purple-500/20 bg-gradient-to-br from-purple-900/10 to-pink-900/10">
							<div class="flex items-center justify-between mb-4">
								<div class="flex items-center space-x-3">
									<img 
										src={coupon.brand?.logo_url || '/images/placeholder-brand.png'} 
										alt={coupon.brand?.name}
										class="w-10 h-10 rounded-lg object-contain"
									/>
									<div>
										<h3 class="font-semibold text-white text-sm">{coupon.brand?.name}</h3>
										<span class="text-xs text-purple-400 font-medium">EXCLUSIVE</span>
									</div>
								</div>
								<div class="text-right">
									<div class="text-2xl font-bold text-primary-400">
										{formatDiscount(coupon)}
									</div>
								</div>
							</div>
							
							<div class="space-y-3">
								<h4 class="font-semibold text-white line-clamp-2 group-hover:text-primary-400 transition-colors">
									{coupon.title}
								</h4>
								
								<p class="text-sm text-neutral-400 line-clamp-2">
									{coupon.description}
								</p>
								
								<div class="flex items-center justify-between text-sm">
									<div class="flex items-center space-x-1">
										<span class="text-green-400">✓</span>
										<span class="text-neutral-400">{coupon.success_rate}% success</span>
									</div>
									{getTimeRemaining(coupon.expiry_date) && (
										<span class="text-orange-400">{getTimeRemaining(coupon.expiry_date)}</span>
									)}
								</div>
								
								<div class="flex items-center space-x-2">
									<div class="flex-1 bg-neutral-800 rounded-lg p-3 font-mono text-center">
										<span class="text-primary-400 font-bold tracking-wider">
											{coupon.code || 'GET DEAL'}
										</span>
									</div>
									<button class="btn-primary px-4 py-3 rounded-lg font-semibold hover:scale-105 transition-transform">
										Copy
									</button>
								</div>
							</div>
						</div>
					))}
				</div>
			</div>
		</section>
	)}

	<!-- Hot Coupons Section -->
	{hotCoupons.length > 0 && (
		<section class="py-16">
			<div class="container mx-auto px-4">
				<div class="flex items-center justify-between mb-8">
					<div>
						<h2 class="text-3xl font-display font-bold text-white mb-2">
							🔥 Hot Coupons
						</h2>
						<p class="text-neutral-400">Trending coupon codes that everyone's using</p>
					</div>
				</div>
				
				<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
					{hotCoupons.map((coupon) => (
						<div class="card p-6 rounded-2xl group hover:scale-105 transition-all duration-300">
							<div class="flex items-center justify-between mb-4">
								<div class="flex items-center space-x-3">
									<img 
										src={coupon.brand?.logo_url || '/images/placeholder-brand.png'} 
										alt={coupon.brand?.name}
										class="w-10 h-10 rounded-lg object-contain"
									/>
									<div>
										<h3 class="font-semibold text-white text-sm">{coupon.brand?.name}</h3>
										<span class="text-xs text-orange-400 font-medium">HOT</span>
									</div>
								</div>
								<div class="text-right">
									<div class="text-2xl font-bold text-primary-400">
										{formatDiscount(coupon)}
									</div>
								</div>
							</div>
							
							<div class="space-y-3">
								<h4 class="font-semibold text-white line-clamp-2 group-hover:text-primary-400 transition-colors">
									{coupon.title}
								</h4>
								
								<p class="text-sm text-neutral-400 line-clamp-2">
									{coupon.description}
								</p>
								
								<div class="flex items-center justify-between text-sm">
									<div class="flex items-center space-x-1">
										<span class="text-green-400">✓</span>
										<span class="text-neutral-400">{coupon.success_rate}% success</span>
									</div>
									<span class="text-neutral-400">{coupon.used_count} used</span>
								</div>
								
								<div class="flex items-center space-x-2">
									<div class="flex-1 bg-neutral-800 rounded-lg p-3 font-mono text-center">
										<span class="text-primary-400 font-bold tracking-wider">
											{coupon.code || 'GET DEAL'}
										</span>
									</div>
									<button class="btn-primary px-4 py-3 rounded-lg font-semibold hover:scale-105 transition-transform">
										Copy
									</button>
								</div>
							</div>
						</div>
					))}
				</div>
			</div>
		</section>
	)}

	<!-- All Coupons Section -->
	{coupons.length > 0 && (
		<section class="py-16">
			<div class="container mx-auto px-4">
				<div class="flex items-center justify-between mb-8">
					<h2 class="text-3xl font-display font-bold text-white">
						All Coupons
					</h2>
					<div class="flex items-center space-x-4">
						<select class="bg-white/10 border border-white/20 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-primary-500">
							<option value="newest">Newest First</option>
							<option value="discount">Highest Discount</option>
							<option value="popular">Most Popular</option>
							<option value="ending">Ending Soon</option>
						</select>
					</div>
				</div>
				
				<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
					{coupons.map((coupon) => (
						<div class="card p-6 rounded-2xl group hover:scale-105 transition-all duration-300">
							<div class="flex items-center justify-between mb-4">
								<div class="flex items-center space-x-3">
									<img 
										src={coupon.brand?.logo_url || '/images/placeholder-brand.png'} 
										alt={coupon.brand?.name}
										class="w-8 h-8 rounded object-contain"
									/>
									<div>
										<h3 class="font-semibold text-white text-sm">{coupon.brand?.name}</h3>
										{coupon.is_verified && (
											<span class="text-xs text-green-400 font-medium">VERIFIED</span>
										)}
									</div>
								</div>
								<div class="text-right">
									<div class="text-xl font-bold text-primary-400">
										{formatDiscount(coupon)}
									</div>
								</div>
							</div>
							
							<div class="space-y-3">
								<h4 class="font-semibold text-white line-clamp-2 group-hover:text-primary-400 transition-colors text-sm">
									{coupon.title}
								</h4>
								
								<div class="flex items-center justify-between text-sm">
									<div class="flex items-center space-x-1">
										<span class="text-green-400">✓</span>
										<span class="text-neutral-400">{coupon.success_rate}% success</span>
									</div>
									{getTimeRemaining(coupon.expiry_date) && (
										<span class="text-orange-400 text-xs">{getTimeRemaining(coupon.expiry_date)}</span>
									)}
								</div>
								
								<div class="flex items-center space-x-2">
									<div class="flex-1 bg-neutral-800 rounded-lg p-2 font-mono text-center">
										<span class="text-primary-400 font-bold tracking-wider text-sm">
											{coupon.code || 'GET DEAL'}
										</span>
									</div>
									<button class="btn-secondary px-3 py-2 rounded-lg font-semibold text-sm hover:scale-105 transition-transform">
										Copy
									</button>
								</div>
							</div>
						</div>
					))}
				</div>
				
				<!-- Load More Button -->
				<div class="text-center mt-12">
					<button class="btn-primary px-8 py-3 rounded-xl font-semibold hover:scale-105 transition-transform">
						Load More Coupons
					</button>
				</div>
			</div>
		</section>
	)}

	{error && (
		<section class="py-16">
			<div class="container mx-auto px-4 text-center">
				<div class="card p-8 rounded-2xl max-w-md mx-auto">
					<div class="text-red-400 mb-4">
						<svg class="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
						</svg>
					</div>
					<h3 class="text-xl font-semibold text-white mb-2">Unable to Load Coupons</h3>
					<p class="text-neutral-400 mb-4">{error}</p>
					<button class="btn-primary px-6 py-2 rounded-lg" onclick="window.location.reload()">
						Try Again
					</button>
				</div>
			</div>
		</section>
	)}
</Layout>

<script>
	// Add interactive functionality
	document.addEventListener('DOMContentLoaded', function() {
		// Copy coupon code functionality
		const copyButtons = document.querySelectorAll('button:contains("Copy")');
		copyButtons.forEach(button => {
			button.addEventListener('click', function() {
				const codeElement = this.parentElement.querySelector('.font-mono span');
				const code = codeElement.textContent.trim();
				
				if (code && code !== 'GET DEAL') {
					navigator.clipboard.writeText(code).then(() => {
						this.textContent = 'Copied!';
						this.classList.add('bg-green-500');
						
						setTimeout(() => {
							this.textContent = 'Copy';
							this.classList.remove('bg-green-500');
						}, 2000);
					});
				} else {
					// Handle "GET DEAL" button
					window.open(this.dataset.url || '#', '_blank');
				}
			});
		});

		// Search functionality
		const searchInput = document.querySelector('input[type="text"]');
		if (searchInput) {
			searchInput.addEventListener('keypress', function(e) {
				if (e.key === 'Enter') {
					const query = this.value.trim();
					if (query) {
						window.location.href = `/search?q=${encodeURIComponent(query)}&type=coupons`;
					}
				}
			});
		}
	});
</script>
