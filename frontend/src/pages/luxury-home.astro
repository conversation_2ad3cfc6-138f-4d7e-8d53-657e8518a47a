---
import LuxuryLayout from '../layouts/LuxuryLayout.astro';
import LuxuryHeader from '../components/layout/LuxuryHeader.astro';
import { api } from '../lib/api';

// Fetch data from backend API
let countries = [];
let brands = [];
let featuredCoupons = [];
let errorMessage = '';

try {
  // Fetch countries
  const countriesResponse = await api.getCountries();
  if (countriesResponse.data) {
    countries = countriesResponse.data;
  }

  // Fetch popular brands
  const brandsResponse = await api.getBrands({ limit: 12 });
  if (brandsResponse.data) {
    brands = brandsResponse.data;
  }

  // Fetch featured coupons
  const couponsResponse = await api.getFeaturedCoupons(8);
  if (couponsResponse.data) {
    featuredCoupons = couponsResponse.data;
  }
} catch (error) {
  console.error('Failed to fetch data:', error);
  errorMessage = 'Unable to load data from server. Please try again later.';
}
---

<LuxuryLayout 
  title="MaxCoupon - Premium Deals & Exclusive Luxury Coupons"
  description="Discover curated exclusive offers from the world's most prestigious brands. Save more, spend smarter with our AI-powered luxury deal discovery platform."
>
  <!-- Hero Section -->
  <section class="relative py-32 overflow-hidden section-animate">
    <div class="container mx-auto px-4 text-center relative z-10">
      <div class="max-w-6xl mx-auto">
        <h1 class="heading-xl mb-8 text-balance">
          Discover
          <span class="block text-gradient animate-pulse">Premium Deals</span>
          <span class="block text-3xl md:text-4xl lg:text-5xl text-neutral-600 font-normal mt-4">
            That Actually Matter
          </span>
        </h1>
        
        <p class="text-2xl text-neutral-600 mb-12 max-w-4xl mx-auto leading-relaxed">
          Curated exclusive offers from the world's most prestigious brands. 
          <span class="text-primary-600 font-semibold">Save more, spend smarter</span> with our AI-powered deal discovery.
        </p>

        <!-- Search Bar -->
        <div class="mb-16 max-w-2xl mx-auto">
          <div class="relative">
            <input 
              type="text" 
              placeholder="Search for luxury brands, deals, or categories..."
              class="w-full px-6 py-5 text-lg bg-white rounded-2xl shadow-luxury border border-neutral-200 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all duration-300"
            />
            <button class="absolute right-3 top-1/2 transform -translate-y-1/2 btn-luxury px-6 py-3">
              <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
              </svg>
              Search
            </button>
          </div>
        </div>

        <!-- Stats -->
        <div class="grid grid-cols-2 md:grid-cols-4 gap-8 mb-16 max-w-4xl mx-auto">
          <div class="text-center">
            <div class="text-4xl md:text-5xl font-bold text-primary-600 mb-2">{featuredCoupons.length}K+</div>
            <div class="text-neutral-500 text-sm md:text-base">Premium Coupons</div>
          </div>
          <div class="text-center">
            <div class="text-4xl md:text-5xl font-bold text-secondary-600 mb-2">{brands.length}K+</div>
            <div class="text-neutral-500 text-sm md:text-base">Elite Brands</div>
          </div>
          <div class="text-center">
            <div class="text-4xl md:text-5xl font-bold text-accent-600 mb-2">5M+</div>
            <div class="text-neutral-500 text-sm md:text-base">Smart Savers</div>
          </div>
          <div class="text-center">
            <div class="text-4xl md:text-5xl font-bold text-primary-600 mb-2">$500M+</div>
            <div class="text-neutral-500 text-sm md:text-base">Total Saved</div>
          </div>
        </div>

        <!-- CTA Buttons -->
        <div class="flex flex-col sm:flex-row items-center justify-center gap-6">
          <a href="/coupons" class="btn-luxury text-lg px-12 py-5">
            <span>Explore Premium Deals</span>
            <svg class="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6"></path>
            </svg>
          </a>
          <a href="/brands" class="btn-outline text-lg px-12 py-5">
            Browse Luxury Brands
          </a>
        </div>
      </div>
    </div>
  </section>

  <!-- Error Message -->
  {errorMessage && (
    <div class="container mx-auto px-4 mb-8">
      <div class="bg-error-50 border border-error-200 text-error-700 px-4 py-3 rounded-xl">
        {errorMessage}
      </div>
    </div>
  )}

  <!-- Featured Coupons Section -->
  {featuredCoupons.length > 0 && (
    <section class="section-padding section-animate">
      <div class="container mx-auto px-4">
        <div class="text-center mb-16">
          <h2 class="heading-lg mb-6">
            🔥 <span class="text-gradient">Featured Premium Deals</span>
          </h2>
          <p class="text-xl text-neutral-600 max-w-3xl mx-auto">
            Hand-picked exclusive offers with the highest savings from luxury brands
          </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {featuredCoupons.map((coupon) => (
            <div class="card card-hover p-6 group">
              <div class="flex items-center justify-between mb-4">
                <div class="w-12 h-12 bg-gradient-to-br from-primary-500 to-secondary-500 rounded-xl flex items-center justify-center">
                  <span class="text-white font-bold text-lg">
                    {coupon.brand_id}
                  </span>
                </div>
                {coupon.is_exclusive && (
                  <span class="badge badge-secondary text-xs">Exclusive</span>
                )}
              </div>
              
              <h3 class="text-lg font-semibold text-neutral-900 mb-2 group-hover:text-primary-600 transition-colors">
                {coupon.title}
              </h3>
              
              <p class="text-neutral-600 text-sm mb-4 line-clamp-2">
                {coupon.description}
              </p>
              
              <div class="flex items-center justify-between mb-4">
                <div class="text-2xl font-bold text-primary-600">
                  {coupon.discount_text}
                </div>
                <div class="text-sm text-neutral-500">
                  {coupon.success_rate}% success
                </div>
              </div>
              
              <button class="w-full btn-primary py-3 text-sm">
                Get Code
              </button>
            </div>
          ))}
        </div>

        <div class="text-center mt-12">
          <a href="/coupons" class="btn-outline px-8 py-4">
            View All Premium Coupons
          </a>
        </div>
      </div>
    </section>
  )}

  <!-- Popular Brands Section -->
  {brands.length > 0 && (
    <section class="section-padding bg-gradient-to-br from-neutral-50 to-primary-50/30 section-animate">
      <div class="container mx-auto px-4">
        <div class="text-center mb-16">
          <h2 class="heading-lg mb-6">
            ⭐ <span class="text-gradient">Popular Luxury Brands</span>
          </h2>
          <p class="text-xl text-neutral-600 max-w-3xl mx-auto">
            Discover exclusive deals from the world's most prestigious brands
          </p>
        </div>

        <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-6">
          {brands.map((brand) => (
            <a 
              href={`/brands/${brand.unique_name}`}
              class="card card-hover p-6 text-center group"
            >
              <div class="w-16 h-16 bg-gradient-to-br from-neutral-100 to-neutral-200 rounded-2xl flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300">
                <span class="text-2xl font-bold text-neutral-700">
                  {brand.name.charAt(0)}
                </span>
              </div>
              
              <h3 class="font-semibold text-neutral-900 mb-1 group-hover:text-primary-600 transition-colors">
                {brand.name}
              </h3>
              
              <p class="text-xs text-neutral-500 mb-3">
                {brand.total_coupons} deals
              </p>
              
              <div class="text-sm font-medium text-secondary-600">
                {brand.average_discount}% avg save
              </div>
            </a>
          ))}
        </div>

        <div class="text-center mt-12">
          <a href="/brands" class="btn-secondary px-8 py-4">
            Explore All Brands
          </a>
        </div>
      </div>
    </section>
  )}

  <!-- Newsletter Section -->
  <section class="section-padding section-animate">
    <div class="container mx-auto px-4">
      <div class="max-w-4xl mx-auto text-center">
        <div class="card-luxury p-12 rounded-3xl">
          <h2 class="heading-md mb-6">
            Never Miss a <span class="text-gradient">Premium Deal</span> Again
          </h2>
          <p class="text-xl text-neutral-600 mb-8 max-w-2xl mx-auto">
            Get exclusive access to VIP deals, early bird offers, and premium coupons 
            delivered straight to your inbox.
          </p>
          
          <div class="flex flex-col sm:flex-row gap-4 max-w-lg mx-auto">
            <input 
              type="email" 
              placeholder="Enter your premium email"
              class="flex-1 input-luxury"
            />
            <button class="btn-luxury px-8 py-4 whitespace-nowrap">
              Join VIP List
            </button>
          </div>
          
          <p class="text-sm text-neutral-500 mt-4">
            Join 100,000+ smart savers. Unsubscribe anytime.
          </p>
        </div>
      </div>
    </div>
  </section>

  <!-- Trust Indicators -->
  <section class="py-16 section-animate">
    <div class="container mx-auto px-4">
      <div class="text-center mb-12">
        <h3 class="heading-sm mb-4">
          Trusted by Premium Shoppers Worldwide
        </h3>
        <p class="text-neutral-600">
          Join millions who save smarter with MaxCoupon
        </p>
      </div>
      
      <div class="grid grid-cols-2 md:grid-cols-4 gap-8 max-w-4xl mx-auto">
        <div class="text-center">
          <div class="w-16 h-16 bg-gradient-to-br from-primary-500 to-primary-600 rounded-2xl flex items-center justify-center mx-auto mb-4">
            <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.707-4.293a1 1 0 010 1.414L9.414 18.707a1 1 0 01-1.414 0L2.293 13a1 1 0 010-1.414l4.293-4.293a1 1 0 011.414 0L12 11.586l4.293-4.293a1 1 0 011.414 0z"></path>
            </svg>
          </div>
          <h4 class="font-semibold text-neutral-900 mb-2">Verified Deals</h4>
          <p class="text-sm text-neutral-600">All coupons tested and verified</p>
        </div>
        
        <div class="text-center">
          <div class="w-16 h-16 bg-gradient-to-br from-secondary-500 to-secondary-600 rounded-2xl flex items-center justify-center mx-auto mb-4">
            <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
            </svg>
          </div>
          <h4 class="font-semibold text-neutral-900 mb-2">Secure & Safe</h4>
          <p class="text-sm text-neutral-600">Your data is protected</p>
        </div>
        
        <div class="text-center">
          <div class="w-16 h-16 bg-gradient-to-br from-accent-500 to-accent-600 rounded-2xl flex items-center justify-center mx-auto mb-4">
            <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
            </svg>
          </div>
          <h4 class="font-semibold text-neutral-900 mb-2">Instant Savings</h4>
          <p class="text-sm text-neutral-600">Apply deals in one click</p>
        </div>
        
        <div class="text-center">
          <div class="w-16 h-16 bg-gradient-to-br from-primary-500 to-secondary-500 rounded-2xl flex items-center justify-center mx-auto mb-4">
            <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
            </svg>
          </div>
          <h4 class="font-semibold text-neutral-900 mb-2">Premium Support</h4>
          <p class="text-sm text-neutral-600">24/7 VIP customer service</p>
        </div>
      </div>
    </div>
  </section>
</LuxuryLayout>

<script>
  import { api } from '../lib/api';

  document.addEventListener('DOMContentLoaded', () => {
    // Track page view
    api.trackPageView('home');

    // Newsletter form handling
    const newsletterForm = document.querySelector('input[type="email"]');
    const subscribeBtn = document.querySelector('.btn-luxury');
    
    subscribeBtn?.addEventListener('click', (e) => {
      e.preventDefault();
      const email = newsletterForm?.value;
      
      if (email && email.includes('@')) {
        // Simulate subscription
        subscribeBtn.textContent = 'Subscribed! ✓';
        subscribeBtn.classList.add('bg-success-600');
        
        setTimeout(() => {
          subscribeBtn.textContent = 'Join VIP List';
          subscribeBtn.classList.remove('bg-success-600');
        }, 3000);
      } else {
        // Show error
        newsletterForm?.classList.add('border-error-500');
        setTimeout(() => {
          newsletterForm?.classList.remove('border-error-500');
        }, 2000);
      }
    });

    // Search functionality
    const searchInput = document.querySelector('input[placeholder*="Search"]');
    const searchBtn = document.querySelector('button');
    
    const performSearch = () => {
      const query = searchInput?.value?.trim();
      if (query) {
        window.location.href = `/search?q=${encodeURIComponent(query)}`;
      }
    };
    
    searchBtn?.addEventListener('click', performSearch);
    searchInput?.addEventListener('keypress', (e) => {
      if (e.key === 'Enter') {
        performSearch();
      }
    });

    // Coupon click tracking
    const couponCards = document.querySelectorAll('.card');
    couponCards.forEach((card, index) => {
      card.addEventListener('click', () => {
        // Track coupon interaction
        console.log(`Coupon ${index + 1} clicked`);
      });
    });
  });
</script>

<style>
  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
</style>
