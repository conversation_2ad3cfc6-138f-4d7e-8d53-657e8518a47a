---
import Layout from '../layouts/Layout.astro';

const API_BASE_URL = import.meta.env.PUBLIC_API_URL || 'http://127.0.0.1:8080';

// Fetch deals from API
let deals = [];
let hotDeals = [];
let flashDeals = [];
let featuredDeals = [];
let error = null;

try {
	// Fetch all deals
	const response = await fetch(`${API_BASE_URL}/api/v1/deals?limit=20`);
	if (response.ok) {
		const data = await response.json();
		deals = data.data || [];
	}

	// Fetch hot deals
	const hotResponse = await fetch(`${API_BASE_URL}/api/v1/deals/hot?limit=6`);
	if (hotResponse.ok) {
		const hotData = await hotResponse.json();
		hotDeals = hotData.data || [];
	}

	// Fetch flash deals
	const flashResponse = await fetch(`${API_BASE_URL}/api/v1/deals/flash?limit=4`);
	if (flashResponse.ok) {
		const flashData = await flashResponse.json();
		flashDeals = flashData.data || [];
	}

	// Fetch featured deals
	const featuredResponse = await fetch(`${API_BASE_URL}/api/v1/deals/featured?limit=8`);
	if (featuredResponse.ok) {
		const featuredData = await featuredResponse.json();
		featuredDeals = featuredData.data || [];
	}
} catch (err) {
	console.error('Error fetching deals:', err);
	error = 'Network error';
}

// Helper functions
function formatDiscount(deal) {
	if (deal.discount_type === 'percentage') {
		return `${deal.discount_value}% OFF`;
	} else if (deal.discount_type === 'fixed') {
		return `$${deal.discount_value} OFF`;
	} else {
		return 'DEAL';
	}
}

function formatPrice(price) {
	return new Intl.NumberFormat('en-US', {
		style: 'currency',
		currency: 'USD'
	}).format(price);
}
---

<Layout 
	title="Premium Deals - MaxCoupon"
	description="Discover exclusive deals and limited-time offers from top brands. Save big with our curated collection of premium deals."
>
	<!-- Hero Section -->
	<section class="relative py-20 overflow-hidden">
		<div class="absolute inset-0 bg-gradient-to-br from-purple-900/20 via-blue-900/20 to-indigo-900/20"></div>
		<div class="container mx-auto px-4 relative z-10">
			<div class="text-center max-w-4xl mx-auto">
				<h1 class="text-5xl md:text-6xl font-display font-bold text-white mb-6">
					Premium <span class="text-gradient">Deals</span> Collection
				</h1>
				<p class="text-xl text-neutral-300 mb-8 max-w-2xl mx-auto">
					Discover exclusive offers and limited-time deals from the world's most prestigious brands
				</p>
				
				<!-- Search Bar -->
				<div class="max-w-2xl mx-auto">
					<div class="relative">
						<input 
							type="text" 
							placeholder="Search for deals, brands, or products..."
							class="w-full px-6 py-4 pl-12 bg-white/10 border border-white/20 rounded-2xl text-white placeholder-neutral-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent backdrop-blur-sm"
						/>
						<svg class="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-neutral-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
						</svg>
					</div>
				</div>
			</div>
		</div>
	</section>

	<!-- Flash Deals Section -->
	{flashDeals.length > 0 && (
		<section class="py-16">
			<div class="container mx-auto px-4">
				<div class="flex items-center justify-between mb-8">
					<div>
						<h2 class="text-3xl font-display font-bold text-white mb-2">
							⚡ Flash Deals
						</h2>
						<p class="text-neutral-400">Limited time offers - grab them before they're gone!</p>
					</div>
					<div class="flex items-center space-x-2 text-red-400">
						<svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
							<path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"></path>
						</svg>
						<span class="font-semibold">Ending Soon</span>
					</div>
				</div>
				
				<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
					{flashDeals.map((deal) => (
						<div class="card p-6 rounded-2xl group hover:scale-105 transition-all duration-300 border border-red-500/20 bg-gradient-to-br from-red-900/10 to-orange-900/10">
							<div class="relative mb-4">
								<img 
									src={deal.image_url} 
									alt={deal.title}
									class="w-full h-48 object-cover rounded-xl"
									loading="lazy"
								/>
								<div class="absolute top-3 left-3">
									<span class="bg-red-500 text-white px-3 py-1 rounded-full text-sm font-semibold animate-pulse">
										FLASH
									</span>
								</div>
								<div class="absolute top-3 right-3">
									<span class="bg-black/70 text-white px-2 py-1 rounded-lg text-sm font-bold">
										{formatDiscount(deal)}
									</span>
								</div>
							</div>
							
							<div class="space-y-3">
								<h3 class="font-semibold text-white line-clamp-2 group-hover:text-primary-400 transition-colors">
									{deal.title}
								</h3>
								
								<div class="flex items-center space-x-2">
									<span class="text-2xl font-bold text-primary-400">
										{formatPrice(deal.sale_price)}
									</span>
									{deal.original_price && (
										<span class="text-neutral-500 line-through">
											{formatPrice(deal.original_price)}
										</span>
									)}
								</div>
								
								<div class="flex items-center justify-between">
									<div class="flex items-center space-x-1">
										<span class="text-yellow-400">⭐</span>
										<span class="text-sm text-neutral-400">{deal.success_rate}% success</span>
									</div>
									<span class="text-sm text-neutral-400">{deal.click_count} used</span>
								</div>
								
								<button class="w-full btn-primary py-3 rounded-xl font-semibold hover:scale-105 transition-transform">
									Get Deal
								</button>
							</div>
						</div>
					))}
				</div>
			</div>
		</section>
	)}

	<!-- Hot Deals Section -->
	{hotDeals.length > 0 && (
		<section class="py-16">
			<div class="container mx-auto px-4">
				<div class="flex items-center justify-between mb-8">
					<div>
						<h2 class="text-3xl font-display font-bold text-white mb-2">
							🔥 Hot Deals
						</h2>
						<p class="text-neutral-400">Trending deals that everyone's talking about</p>
					</div>
				</div>
				
				<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
					{hotDeals.map((deal) => (
						<div class="card p-6 rounded-2xl group hover:scale-105 transition-all duration-300">
							<div class="relative mb-4">
								<img 
									src={deal.image_url} 
									alt={deal.title}
									class="w-full h-48 object-cover rounded-xl"
									loading="lazy"
								/>
								<div class="absolute top-3 left-3">
									<span class="bg-orange-500 text-white px-3 py-1 rounded-full text-sm font-semibold">
										HOT
									</span>
								</div>
								<div class="absolute top-3 right-3">
									<span class="bg-black/70 text-white px-2 py-1 rounded-lg text-sm font-bold">
										{formatDiscount(deal)}
									</span>
								</div>
							</div>
							
							<div class="space-y-3">
								<div class="flex items-center space-x-2 mb-2">
									<img 
										src={deal.brand?.logo_url || '/images/placeholder-brand.png'} 
										alt={deal.brand?.name}
										class="w-6 h-6 rounded"
									/>
									<span class="text-sm text-neutral-400">{deal.brand?.name}</span>
								</div>
								
								<h3 class="font-semibold text-white line-clamp-2 group-hover:text-primary-400 transition-colors">
									{deal.title}
								</h3>
								
								<p class="text-sm text-neutral-400 line-clamp-2">
									{deal.description}
								</p>
								
								<div class="flex items-center space-x-2">
									<span class="text-2xl font-bold text-primary-400">
										{formatPrice(deal.sale_price)}
									</span>
									{deal.original_price && (
										<span class="text-neutral-500 line-through">
											{formatPrice(deal.original_price)}
										</span>
									)}
								</div>
								
								<div class="flex items-center justify-between">
									<div class="flex items-center space-x-1">
										<span class="text-yellow-400">⭐</span>
										<span class="text-sm text-neutral-400">{deal.success_rate}% success</span>
									</div>
									<span class="text-sm text-neutral-400">{deal.view_count} views</span>
								</div>
								
								<button class="w-full btn-primary py-3 rounded-xl font-semibold hover:scale-105 transition-transform">
									Get Deal
								</button>
							</div>
						</div>
					))}
				</div>
			</div>
		</section>
	)}

	<!-- Featured Deals Section -->
	{featuredDeals.length > 0 && (
		<section class="py-16">
			<div class="container mx-auto px-4">
				<div class="text-center mb-12">
					<h2 class="text-3xl font-display font-bold text-white mb-4">
						✨ Featured Deals
					</h2>
					<p class="text-neutral-400 max-w-2xl mx-auto">
						Hand-picked premium deals from our editorial team
					</p>
				</div>
				
				<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
					{featuredDeals.map((deal) => (
						<div class="card p-6 rounded-2xl group hover:scale-105 transition-all duration-300">
							<div class="relative mb-4">
								<img 
									src={deal.image_url} 
									alt={deal.title}
									class="w-full h-48 object-cover rounded-xl"
									loading="lazy"
								/>
								{deal.is_exclusive && (
									<div class="absolute top-3 left-3">
										<span class="bg-purple-500 text-white px-3 py-1 rounded-full text-sm font-semibold">
											EXCLUSIVE
										</span>
									</div>
								)}
								<div class="absolute top-3 right-3">
									<span class="bg-black/70 text-white px-2 py-1 rounded-lg text-sm font-bold">
										{formatDiscount(deal)}
									</span>
								</div>
							</div>
							
							<div class="space-y-3">
								<div class="flex items-center space-x-2 mb-2">
									<img 
										src={deal.brand?.logo_url || '/images/placeholder-brand.png'} 
										alt={deal.brand?.name}
										class="w-6 h-6 rounded"
									/>
									<span class="text-sm text-neutral-400">{deal.brand?.name}</span>
								</div>
								
								<h3 class="font-semibold text-white line-clamp-2 group-hover:text-primary-400 transition-colors">
									{deal.title}
								</h3>
								
								<div class="flex items-center space-x-2">
									<span class="text-xl font-bold text-primary-400">
										{formatPrice(deal.sale_price)}
									</span>
									{deal.original_price && (
										<span class="text-neutral-500 line-through text-sm">
											{formatPrice(deal.original_price)}
										</span>
									)}
								</div>
								
								<button class="w-full btn-secondary py-2 rounded-lg font-semibold hover:scale-105 transition-transform">
									View Deal
								</button>
							</div>
						</div>
					))}
				</div>
			</div>
		</section>
	)}

	<!-- All Deals Section -->
	{deals.length > 0 && (
		<section class="py-16">
			<div class="container mx-auto px-4">
				<div class="flex items-center justify-between mb-8">
					<h2 class="text-3xl font-display font-bold text-white">
						All Deals
					</h2>
					<div class="flex items-center space-x-4">
						<select class="bg-white/10 border border-white/20 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-primary-500">
							<option value="newest">Newest First</option>
							<option value="discount">Highest Discount</option>
							<option value="popular">Most Popular</option>
							<option value="ending">Ending Soon</option>
						</select>
					</div>
				</div>
				
				<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
					{deals.map((deal) => (
						<div class="card p-6 rounded-2xl group hover:scale-105 transition-all duration-300">
							<div class="relative mb-4">
								<img 
									src={deal.image_url} 
									alt={deal.title}
									class="w-full h-48 object-cover rounded-xl"
									loading="lazy"
								/>
								<div class="absolute top-3 right-3">
									<span class="bg-black/70 text-white px-2 py-1 rounded-lg text-sm font-bold">
										{formatDiscount(deal)}
									</span>
								</div>
							</div>
							
							<div class="space-y-3">
								<div class="flex items-center space-x-2 mb-2">
									<img 
										src={deal.brand?.logo_url || '/images/placeholder-brand.png'} 
										alt={deal.brand?.name}
										class="w-5 h-5 rounded"
									/>
									<span class="text-xs text-neutral-400">{deal.brand?.name}</span>
								</div>
								
								<h3 class="font-semibold text-white line-clamp-2 group-hover:text-primary-400 transition-colors text-sm">
									{deal.title}
								</h3>
								
								<div class="flex items-center space-x-2">
									<span class="text-lg font-bold text-primary-400">
										{formatPrice(deal.sale_price)}
									</span>
									{deal.original_price && (
										<span class="text-neutral-500 line-through text-sm">
											{formatPrice(deal.original_price)}
										</span>
									)}
								</div>
								
								<button class="w-full btn-secondary py-2 rounded-lg font-semibold text-sm hover:scale-105 transition-transform">
									Get Deal
								</button>
							</div>
						</div>
					))}
				</div>
				
				<!-- Load More Button -->
				<div class="text-center mt-12">
					<button class="btn-primary px-8 py-3 rounded-xl font-semibold hover:scale-105 transition-transform">
						Load More Deals
					</button>
				</div>
			</div>
		</section>
	)}

	{error && (
		<section class="py-16">
			<div class="container mx-auto px-4 text-center">
				<div class="card p-8 rounded-2xl max-w-md mx-auto">
					<div class="text-red-400 mb-4">
						<svg class="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
						</svg>
					</div>
					<h3 class="text-xl font-semibold text-white mb-2">Unable to Load Deals</h3>
					<p class="text-neutral-400 mb-4">{error}</p>
					<button class="btn-primary px-6 py-2 rounded-lg" onclick="window.location.reload()">
						Try Again
					</button>
				</div>
			</div>
		</section>
	)}
</Layout>

<script>
	// Add interactive functionality
	document.addEventListener('DOMContentLoaded', function() {
		// Search functionality
		const searchInput = document.querySelector('input[type="text"]');
		if (searchInput) {
			searchInput.addEventListener('keypress', function(e) {
				if (e.key === 'Enter') {
					const query = this.value.trim();
					if (query) {
						window.location.href = `/search?q=${encodeURIComponent(query)}`;
					}
				}
			});
		}

		// Deal click tracking
		const dealButtons = document.querySelectorAll('button:contains("Get Deal"), button:contains("View Deal")');
		dealButtons.forEach(button => {
			button.addEventListener('click', function() {
				// Add click tracking logic here
				console.log('Deal clicked');
			});
		});
	});
</script>
