---
import Header from '../components/layout/Header.astro';
import Footer from '../components/layout/Footer.astro';

export interface Props {
	title: string;
	description?: string;
	keywords?: string;
	image?: string;
	canonical?: string;
}

const { 
	title, 
	description = "Discover the latest coupons, deals, and discounts from top brands across Europe and America. Save money on your favorite products with verified promo codes.",
	keywords = "coupons, deals, discounts, promo codes, savings, Europe, America, brands, shopping",
	image = "/images/og-image.jpg",
	canonical = Astro.url.href
} = Astro.props;

const siteTitle = title.includes('MaxCoupon') ? title : `${title} | MaxCoupon`;
---

<!DOCTYPE html>
<html lang="en" class="scroll-smooth">
	<head>
		<meta charset="UTF-8" />
		<meta name="description" content={description} />
		<meta name="keywords" content={keywords} />
		<meta name="viewport" content="width=device-width, initial-scale=1.0" />
		<meta name="generator" content={Astro.generator} />
		
		<!-- Canonical URL -->
		<link rel="canonical" href={canonical} />
		
		<!-- Favicon -->
		<link rel="icon" type="image/svg+xml" href="/favicon.svg" />
		<link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png" />
		<link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png" />
		<link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
		<link rel="manifest" href="/site.webmanifest" />
		
		<!-- Open Graph / Facebook -->
		<meta property="og:type" content="website" />
		<meta property="og:url" content={canonical} />
		<meta property="og:title" content={siteTitle} />
		<meta property="og:description" content={description} />
		<meta property="og:image" content={image} />
		<meta property="og:site_name" content="MaxCoupon" />
		
		<!-- Twitter -->
		<meta property="twitter:card" content="summary_large_image" />
		<meta property="twitter:url" content={canonical} />
		<meta property="twitter:title" content={siteTitle} />
		<meta property="twitter:description" content={description} />
		<meta property="twitter:image" content={image} />
		
		<!-- Additional SEO -->
		<meta name="robots" content="index, follow" />
		<meta name="author" content="MaxCoupon" />
		<meta name="theme-color" content="#3B82F6" />
		
		<!-- Preconnect to external domains -->
		<link rel="preconnect" href="https://fonts.googleapis.com" />
		<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
		
		<!-- Google Fonts -->
		<link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet" />

		<!-- Structured Data -->
		<script type="application/ld+json">
			{
				"@context": "https://schema.org",
				"@type": "WebSite",
				"name": "MaxCoupon",
				"description": "Best deals and coupons for Europe and America",
				"url": "https://maxcoupon.com",
				"potentialAction": {
					"@type": "SearchAction",
					"target": "https://maxcoupon.com/search?q={search_term_string}",
					"query-input": "required name=search_term_string"
				}
			}
		</script>

		<title>{siteTitle}</title>
	</head>
	<body class="min-h-screen bg-white text-gray-800 antialiased">
		<!-- Subtle Background Pattern -->
		<div class="fixed inset-0 overflow-hidden pointer-events-none opacity-30">
			<div class="absolute top-0 left-0 w-full h-full" style="background-image: radial-gradient(circle at 25% 25%, rgba(168, 85, 247, 0.05) 0%, transparent 50%), radial-gradient(circle at 75% 75%, rgba(168, 85, 247, 0.03) 0%, transparent 50%);"></div>
		</div>

		<div class="flex flex-col min-h-screen relative z-10">
			<Header />

			<main class="flex-1">
				<slot />
			</main>

			<Footer />
		</div>
		
		<!-- Analytics Scripts -->
		<script>
			// Google Analytics
			if (typeof gtag !== 'undefined') {
				gtag('config', 'GA_MEASUREMENT_ID');
			}
			
			// Performance monitoring
			if ('performance' in window) {
				window.addEventListener('load', () => {
					const perfData = performance.getEntriesByType('navigation')[0];
					console.log('Page load time:', perfData.loadEventEnd - perfData.loadEventStart);
				});
			}
		</script>
		
		<!-- Service Worker Registration -->
		<script>
			if ('serviceWorker' in navigator) {
				window.addEventListener('load', () => {
					navigator.serviceWorker.register('/sw.js')
						.then((registration) => {
							console.log('SW registered: ', registration);
						})
						.catch((registrationError) => {
							console.log('SW registration failed: ', registrationError);
						});
				});
			}
		</script>
	</body>
</html>

<style is:global>
	/* CSS Variables for Purple Theme */
	:root {
		--purple-50: #faf5ff;
		--purple-100: #f3e8ff;
		--purple-200: #e9d5ff;
		--purple-300: #d8b4fe;
		--purple-400: #c084fc;
		--purple-500: #a855f7;
		--purple-600: #9333ea;
		--purple-700: #7c3aed;
		--purple-800: #6b21a8;
		--purple-900: #581c87;

		--gray-50: #fafafa;
		--gray-100: #f5f5f5;
		--gray-200: #e5e5e5;
		--gray-300: #d4d4d4;
		--gray-400: #a3a3a3;
		--gray-500: #737373;
		--gray-600: #525252;
		--gray-700: #404040;
		--gray-800: #262626;
		--gray-900: #171717;
	}

	html {
		font-family: 'Inter', system-ui, sans-serif;
	}

	/* Typography */
	h1, h2, h3, h4, h5, h6 {
		font-weight: 600;
		line-height: 1.2;
		color: var(--gray-900);
		letter-spacing: -0.025em;
	}

	/* Custom scrollbar - Minimal */
	::-webkit-scrollbar {
		width: 6px;
	}

	::-webkit-scrollbar-track {
		background: var(--gray-100);
	}

	::-webkit-scrollbar-thumb {
		background: var(--purple-300);
		border-radius: 3px;
	}

	::-webkit-scrollbar-thumb:hover {
		background: var(--purple-400);
	}

	/* Focus styles */
	.focus-ring {
		@apply focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2;
	}

	/* Animation utilities */
	@keyframes fadeIn {
		from { opacity: 0; }
		to { opacity: 1; }
	}

	@keyframes slideUp {
		from {
			opacity: 0;
			transform: translateY(20px);
		}
		to {
			opacity: 1;
			transform: translateY(0);
		}
	}

	.animate-fade-in {
		animation: fadeIn 0.3s ease-out;
	}

	.animate-slide-up {
		animation: slideUp 0.4s ease-out;
	}

	/* Loading skeleton */
	.skeleton {
		@apply animate-pulse bg-gray-200 rounded;
	}

	/* Button variants - Minimalist */
	.btn-primary {
		background-color: var(--purple-600);
		color: white;
		border: 1px solid var(--purple-600);
		@apply font-medium py-3 px-6 rounded-lg transition-all duration-200 focus-ring;
	}

	.btn-primary:hover {
		background-color: var(--purple-700);
		border-color: var(--purple-700);
		transform: translateY(-1px);
	}

	.btn-secondary {
		background-color: white;
		color: var(--purple-600);
		border: 1px solid var(--purple-200);
		@apply font-medium py-3 px-6 rounded-lg transition-all duration-200 focus-ring;
	}

	.btn-secondary:hover {
		background-color: var(--purple-50);
		border-color: var(--purple-300);
	}

	.btn-outline {
		background-color: transparent;
		color: var(--purple-600);
		border: 1px solid var(--purple-300);
		@apply font-medium py-3 px-6 rounded-lg transition-all duration-200 focus-ring;
	}

	.btn-outline:hover {
		background-color: var(--purple-600);
		color: white;
		border-color: var(--purple-600);
	}

	/* Card styles - Clean & Minimal */
	.card {
		background: white;
		border: 1px solid var(--gray-200);
		box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
		@apply rounded-xl overflow-hidden transition-all duration-200;
	}

	.card:hover {
		border-color: var(--purple-200);
		box-shadow: 0 4px 12px rgba(168, 85, 247, 0.08);
		transform: translateY(-2px);
	}

	.card-minimal {
		background: white;
		border: 1px solid var(--gray-100);
		@apply rounded-lg overflow-hidden transition-all duration-150;
	}

	.card-minimal:hover {
		border-color: var(--purple-200);
		box-shadow: 0 2px 8px rgba(168, 85, 247, 0.06);
	}

	/* Badge styles - Minimal */
	.badge {
		@apply inline-flex items-center px-3 py-1 rounded-full text-xs font-medium;
	}

	.badge-primary {
		background-color: var(--purple-100);
		color: var(--purple-800);
	}

	.badge-secondary {
		background-color: var(--gray-100);
		color: var(--gray-700);
	}

	.badge-success {
		background-color: #dcfce7;
		color: #166534;
	}

	.badge-warning {
		background-color: #fef3c7;
		color: #92400e;
	}

	/* Text utilities */
	.text-gradient {
		background: linear-gradient(135deg, var(--purple-600), var(--purple-400));
		-webkit-background-clip: text;
		-webkit-text-fill-color: transparent;
		background-clip: text;
	}

	/* Spacing utilities */
	.space-y-1 > * + * { margin-top: 0.25rem; }
	.space-y-2 > * + * { margin-top: 0.5rem; }
	.space-y-3 > * + * { margin-top: 0.75rem; }
	.space-y-4 > * + * { margin-top: 1rem; }
	.space-y-6 > * + * { margin-top: 1.5rem; }
	.space-y-8 > * + * { margin-top: 2rem; }
</style>
