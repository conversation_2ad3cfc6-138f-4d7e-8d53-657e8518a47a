---
import Header from '../components/layout/Header.astro';
import Footer from '../components/layout/Footer.astro';

export interface Props {
	title: string;
	description?: string;
	keywords?: string;
	image?: string;
	canonical?: string;
}

const { 
	title, 
	description = "Discover the latest coupons, deals, and discounts from top brands across Europe and America. Save money on your favorite products with verified promo codes.",
	keywords = "coupons, deals, discounts, promo codes, savings, Europe, America, brands, shopping",
	image = "/images/og-image.jpg",
	canonical = Astro.url.href
} = Astro.props;

const siteTitle = title.includes('MaxCoupon') ? title : `${title} | MaxCoupon`;
---

<!DOCTYPE html>
<html lang="en" class="scroll-smooth">
	<head>
		<meta charset="UTF-8" />
		<meta name="description" content={description} />
		<meta name="keywords" content={keywords} />
		<meta name="viewport" content="width=device-width, initial-scale=1.0" />
		<meta name="generator" content={Astro.generator} />
		
		<!-- Canonical URL -->
		<link rel="canonical" href={canonical} />
		
		<!-- Favicon -->
		<link rel="icon" type="image/svg+xml" href="/favicon.svg" />
		<link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png" />
		<link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png" />
		<link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
		<link rel="manifest" href="/site.webmanifest" />
		
		<!-- Open Graph / Facebook -->
		<meta property="og:type" content="website" />
		<meta property="og:url" content={canonical} />
		<meta property="og:title" content={siteTitle} />
		<meta property="og:description" content={description} />
		<meta property="og:image" content={image} />
		<meta property="og:site_name" content="MaxCoupon" />
		
		<!-- Twitter -->
		<meta property="twitter:card" content="summary_large_image" />
		<meta property="twitter:url" content={canonical} />
		<meta property="twitter:title" content={siteTitle} />
		<meta property="twitter:description" content={description} />
		<meta property="twitter:image" content={image} />
		
		<!-- Additional SEO -->
		<meta name="robots" content="index, follow" />
		<meta name="author" content="MaxCoupon" />
		<meta name="theme-color" content="#3B82F6" />
		
		<!-- Preconnect to external domains -->
		<link rel="preconnect" href="https://fonts.googleapis.com" />
		<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
		
		<!-- Google Fonts -->
		<link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700;800&display=swap" rel="stylesheet" />
		
		<!-- Structured Data -->
		<script type="application/ld+json">
			{
				"@context": "https://schema.org",
				"@type": "WebSite",
				"name": "MaxCoupon",
				"description": "Best deals and coupons for Europe and America",
				"url": "https://maxcoupon.com",
				"potentialAction": {
					"@type": "SearchAction",
					"target": "https://maxcoupon.com/search?q={search_term_string}",
					"query-input": "required name=search_term_string"
				}
			}
		</script>
		
		<title>{siteTitle}</title>
	</head>
	<body class="min-h-screen bg-gradient-to-br from-neutral-900 via-neutral-800 to-neutral-900 text-white antialiased overflow-x-hidden">
		<!-- Background Elements -->
		<div class="fixed inset-0 overflow-hidden pointer-events-none">
			<div class="absolute top-1/4 left-1/4 w-96 h-96 bg-primary-500/20 rounded-full blur-3xl floating-element"></div>
			<div class="absolute top-3/4 right-1/4 w-80 h-80 bg-secondary-500/20 rounded-full blur-3xl floating-element" style="animation-delay: -2s;"></div>
			<div class="absolute top-1/2 left-1/2 w-64 h-64 bg-accent-500/20 rounded-full blur-3xl floating-element" style="animation-delay: -4s;"></div>
		</div>

		<div class="flex flex-col min-h-screen relative z-10">
			<Header />

			<main class="flex-1">
				<slot />
			</main>

			<Footer />
		</div>
		
		<!-- Analytics Scripts -->
		<script>
			// Google Analytics
			if (typeof gtag !== 'undefined') {
				gtag('config', 'GA_MEASUREMENT_ID');
			}
			
			// Performance monitoring
			if ('performance' in window) {
				window.addEventListener('load', () => {
					const perfData = performance.getEntriesByType('navigation')[0];
					console.log('Page load time:', perfData.loadEventEnd - perfData.loadEventStart);
				});
			}
		</script>
		
		<!-- Service Worker Registration -->
		<script>
			if ('serviceWorker' in navigator) {
				window.addEventListener('load', () => {
					navigator.serviceWorker.register('/sw.js')
						.then((registration) => {
							console.log('SW registered: ', registration);
						})
						.catch((registrationError) => {
							console.log('SW registration failed: ', registrationError);
						});
				});
			}
		</script>
	</body>
</html>

<style is:global>
	html {
		font-family: 'Inter', system-ui, sans-serif;
	}

	.font-display {
		font-family: 'Poppins', system-ui, sans-serif;
	}

	/* Floating animation */
	.floating-element {
		animation: float 6s ease-in-out infinite;
	}

	@keyframes float {
		0%, 100% { transform: translateY(0px); }
		50% { transform: translateY(-20px); }
	}

	/* Custom scrollbar */
	::-webkit-scrollbar {
		width: 8px;
	}

	::-webkit-scrollbar-track {
		background: #262626;
	}

	::-webkit-scrollbar-thumb {
		background: #525252;
		border-radius: 4px;
	}

	::-webkit-scrollbar-thumb:hover {
		background: #737373;
	}
	
	/* Focus styles */
	.focus-ring {
		@apply focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2;
	}
	
	/* Animation utilities */
	.animate-fade-in {
		animation: fadeIn 0.5s ease-in-out;
	}
	
	.animate-slide-up {
		animation: slideUp 0.3s ease-out;
	}
	
	/* Loading skeleton */
	.skeleton {
		@apply animate-pulse bg-gray-200 rounded;
	}
	
	/* Button variants */
	.btn-primary {
		background: linear-gradient(135deg, #d946ef, #c026d3);
		box-shadow: 0 10px 25px rgba(217, 70, 239, 0.3);
		@apply text-white font-medium py-3 px-6 rounded-xl transition-all duration-300 focus-ring;
	}

	.btn-primary:hover {
		transform: translateY(-2px);
		box-shadow: 0 15px 35px rgba(217, 70, 239, 0.4);
	}

	.btn-secondary {
		@apply bg-neutral-700 hover:bg-neutral-600 text-white font-medium py-3 px-6 rounded-xl transition-colors duration-200 focus-ring;
	}

	.btn-outline {
		background: rgba(255, 255, 255, 0.1);
		backdrop-filter: blur(20px);
		border: 1px solid rgba(255, 255, 255, 0.2);
		@apply text-white hover:border-primary-400/50 font-medium py-3 px-6 rounded-xl transition-all duration-300 focus-ring;
	}

	/* Card styles */
	.card {
		background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
		backdrop-filter: blur(20px);
		border: 1px solid rgba(255, 255, 255, 0.1);
		box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
		@apply rounded-2xl overflow-hidden;
	}

	.card-hover {
		@apply hover:scale-105 hover:-translate-y-2 transition-all duration-500;
	}

	.glass-effect {
		background: rgba(255, 255, 255, 0.1);
		backdrop-filter: blur(20px);
		border: 1px solid rgba(255, 255, 255, 0.2);
	}
	
	/* Badge styles */
	.badge {
		@apply inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold;
	}

	.badge-primary {
		background: linear-gradient(135deg, #d946ef, #c026d3);
		@apply text-white;
	}

	.badge-secondary {
		background: linear-gradient(135deg, #f97316, #ea580c);
		@apply text-white;
	}

	.badge-accent {
		background: linear-gradient(135deg, #0ea5e9, #0284c7);
		@apply text-white;
	}

	/* Text utilities */
	.text-gradient {
		background: linear-gradient(135deg, #d946ef, #f97316);
		-webkit-background-clip: text;
		-webkit-text-fill-color: transparent;
		background-clip: text;
	}
</style>
