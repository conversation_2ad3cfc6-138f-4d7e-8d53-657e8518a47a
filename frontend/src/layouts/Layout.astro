---
import Header from '../components/layout/Header.astro';
import Footer from '../components/layout/Footer.astro';

export interface Props {
	title: string;
	description?: string;
	keywords?: string;
	image?: string;
	canonical?: string;
}

const { 
	title, 
	description = "Discover the latest coupons, deals, and discounts from top brands across Europe and America. Save money on your favorite products with verified promo codes.",
	keywords = "coupons, deals, discounts, promo codes, savings, Europe, America, brands, shopping",
	image = "/images/og-image.jpg",
	canonical = Astro.url.href
} = Astro.props;

const siteTitle = title.includes('MaxCoupon') ? title : `${title} | MaxCoupon`;
---

<!DOCTYPE html>
<html lang="en" class="scroll-smooth">
	<head>
		<meta charset="UTF-8" />
		<meta name="description" content={description} />
		<meta name="keywords" content={keywords} />
		<meta name="viewport" content="width=device-width, initial-scale=1.0" />
		<meta name="generator" content={Astro.generator} />
		
		<!-- Canonical URL -->
		<link rel="canonical" href={canonical} />
		
		<!-- Favicon -->
		<link rel="icon" type="image/svg+xml" href="/favicon.svg" />
		<link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png" />
		<link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png" />
		<link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
		<link rel="manifest" href="/site.webmanifest" />
		
		<!-- Open Graph / Facebook -->
		<meta property="og:type" content="website" />
		<meta property="og:url" content={canonical} />
		<meta property="og:title" content={siteTitle} />
		<meta property="og:description" content={description} />
		<meta property="og:image" content={image} />
		<meta property="og:site_name" content="MaxCoupon" />
		
		<!-- Twitter -->
		<meta property="twitter:card" content="summary_large_image" />
		<meta property="twitter:url" content={canonical} />
		<meta property="twitter:title" content={siteTitle} />
		<meta property="twitter:description" content={description} />
		<meta property="twitter:image" content={image} />
		
		<!-- Additional SEO -->
		<meta name="robots" content="index, follow" />
		<meta name="author" content="MaxCoupon" />
		<meta name="theme-color" content="#3B82F6" />
		
		<!-- Preconnect to external domains -->
		<link rel="preconnect" href="https://fonts.googleapis.com" />
		<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
		
		<!-- Google Fonts -->
		<link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700;800&display=swap" rel="stylesheet" />
		
		<!-- Structured Data -->
		<script type="application/ld+json">
			{
				"@context": "https://schema.org",
				"@type": "WebSite",
				"name": "MaxCoupon",
				"description": "Best deals and coupons for Europe and America",
				"url": "https://maxcoupon.com",
				"potentialAction": {
					"@type": "SearchAction",
					"target": "https://maxcoupon.com/search?q={search_term_string}",
					"query-input": "required name=search_term_string"
				}
			}
		</script>
		
		<title>{siteTitle}</title>
	</head>
	<body class="min-h-screen bg-gray-50 text-gray-900 antialiased">
		<div class="flex flex-col min-h-screen">
			<Header />
			
			<main class="flex-1">
				<slot />
			</main>
			
			<Footer />
		</div>
		
		<!-- Analytics Scripts -->
		<script>
			// Google Analytics
			if (typeof gtag !== 'undefined') {
				gtag('config', 'GA_MEASUREMENT_ID');
			}
			
			// Performance monitoring
			if ('performance' in window) {
				window.addEventListener('load', () => {
					const perfData = performance.getEntriesByType('navigation')[0];
					console.log('Page load time:', perfData.loadEventEnd - perfData.loadEventStart);
				});
			}
		</script>
		
		<!-- Service Worker Registration -->
		<script>
			if ('serviceWorker' in navigator) {
				window.addEventListener('load', () => {
					navigator.serviceWorker.register('/sw.js')
						.then((registration) => {
							console.log('SW registered: ', registration);
						})
						.catch((registrationError) => {
							console.log('SW registration failed: ', registrationError);
						});
				});
			}
		</script>
	</body>
</html>

<style is:global>
	html {
		font-family: 'Inter', system-ui, sans-serif;
	}
	
	.font-display {
		font-family: 'Poppins', system-ui, sans-serif;
	}
	
	/* Custom scrollbar */
	::-webkit-scrollbar {
		width: 8px;
	}
	
	::-webkit-scrollbar-track {
		background: #f1f5f9;
	}
	
	::-webkit-scrollbar-thumb {
		background: #cbd5e1;
		border-radius: 4px;
	}
	
	::-webkit-scrollbar-thumb:hover {
		background: #94a3b8;
	}
	
	/* Focus styles */
	.focus-ring {
		@apply focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2;
	}
	
	/* Animation utilities */
	.animate-fade-in {
		animation: fadeIn 0.5s ease-in-out;
	}
	
	.animate-slide-up {
		animation: slideUp 0.3s ease-out;
	}
	
	/* Loading skeleton */
	.skeleton {
		@apply animate-pulse bg-gray-200 rounded;
	}
	
	/* Button variants */
	.btn-primary {
		@apply bg-primary-600 hover:bg-primary-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200 focus-ring;
	}
	
	.btn-secondary {
		@apply bg-gray-200 hover:bg-gray-300 text-gray-900 font-medium py-2 px-4 rounded-lg transition-colors duration-200 focus-ring;
	}
	
	.btn-outline {
		@apply border border-primary-600 text-primary-600 hover:bg-primary-600 hover:text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200 focus-ring;
	}
	
	/* Card styles */
	.card {
		@apply bg-white rounded-xl shadow-soft border border-gray-100 overflow-hidden;
	}
	
	.card-hover {
		@apply hover:shadow-medium hover:-translate-y-1 transition-all duration-300;
	}
	
	/* Badge styles */
	.badge {
		@apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
	}
	
	.badge-primary {
		@apply bg-primary-100 text-primary-800;
	}
	
	.badge-secondary {
		@apply bg-secondary-100 text-secondary-800;
	}
	
	.badge-accent {
		@apply bg-accent-100 text-accent-800;
	}
	
	/* Text utilities */
	.text-gradient {
		@apply bg-gradient-to-r from-primary-600 to-secondary-600 bg-clip-text text-transparent;
	}
</style>
