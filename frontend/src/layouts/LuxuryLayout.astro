---
export interface Props {
	title: string;
	description?: string;
	keywords?: string;
	ogImage?: string;
	canonicalURL?: string;
}

const { 
	title, 
	description = "Discover premium deals and exclusive coupons from the world's most prestigious brands. Save more with MaxCoupon's curated luxury offers.",
	keywords = "luxury coupons, premium deals, exclusive offers, high-end discounts, designer brands, luxury shopping",
	ogImage = "/images/og-image.jpg",
	canonicalURL = Astro.url.href
} = Astro.props;

import Header from '../components/layout/Header.astro';
import Footer from '../components/layout/Footer.astro';
---

<!doctype html>
<html lang="en" class="scroll-smooth">
	<head>
		<meta charset="UTF-8" />
		<meta name="description" content={description} />
		<meta name="keywords" content={keywords} />
		<meta name="viewport" content="width=device-width, initial-scale=1.0" />
		<link rel="icon" type="image/svg+xml" href="/favicon.svg" />
		<meta name="generator" content={Astro.generator} />
		
		<!-- SEO Meta Tags -->
		<title>{title}</title>
		<meta name="description" content={description} />
		<link rel="canonical" href={canonicalURL} />
		
		<!-- Open Graph / Facebook -->
		<meta property="og:type" content="website" />
		<meta property="og:url" content={canonicalURL} />
		<meta property="og:title" content={title} />
		<meta property="og:description" content={description} />
		<meta property="og:image" content={ogImage} />
		
		<!-- Twitter -->
		<meta property="twitter:card" content="summary_large_image" />
		<meta property="twitter:url" content={canonicalURL} />
		<meta property="twitter:title" content={title} />
		<meta property="twitter:description" content={description} />
		<meta property="twitter:image" content={ogImage} />
		
		<!-- Fonts -->
		<link rel="preconnect" href="https://fonts.googleapis.com">
		<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
		<link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700;800&display=swap" rel="stylesheet">
		
		<!-- Preload critical resources -->
		<link rel="preload" href="/fonts/inter-var.woff2" as="font" type="font/woff2" crossorigin>
		<link rel="preload" href="/fonts/poppins-var.woff2" as="font" type="font/woff2" crossorigin>
	</head>
	
	<body class="min-h-screen bg-gradient-to-br from-neutral-50 via-white to-primary-50/30 text-neutral-900 antialiased overflow-x-hidden">
		<!-- Background Pattern -->
		<div class="fixed inset-0 overflow-hidden pointer-events-none">
			<!-- Luxury gradient background -->
			<div class="absolute inset-0 bg-gradient-to-br from-primary-50/50 via-white to-secondary-50/30"></div>
			
			<!-- Floating orbs -->
			<div class="absolute top-1/4 left-1/4 w-96 h-96 bg-gradient-to-r from-primary-400/20 to-secondary-400/20 rounded-full blur-3xl floating-element"></div>
			<div class="absolute top-3/4 right-1/4 w-80 h-80 bg-gradient-to-r from-secondary-400/20 to-accent-400/20 rounded-full blur-3xl floating-element" style="animation-delay: -2s;"></div>
			<div class="absolute top-1/2 left-1/2 w-64 h-64 bg-gradient-to-r from-accent-400/20 to-primary-400/20 rounded-full blur-3xl floating-element" style="animation-delay: -4s;"></div>
			
			<!-- Subtle pattern overlay -->
			<div class="absolute inset-0 opacity-[0.02]" style="background-image: radial-gradient(circle at 1px 1px, rgba(168,85,247,0.3) 1px, transparent 0); background-size: 20px 20px;"></div>
		</div>
		
		<!-- Main Content -->
		<div class="relative z-10 flex flex-col min-h-screen">
			<!-- Header -->
			<Header />
			
			<!-- Main Content Area -->
			<main class="flex-1">
				<slot />
			</main>
			
			<!-- Footer -->
			<Footer />
		</div>
		
		<!-- Global Styles -->
		<style is:global>
			/* Font Face Declarations */
			html {
				font-family: 'Inter', system-ui, sans-serif;
				scroll-behavior: smooth;
			}
			
			.font-display {
				font-family: 'Poppins', system-ui, sans-serif;
			}
			
			/* Custom Scrollbar */
			::-webkit-scrollbar {
				width: 8px;
			}
			
			::-webkit-scrollbar-track {
				background: #f1f5f9;
			}
			
			::-webkit-scrollbar-thumb {
				background: linear-gradient(135deg, #a855f7, #f59e0b);
				border-radius: 4px;
			}
			
			::-webkit-scrollbar-thumb:hover {
				background: linear-gradient(135deg, #9333ea, #d97706);
			}
			
			/* Selection */
			::selection {
				background: rgba(168, 85, 247, 0.2);
				color: #3b0764;
			}
			
			/* Focus Ring */
			.focus-ring {
				@apply focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2;
			}
			
			/* Button Variants */
			.btn-primary {
				@apply inline-flex items-center justify-center px-6 py-3 bg-gradient-to-r from-primary-600 to-primary-700 hover:from-primary-700 hover:to-primary-800 text-white font-medium rounded-xl shadow-lg hover:shadow-xl hover:-translate-y-0.5 transition-all duration-300 focus-ring;
			}
			
			.btn-secondary {
				@apply inline-flex items-center justify-center px-6 py-3 bg-gradient-to-r from-secondary-500 to-secondary-600 hover:from-secondary-600 hover:to-secondary-700 text-white font-medium rounded-xl shadow-lg hover:shadow-xl hover:-translate-y-0.5 transition-all duration-300 focus-ring;
			}
			
			.btn-outline {
				@apply inline-flex items-center justify-center px-6 py-3 border-2 border-primary-600 text-primary-600 hover:bg-primary-600 hover:text-white font-medium rounded-xl transition-all duration-300 focus-ring;
			}
			
			.btn-ghost {
				@apply inline-flex items-center justify-center px-6 py-3 text-primary-600 hover:bg-primary-50 font-medium rounded-xl transition-all duration-300 focus-ring;
			}
			
			.btn-luxury {
				@apply inline-flex items-center justify-center px-8 py-4 bg-gradient-to-r from-primary-600 via-secondary-500 to-accent-600 text-white font-semibold rounded-2xl shadow-luxury hover:shadow-glow-lg hover:-translate-y-1 transition-all duration-300 focus-ring;
			}
			
			/* Card Variants */
			.card {
				@apply bg-white rounded-2xl shadow-lg border border-neutral-200 overflow-hidden;
			}
			
			.card-hover {
				@apply hover:shadow-xl hover:-translate-y-1 transition-all duration-300;
			}
			
			.card-luxury {
				@apply bg-gradient-to-br from-white to-neutral-50 rounded-2xl shadow-luxury border border-neutral-100 overflow-hidden;
			}
			
			.card-glass {
				@apply bg-white/80 backdrop-blur-xl rounded-2xl shadow-2xl border border-white/20 overflow-hidden;
			}
			
			/* Input Styles */
			.input-luxury {
				@apply block w-full px-4 py-3 bg-white/90 backdrop-blur-sm border border-neutral-200 rounded-xl text-neutral-900 placeholder-neutral-500 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 shadow-sm transition-all duration-200;
			}
			
			/* Badge Styles */
			.badge {
				@apply inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold;
			}
			
			.badge-primary {
				@apply bg-gradient-to-r from-primary-500 to-primary-600 text-white;
			}
			
			.badge-secondary {
				@apply bg-gradient-to-r from-secondary-500 to-secondary-600 text-white;
			}
			
			.badge-accent {
				@apply bg-gradient-to-r from-accent-500 to-accent-600 text-white;
			}
			
			.badge-success {
				@apply bg-gradient-to-r from-success-500 to-success-600 text-white;
			}
			
			.badge-warning {
				@apply bg-gradient-to-r from-warning-500 to-warning-600 text-white;
			}
			
			.badge-error {
				@apply bg-gradient-to-r from-error-500 to-error-600 text-white;
			}
			
			/* Text Utilities */
			.text-gradient {
				background: linear-gradient(135deg, #a855f7, #f59e0b);
				-webkit-background-clip: text;
				-webkit-text-fill-color: transparent;
				background-clip: text;
			}
			
			/* Animation Classes */
			.animate-fade-in {
				animation: fadeIn 0.5s ease-out;
			}
			
			.animate-fade-in-up {
				animation: fadeInUp 0.6s ease-out;
			}
			
			.animate-slide-in-right {
				animation: slideInRight 0.5s ease-out;
			}
			
			.animate-scale-in {
				animation: scaleIn 0.3s ease-out;
			}
			
			/* Utility Classes */
			.container {
				@apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
			}
			
			.section-padding {
				@apply py-16 lg:py-24;
			}
			
			.text-balance {
				text-wrap: balance;
			}
			
			/* Loading States */
			.loading {
				@apply animate-pulse;
			}
			
			.skeleton {
				@apply bg-gradient-to-r from-neutral-200 via-neutral-100 to-neutral-200 bg-[length:200%_100%] animate-gradient;
			}
			
			/* Responsive Typography */
			.heading-xl {
				@apply text-4xl md:text-5xl lg:text-6xl font-display font-bold;
			}
			
			.heading-lg {
				@apply text-3xl md:text-4xl lg:text-5xl font-display font-bold;
			}
			
			.heading-md {
				@apply text-2xl md:text-3xl lg:text-4xl font-display font-bold;
			}
			
			.heading-sm {
				@apply text-xl md:text-2xl lg:text-3xl font-display font-bold;
			}
		</style>
		
		<!-- Analytics and Performance -->
		<script>
			// Performance monitoring
			if ('performance' in window) {
				window.addEventListener('load', () => {
					const perfData = performance.getEntriesByType('navigation')[0];
					console.log('Page load time:', perfData.loadEventEnd - perfData.loadEventStart, 'ms');
				});
			}
			
			// Intersection Observer for animations
			if ('IntersectionObserver' in window) {
				const observer = new IntersectionObserver((entries) => {
					entries.forEach((entry) => {
						if (entry.isIntersecting) {
							entry.target.classList.add('animate-fade-in-up');
						}
					});
				}, {
					threshold: 0.1,
					rootMargin: '0px 0px -50px 0px'
				});
				
				// Observe elements when DOM is ready
				document.addEventListener('DOMContentLoaded', () => {
					document.querySelectorAll('.card, .section-animate').forEach((el) => {
						observer.observe(el);
					});
				});
			}
		</script>
	</body>
</html>
