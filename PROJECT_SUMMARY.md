# MaxCoupon - Complete Coupon & Deals Platform

## 🎯 Project Overview

MaxCoupon is a comprehensive coupon and deals platform designed for European and American markets. The platform provides verified coupons, exclusive deals, and instant discounts from top brands across multiple categories.

## 🏗️ Architecture

### Technology Stack
- **Backend**: Go + Gin Framework + PostgreSQL + Redis
- **Frontend**: Astro + React + Tailwind CSS
- **Database**: PostgreSQL with advanced indexing
- **Cache**: Redis for high-performance caching
- **Deployment**: Docker + Docker Compose

### Key Features
- ✅ Multi-country support (US, CA, GB, DE, FR, SE)
- ✅ Advanced search with full-text capabilities
- ✅ Real-time analytics and tracking
- ✅ Responsive mobile-first design
- ✅ SEO-optimized architecture
- ✅ Admin dashboard for content management
- ✅ API-first design with comprehensive endpoints

## 📁 Project Structure

```
maxcoupon/
├── backend/                 # Go backend application
│   ├── cmd/                # Application entry points
│   ├── internal/           # Internal packages
│   │   ├── config/        # Configuration management
│   │   ├── database/      # Database connection & migrations
│   │   ├── handlers/      # HTTP request handlers
│   │   ├── middleware/    # HTTP middleware
│   │   ├── models/        # Data models
│   │   ├── repository/    # Data access layer
│   │   ├── services/      # Business logic
│   │   └── utils/         # Utility functions
│   ├── migrations/        # Database migrations
│   ├── seeds/            # Database seed data
│   └── docker/           # Docker configurations
├── frontend/              # Astro frontend application
│   ├── src/
│   │   ├── components/   # Reusable UI components
│   │   ├── layouts/      # Page layouts
│   │   ├── pages/        # Route pages
│   │   ├── lib/          # Utility libraries
│   │   └── styles/       # Global styles
│   └── public/           # Static assets
└── docker-compose.yml    # Development environment
```

## 🗄️ Database Schema

### Core Tables
- **countries**: Multi-country support
- **categories**: Hierarchical category system
- **brands**: Brand management with metadata
- **coupons**: Coupon codes with validation
- **deals**: Product deals and offers
- **analytics**: Comprehensive tracking system

### Key Features
- Advanced indexing for search performance
- Full-text search capabilities
- Hierarchical categories with parent-child relationships
- Comprehensive analytics tracking
- Multi-country localization support

## 🔧 Backend API

### Core Endpoints

#### Coupons API
- `GET /api/coupons` - List coupons with filtering
- `GET /api/coupons/{id}` - Get coupon details
- `GET /api/coupons/featured` - Featured coupons
- `POST /api/coupons/{id}/click` - Track coupon usage

#### Deals API
- `GET /api/deals` - List deals with filtering
- `GET /api/deals/{id}` - Get deal details
- `GET /api/deals/hot` - Hot deals

#### Search API
- `GET /api/search` - Universal search
- `GET /api/search/suggestions` - Search suggestions
- `GET /api/search/popular` - Popular searches

#### Brands & Categories
- `GET /api/brands` - List brands
- `GET /api/categories` - List categories
- `GET /api/brands/popular` - Popular brands

### Features
- Comprehensive filtering and sorting
- Pagination support
- Real-time analytics tracking
- Caching with Redis
- Rate limiting and security middleware

## 🎨 Frontend Components

### Core Components
- **Header**: Navigation with search functionality
- **Hero**: Landing page hero section
- **FeaturedCoupons**: Highlighted coupon cards
- **PopularBrands**: Brand showcase
- **Categories**: Category navigation
- **SearchBar**: Advanced search with suggestions
- **CouponCard**: Individual coupon display
- **FilterSidebar**: Advanced filtering options

### Pages
- **Homepage**: Main landing page
- **Coupons**: Coupon listing with filters
- **Search**: Search results page
- **Brand Pages**: Brand-specific content
- **Category Pages**: Category-specific content

### Design System
- Modern, clean design with Tailwind CSS
- Responsive mobile-first approach
- Consistent color scheme and typography
- Interactive hover effects and animations
- Accessibility-focused components

## 🚀 Development Setup

### Prerequisites
- Go 1.21+
- Node.js 18.20.8+
- PostgreSQL 15+
- Redis 7+
- Docker & Docker Compose

### Quick Start

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd maxcoupon
   ```

2. **Start development environment**
   ```bash
   docker-compose up -d
   ```

3. **Setup backend**
   ```bash
   cd backend
   cp .env.example .env
   go mod download
   go run cmd/migrate/main.go
   go run cmd/seed/main.go
   go run cmd/server/main.go
   ```

4. **Setup frontend**
   ```bash
   cd frontend
   cp .env.example .env
   npm install
   npm run dev
   ```

### Environment Variables

#### Backend (.env)
```env
DB_HOST=localhost
DB_PORT=5432
DB_USER=maxcoupon
DB_PASSWORD=password
DB_NAME=maxcoupon
REDIS_URL=redis://localhost:6379
JWT_SECRET=your-secret-key
PORT=8080
```

#### Frontend (.env)
```env
PUBLIC_API_URL=http://localhost:8080/api
PUBLIC_SITE_URL=http://localhost:4321
```

## 📊 Features Implemented

### ✅ Completed Features
- [x] Database schema and migrations
- [x] Complete backend API with all endpoints
- [x] Redis caching implementation
- [x] Frontend component library
- [x] Responsive design system
- [x] Search functionality with suggestions
- [x] Advanced filtering system
- [x] Analytics tracking
- [x] Multi-country support
- [x] SEO optimization
- [x] Docker development environment

### 🔄 In Progress
- [ ] User authentication system
- [ ] Admin dashboard
- [ ] Email notifications
- [ ] Social media integration

### 📋 Future Enhancements
- [ ] Mobile app (React Native)
- [ ] Advanced recommendation engine
- [ ] A/B testing framework
- [ ] Advanced analytics dashboard
- [ ] Multi-language support
- [ ] Payment integration for premium features

## 🔒 Security Features

- CORS middleware with configurable origins
- Rate limiting to prevent abuse
- Input validation and sanitization
- SQL injection prevention with parameterized queries
- XSS protection headers
- CSRF protection
- Secure session management

## 📈 Performance Optimizations

- Redis caching for frequently accessed data
- Database indexing for search performance
- Image optimization and lazy loading
- CDN-ready static asset structure
- Efficient pagination
- Query optimization with proper joins

## 🌍 Internationalization

- Multi-country support (US, CA, GB, DE, FR, SE)
- Currency localization
- Country-specific deals and coupons
- Localized content management
- SEO-friendly URL structure

## 📱 Mobile Responsiveness

- Mobile-first design approach
- Touch-friendly interface elements
- Optimized mobile navigation
- Fast loading on mobile networks
- Progressive Web App capabilities

## 🧪 Testing Strategy

- Unit tests for backend services
- Integration tests for API endpoints
- Frontend component testing
- End-to-end testing with Playwright
- Performance testing with load testing tools

## 📚 Documentation

- Comprehensive API documentation
- Component documentation with Storybook
- Database schema documentation
- Deployment guides
- Contributing guidelines

## 🚀 Deployment

### Production Deployment
- Docker containerization
- Kubernetes orchestration
- CI/CD pipeline with GitHub Actions
- Environment-specific configurations
- Health checks and monitoring
- Automated backups

### Monitoring & Analytics
- Application performance monitoring
- Error tracking and logging
- User behavior analytics
- Business metrics dashboard
- Real-time alerting system

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 📞 Support

For support and questions:
- Email: <EMAIL>
- Documentation: https://docs.maxcoupon.com
- Issues: GitHub Issues page

---

**MaxCoupon** - Saving money, one deal at a time! 💰
