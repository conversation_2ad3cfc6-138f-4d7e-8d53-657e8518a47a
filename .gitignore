# Environment variables
.env
.env.local
.env.production
.env.staging

# Go
*.exe
*.exe~
*.dll
*.so
*.dylib
*.test
*.out
go.work
backend/bin/
backend/tmp/
backend/vendor/

# Node.js
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*
frontend/dist/
frontend/.astro/
frontend/.output/
frontend/.vercel/
frontend/.netlify/

# IDEs and editors
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Dependency directories
jspm_packages/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# Next.js build output
.next

# Nuxt.js build / generate output
.nuxt

# Gatsby files
.cache/
public

# Storybook build outputs
.out
.storybook-out

# Temporary folders
tmp/
temp/

# Database
*.db
*.sqlite
*.sqlite3

# Docker
docker-compose.override.yml

# Backup files
*.backup
*.bak
*.sql

# SSL certificates
*.pem
*.key
*.crt
*.csr

# Uploads
uploads/
static/uploads/

# Build artifacts
build/
dist/
out/

# Test artifacts
test-results/
coverage/

# Monitoring
prometheus_data/
grafana_data/

# Local development
.local/
.cache/
