# MaxCoupon - European & American Coupon Website

A comprehensive coupon and deals website targeting European and American users, built with modern technologies for optimal performance and user experience.

## 🚀 Tech Stack

### Frontend
- **Astro** - Modern static site generator with component islands
- **TypeScript** - Type-safe development
- **Tailwind CSS** - Utility-first CSS framework
- **Alpine.js** - Lightweight JavaScript framework

### Backend
- **Go** - High-performance backend language
- **Gin** - Fast HTTP web framework
- **PostgreSQL** - Robust relational database
- **Redis** - In-memory caching and session storage

## 📁 Project Structure

```
maxcoupon/
├── frontend/                 # Astro frontend application
│   ├── src/
│   │   ├── components/      # Reusable UI components
│   │   ├── layouts/         # Page layouts
│   │   ├── pages/           # Route pages
│   │   ├── styles/          # Global styles
│   │   └── utils/           # Utility functions
│   ├── public/              # Static assets
│   └── package.json
├── backend/                 # Go backend application
│   ├── cmd/                 # Application entry points
│   ├── internal/            # Private application code
│   │   ├── api/            # HTTP handlers
│   │   ├── models/         # Data models
│   │   ├── services/       # Business logic
│   │   ├── repository/     # Data access layer
│   │   └── middleware/     # HTTP middleware
│   ├── migrations/          # Database migrations
│   ├── config/             # Configuration files
│   └── go.mod
├── docker/                  # Docker configuration
├── docs/                    # Documentation
└── scripts/                 # Build and deployment scripts
```

## 🗄️ Database Schema

### Core Tables
- **coupons** - Coupon codes and discounts
- **deals** - Special offers and sales
- **brands** - Retailer and brand information
- **categories** - Product/service categories
- **countries** - Geographic targeting

## 🔧 Development Setup

### Prerequisites
- Go 1.21+
- Node.js 18+
- PostgreSQL 14+
- Redis 6+
- Docker (optional)

### Quick Start

1. **Clone and setup**
   ```bash
   git clone <repository-url>
   cd maxcoupon
   ```

2. **Backend setup**
   ```bash
   cd backend
   go mod tidy
   cp config/config.example.yaml config/config.yaml
   # Edit config.yaml with your database credentials
   go run cmd/server/main.go
   ```

3. **Frontend setup**
   ```bash
   cd frontend
   npm install
   npm run dev
   ```

4. **Database setup**
   ```bash
   # Run migrations
   cd backend
   go run cmd/migrate/main.go up
   
   # Seed sample data
   go run cmd/seed/main.go
   ```

## 🌐 API Endpoints

### Coupons
- `GET /api/coupons` - List coupons with filtering
- `GET /api/coupons/:id` - Get coupon details

### Deals
- `GET /api/deals` - List deals with filtering
- `GET /api/deals/:id` - Get deal details

### Brands
- `GET /api/brands` - List brands
- `GET /api/brands/:id` - Get brand details

### Categories
- `GET /api/categories` - List categories (hierarchical)
- `GET /api/categories/:id` - Get category details

### Countries
- `GET /api/countries` - List countries
- `GET /api/countries/:id` - Get country details

### Search
- `GET /api/search` - Unified search across all entities

## 🚀 Deployment

### Using Docker
```bash
docker-compose up -d
```

### Manual Deployment
See `docs/deployment.md` for detailed instructions.

## 📝 License

This project is licensed under the MIT License - see the LICENSE file for details.
